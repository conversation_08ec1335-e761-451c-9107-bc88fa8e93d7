var __defProp = Object.defineProperty,
  __defNormalProp = (e, t, s) =>
    t in e
      ? __defProp(e, t, { enumerable: !0, configurable: !0, writable: !0, value: s })
      : (e[t] = s),
  __publicField = (e, t, s) => (__defNormalProp(e, 'symbol' != typeof t ? t + '' : t, s), s)
!(function (e, t) {
  'object' == typeof exports && 'undefined' != typeof module
    ? t(exports)
    : 'function' == typeof define && define.amd
      ? define(['exports'], t)
      : t(((e = 'undefined' != typeof globalThis ? globalThis : e || self).WSPlayer = {}))
})(this, function (e) {
  'use strict'
  const t = {
      websocketPorts: {
        realmonitor: 'realmonitor-websocket',
        playback: 'playback-websocket',
        realmonitor_ws: '9100',
        playback_ws: '9320',
        realmonitor_wss: '9102',
        playback_wss: '9322'
      },
      errorVideoInfo: {
        101: '当前浏览器不支持硬解码',
        102: '硬解码播放失败',
        103: '硬解码播放延时超过8秒',
        104: '硬解码失败',
        201: '当前音频无法播放',
        202: 'websocket连接错误',
        203: '文件播放完成',
        401: '该用户无操作权限',
        404: '请求超时或未找到',
        405: '播放超时',
        406: '视频流类型解析失败，请检查通道配置',
        407: '请求超时',
        408: '请求超时或码流不支持',
        409: '请求超时或码流不支持',
        410: '视频流异常断开',
        411: '录像播放完成',
        457: '时间设置错误',
        503: 'WS连接地址错误，非当前连接的ICC服务器返回',
        504: '对讲失败',
        701: 'Chrome版本低，请升级到最新的Chrome版本',
        702: 'Firefox版本低，请升级到最新的Firefox版本',
        703: 'Edge版本低，请升级到最新的Edge版本',
        defaultErrorMsg: '播放失败，请检查配置'
      },
      errorInfo: {
        101: '所选通道离线，无法播放',
        102: '登录权限过期，查询实时预览rtsp失败',
        103: '操作失败，请稍后重试',
        104: '操作失败，请稍后重试',
        105: '通道休眠，正在唤醒中，请稍后再试',
        106: '请传入rtsp地址和websocket地址',
        107: '当前未传records录像文件, 将无法拖动进度条',
        108: '请求超时，请稍后重试',
        201: '所选通道未查询到录像文件',
        202: '查询录像文件列表失败',
        203: '查询录像rtsp失败',
        204: '{0}倍速无法播放音频',
        205: '通道休眠，正在唤醒中，请稍后再试',
        206: '当前倍速不支持',
        301: '正在对讲，无法关闭音频',
        302: '其他设备对讲中，无法开启音频',
        303: '其他设备对讲中，无法开启对讲',
        304: '查询对讲rtsp失败',
        305: 'http协议不支持对讲',
        306: '设备对讲失败',
        307: '不支持PCM音频格式对讲',
        308: '不支持ARM音频格式对讲',
        309: '不支持G711u音频格式对讲',
        310: '不支持G726音频格式对讲',
        311: '不支持AAC音频格式对讲',
        312: '不支持G722音频格式对讲',
        501: '解码库未初始化完成，请稍后播放！',
        502: '解码库未初始化完成，请稍后对讲！',
        503: '请检查创建播放器时，播放器容器是否存在',
        601: '所操作播放器不存在',
        602: '所选播放器正在本地录像中，不可重复本地录像',
        603: '所选播放器未播放录像，不可本地录像',
        604: '所选播放器未开始本地录像，不可操作关闭本地录像',
        605: '时间跳转播放传参错误',
        606: '设置自适应拉伸传参错误',
        607: '实时预览不支持倍速播放',
        608: '需传入正确的{0}方法：{1}',
        609: '超出当前设置最大窗口布局数量',
        610: '操作自定义窗口布局失败：{0}',
        611: '实时预览不支持播放方法',
        612: '实时预览不支持暂停方法',
        613: '实时预览不支持跳转播放',
        614: '当前窗口未播放实时预览画面, 无法进行对讲',
        701: '云台被用户{0}锁定，无法操作',
        702: '控制云台三维定位失败{0}',
        703: '控制云台{0}{1}失败{2}',
        704: '控制云台方向失败{0}',
        30000001: 'SDP初始化失败',
        30000002: '接入服务内部错误（信令建立过程）',
        30000101: '请求失败',
        30000102: '拉流过程异常中断',
        30000103: '接入服务内部异常（码流传输过程）',
        30000104: '老设备回放时登入设备失败',
        30000105: '设备离线',
        30000106: '通道离线',
        30000107: '设备被删除',
        30000108: '通道被删除',
        30001001: '创建子链接失败',
        30001002: '设备不支持',
        30001003: '设备资源不足',
        30001004: '查询不到录像',
        30001005: '设备返回超时',
        30001006: '超过最大连接数',
        30002001: '拒绝对讲',
        30002002: '对讲被其他客户端打开，请稍后重试',
        30002003: '资源冲突',
        30002004: '不支持的语音编码格式',
        30002005: '无权限',
        30002006: '请求对讲失败',
        30002007: '请求喊话失败',
        30003001: '码率超限'
      },
      windowDefaultCustomDivision: {
        2: [
          { lStep: 0, tStep: 0, wStep: 50, hStep: 100 },
          { lStep: 50, tStep: 0, wStep: 50, hStep: 100 }
        ],
        3: [
          { wStep: 50, hStep: 100, tStep: 0, lStep: 0 },
          { wStep: 50, hStep: 50, tStep: 0, lStep: 50 },
          { wStep: 50, hStep: 50, tStep: 50, lStep: 50 }
        ],
        6: [
          { wStep: 66, hStep: 66, tStep: 0, lStep: 0, selectIndex: 0 },
          { wStep: 34, hStep: 33, tStep: 0, lStep: 66, selectIndex: 1 },
          { wStep: 34, hStep: 33, tStep: 33, lStep: 66, selectIndex: 2 },
          { wStep: 33, hStep: 34, tStep: 66, lStep: 0, selectIndex: 3 },
          { wStep: 33, hStep: 34, tStep: 66, lStep: 33, selectIndex: 4 },
          { wStep: 34, hStep: 34, tStep: 66, lStep: 66, selectIndex: 5 }
        ],
        8: [
          { wStep: 75, hStep: 75, tStep: 0, lStep: 0, selectIndex: 0 },
          { wStep: 25, hStep: 25, tStep: 0, lStep: 75, selectIndex: 1 },
          { wStep: 25, hStep: 25, tStep: 25, lStep: 75, selectIndex: 2 },
          { wStep: 25, hStep: 25, tStep: 50, lStep: 75, selectIndex: 3 },
          { wStep: 25, hStep: 25, tStep: 75, lStep: 0, selectIndex: 4 },
          { wStep: 25, hStep: 25, tStep: 75, lStep: 25, selectIndex: 5 },
          { wStep: 25, hStep: 25, tStep: 75, lStep: 50, selectIndex: 6 },
          { wStep: 25, hStep: 25, tStep: 75, lStep: 75, selectIndex: 7 }
        ]
      },
      iconConfig: [
        {
          key: 'refreshReplay',
          label: 'wsPlayer.refreshReplay',
          buttonParentClass: 'ws-refresh-replay',
          buttonIconClass: 'opt-icon refresh-replay-icon'
        },
        {
          key: 'drawTriangle',
          label: 'wsPlayer.drawTriangle',
          buttonParentClass: 'ws-draw-triangle',
          buttonIconClass: 'opt-icon draw-triangle-icon off'
        },
        {
          key: 'talkIcon',
          label: 'wsPlayer.talk',
          buttonParentClass: 'ws-talk',
          buttonIconClass: 'opt-icon talk-icon off'
        },
        {
          key: 'localRecordIcon',
          label: 'wsPlayer.recording',
          buttonParentClass: 'ws-record',
          buttonIconClass: 'opt-icon record-icon'
        },
        {
          key: 'audioIcon',
          label: 'wsPlayer.sound',
          buttonParentClass: 'ws-audio',
          buttonIconClass: 'opt-icon audio-icon off'
        },
        {
          key: 'snapshotIcon',
          label: 'wsPlayer.capture',
          buttonParentClass: 'ws-capture',
          buttonIconClass: 'opt-icon capture-icon'
        }
      ],
      _translate(e, t, s) {
        Object.keys(this[e]).forEach((i) => {
          let r = t.$t(`${s}${i}`)
          r && (this[e][i] = r)
        })
      },
      updateLocale(e) {
        this._translate('errorInfo', e, 'wsPlayer.error.'),
          this._translate('errorVideoInfo', e, 'wsPlayer.play.error.'),
          this.iconConfig.forEach((t) => {
            t.label = e.$t(t.label)
          })
      },
      transParam: 8192,
      contentPadding: 16,
      tagHeight: 50,
      contentTop: 64,
      lineHeight: 24
    },
    s = 'Opera',
    i = 'Chrome',
    r = 'Firefox',
    a = 'Edge',
    l = 'Edg',
    n = 'IE',
    o = 'Safari'
  const c = {
    checkBrowser: function () {
      const e = (function () {
          const { userAgent: e } = navigator
          return e.includes('Edge')
            ? a
            : e.includes('Edg')
              ? l
              : e.includes('Firefox')
                ? r
                : e.includes('Chrome')
                  ? i
                  : e.includes('Safari')
                    ? o
                    : e.includes('compatible') && e.includes('MSIE') && e.includes('Opera')
                      ? n
                      : e.includes('Opera')
                        ? s
                        : ''
        })(),
        t = navigator.userAgent.includes('x64') || navigator.userAgent.includes('x86_64') ? 64 : 32,
        c = (function (e) {
          const { userAgent: t } = navigator
          return t.split(e)[1].split('.')[0].slice(1)
        })(e)
      let d = !1,
        h = 0
      switch (e) {
        case i:
          ;(d = c >= 91 && 64 === t), (h = 701)
          break
        case r:
          ;(d = c >= 97), (h = 702)
          break
        case a:
        case l:
          ;(d = c >= 91), (h = 703)
          break
        default:
          d = 0
      }
      return { isVersionCompliance: d, browserType: e, errorCode: h }
    },
    validFunction: function (e) {
      return '[object Function]' === toString.call(e)
    },
    mergeObject: function e() {
      let t = {}
      for (let i = 0; i < arguments.length; i++) {
        let r = arguments[i]
        for (let i in r) {
          let a = r[i]
          'localeI18n' === i
            ? (t[i] = a)
            : ((s = a),
              '[object Object]' === toString.call(s) ? (t[i] = e(a)) : null != a && (t[i] = a))
        }
      }
      var s
      return t
    },
    getDateFormatByUnderline: function () {
      return (function () {
        let e = new Date()
        return [
          e.getFullYear(),
          e.getMonth() + 1,
          e.getDate(),
          e.getHours(),
          e.getMinutes(),
          e.getSeconds(),
          e.getMilliseconds()
        ]
      })().join('_')
    },
    throttle: function (e, t) {
      let s
      return function () {
        s ||
          (e.apply(this, arguments),
          (s = setTimeout(() => {
            s = 0
          }, t)))
      }
    },
    debounce: function (e, t) {
      let s
      return function () {
        s && clearTimeout(s),
          (s = setTimeout(() => {
            e.apply(this, arguments), (s = 0)
          }, t))
      }
    }
  }
  var d =
      (globalThis && globalThis.__assign) ||
      function () {
        return (
          (d =
            Object.assign ||
            function (e) {
              for (var t, s = 1, i = arguments.length; s < i; s++)
                for (var r in (t = arguments[s]))
                  Object.prototype.hasOwnProperty.call(t, r) && (e[r] = t[r])
              return e
            }),
          d.apply(this, arguments)
        )
      },
    h = {
      lines: 12,
      length: 7,
      width: 5,
      radius: 10,
      scale: 1,
      corners: 1,
      color: '#000',
      fadeColor: 'transparent',
      animation: 'spinner-line-fade-default',
      rotate: 0,
      direction: 1,
      speed: 1,
      zIndex: 2e9,
      className: 'spinner',
      top: '50%',
      left: '50%',
      shadow: '0 0 1px transparent',
      position: 'absolute'
    },
    p = (function () {
      function e(e) {
        void 0 === e && (e = {}), (this.opts = d(d({}, h), e))
      }
      return (
        (e.prototype.spin = function (e) {
          return (
            this.stop(),
            (this.el = document.createElement('div')),
            (this.el.className = this.opts.className),
            this.el.setAttribute('role', 'progressbar'),
            y(this.el, {
              position: this.opts.position,
              width: 0,
              zIndex: this.opts.zIndex,
              left: this.opts.left,
              top: this.opts.top,
              transform: 'scale(' + this.opts.scale + ')'
            }),
            e && e.insertBefore(this.el, e.firstChild || null),
            (function (e, t) {
              var s = Math.round(t.corners * t.width * 500) / 1e3 + 'px',
                i = 'none'
              !0 === t.shadow
                ? (i = '0 2px 4px #000')
                : 'string' == typeof t.shadow && (i = t.shadow)
              for (
                var r = (function (e) {
                    for (
                      var t =
                          /^\s*([a-zA-Z]+\s+)?(-?\d+(\.\d+)?)([a-zA-Z]*)\s+(-?\d+(\.\d+)?)([a-zA-Z]*)(.*)$/,
                        s = [],
                        i = 0,
                        r = e.split(',');
                      i < r.length;
                      i++
                    ) {
                      var a = r[i].match(t)
                      if (null !== a) {
                        var l = +a[2],
                          n = +a[5],
                          o = a[4],
                          c = a[7]
                        0 !== l || o || (o = c),
                          0 !== n || c || (c = o),
                          o === c &&
                            s.push({
                              prefix: a[1] || '',
                              x: l,
                              y: n,
                              xUnits: o,
                              yUnits: c,
                              end: a[8]
                            })
                      }
                    }
                    return s
                  })(i),
                  a = 0;
                a < t.lines;
                a++
              ) {
                var l = ~~((360 / t.lines) * a + t.rotate),
                  n = y(document.createElement('div'), {
                    position: 'absolute',
                    top: -t.width / 2 + 'px',
                    width: t.length + t.width + 'px',
                    height: t.width + 'px',
                    background: u(t.fadeColor, a),
                    borderRadius: s,
                    transformOrigin: 'left',
                    transform: 'rotate(' + l + 'deg) translateX(' + t.radius + 'px)'
                  }),
                  o = (a * t.direction) / t.lines / t.speed
                o -= 1 / t.speed
                var c = y(document.createElement('div'), {
                  width: '100%',
                  height: '100%',
                  background: u(t.color, a),
                  borderRadius: s,
                  boxShadow: m(r, l),
                  animation: 1 / t.speed + 's linear ' + o + 's infinite ' + t.animation
                })
                n.appendChild(c), e.appendChild(n)
              }
            })(this.el, this.opts),
            this
          )
        }),
        (e.prototype.stop = function () {
          return (
            this.el &&
              ('undefined' != typeof requestAnimationFrame
                ? cancelAnimationFrame(this.animateId)
                : clearTimeout(this.animateId),
              this.el.parentNode && this.el.parentNode.removeChild(this.el),
              (this.el = void 0)),
            this
          )
        }),
        e
      )
    })()
  function y(e, t) {
    for (var s in t) e.style[s] = t[s]
    return e
  }
  function u(e, t) {
    return 'string' == typeof e ? e : e[t % e.length]
  }
  function m(e, t) {
    for (var s = [], i = 0, r = e; i < r.length; i++) {
      var a = r[i],
        l = w(a.x, a.y, t)
      s.push(a.prefix + l[0] + a.xUnits + ' ' + l[1] + a.yUnits + a.end)
    }
    return s.join(', ')
  }
  function w(e, t, s) {
    var i = (s * Math.PI) / 180,
      r = Math.sin(i),
      a = Math.cos(i)
    return [Math.round(1e3 * (e * a + t * r)) / 1e3, Math.round(1e3 * (-e * r + t * a)) / 1e3]
  }
  class P {
    constructor(e) {
      ;(this.$el = null),
        (this.canvasElem = null),
        (this.videoElem = null),
        (this.wrapperDomId = e.wrapperDomId),
        (this.i18n = e.locale),
        (this.isclosing = !1),
        (this.domId = e.wrapperDomId + '-' + e.index),
        (this.wsPlayer = e.wsPlayer),
        (this.index = e.index),
        (this.initIndex = e.index),
        (this.firstTime = 0),
        (this.isAudioPlay = !1),
        (this.speed = 1)
    }
    initDom() {
      let e = this.getTemplate(),
        t = WSPlayerJQ.$(e)
      this.wsPlayer.$wrapper.append(t[0]),
        (this.$el = WSPlayerJQ.$('#' + this.domId)),
        (this.canvasElem = document.getElementById(this.canvasId) || {}),
        (this.ivsCanvasElem = document.getElementById(this.ivsCanvasId) || {}),
        (this.cRawCanvasElem = document.getElementById(this.cRawCanvasId) || {}),
        (this.pztCanvasElem = document.getElementById(this.pztCanvasId) || {}),
        (this.videoElem = document.getElementById(this.videoId)),
        (this.showIcons = this.wsPlayer.config.showIcons || {}),
        (this.showIconNum = 7),
        this.showIcons.streamChangeSelect ||
          this.setDomHiddenDeep(WSPlayerJQ.$('.select-container', this.$el), !1),
        this.showIcons.replayIcon ||
          (this.setDomHiddenDeep(WSPlayerJQ.$('.ws-refresh-replay', this.$el), !1),
          this.showIconNum--),
        this.showIcons.ivsIcon ||
          (this.setDomHiddenDeep(WSPlayerJQ.$('.ws-draw-triangle', this.$el), !1),
          this.showIconNum--),
        this.setTalkIconShow(),
        this.showIcons.audioIcon ||
          (this.setDomHiddenDeep(WSPlayerJQ.$('.ws-audio', this.$el), !1), this.showIconNum--),
        this.showIcons.snapshotIcon ||
          (this.setDomHiddenDeep(WSPlayerJQ.$('.ws-capture', this.$el), !1), this.showIconNum--),
        this.showIcons.localRecordIcon ||
          (this.setDomHiddenDeep(WSPlayerJQ.$('.ws-record', this.$el), !1), this.showIconNum--),
        this.showIcons.closeIcon ||
          (this.setDomHiddenDeep(WSPlayerJQ.$('.ws-close', this.$el), !1), this.showIconNum--),
        (this.showMoreIcon = !1)
    }
    setTalkIconShow() {
      let { talkIcon: e } = this.wsPlayer.config.showIcons || {},
        t = location.protocol
      e && 'https:' === t && 'real' === this.wsPlayer.type
        ? WSPlayerJQ.$('.ws-talk', this.$el).css({ display: '' })
        : (this.setDomHiddenDeep(WSPlayerJQ.$('.ws-talk', this.$el), !1), this.showIconNum--)
    }
    initMouseEvent() {
      this.$el.click((e) => {
        this.wsPlayer.setSelectIndex(this.index),
          this.$el.siblings().removeClass('selected').addClass('unselected'),
          this.$el.removeClass('unselected').addClass('selected')
      }),
        this.$el.dblclick((e) => {
          if (1 !== this.wsPlayer.options.config.maxNum) {
            if (1 !== this.wsPlayer.showNum) {
              this.wsPlayer.beforeShowNum = this.wsPlayer.showNum
              let e = WSPlayerJQ.$(`#${this.wrapperDomId} .wsplayer-item`)
              this.wsPlayer.resetPlayerScreen(e, this.wsPlayer.options.config.maxNum),
                WSPlayerJQ.$(`#${this.wrapperDomId}-${this.index}`).css({
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  visibility: 'visible'
                }),
                (this.wsPlayer.showNum = 1)
            } else this.wsPlayer.setPlayerNum(this.wsPlayer.beforeShowNum, !0)
            this.wsPlayer.setSelectIndex(this.index),
              this.$el.siblings().removeClass('selected').addClass('unselected'),
              this.$el.removeClass('unselected').addClass('selected'),
              setTimeout(() => {
                this.wsPlayer.__updatePlayerWindow()
              }, 200)
          }
        }),
        WSPlayerJQ.$('.ws-audio', this.$el).click((e) => {
          if (this.currentWindowPlaying) {
            if (this.wsPlayer.isTalking && this.isTalking && this.gbTalk);
            else if (this.wsPlayer.isTalking)
              return void this.wsPlayer.sendErrorMessage(this.isTalking ? '301' : '302')
            if (this.isAudioPlay) this.closeVolume()
            else {
              if (this.playSDK.isPlayback && 1 !== Number(this.speed))
                return void this.wsPlayer.sendErrorMessage('204', { insert: [this.speed] })
              this.openVolume()
            }
          }
        }),
        WSPlayerJQ.$('.ws-refresh-replay', this.$el).click((e) => {
          'url' === this.wsPlayer.playerList[this.index].playType
            ? this.wsPlayer.sendMessage('refreshReplay', {
                selectIndex: this.options.selectIndex,
                channelInfo: { ...this.options }
              })
            : 'real' === this.wsPlayer.type
              ? this.wsPlayer.playRealVideo({
                  channelList: [{ ...this.options.channelData }],
                  streamType: this.options.streamType,
                  windowIndex: this.index
                })
              : this.wsPlayer.playRecordVideo({
                  channelList: [{ ...this.options.channelData }],
                  startTime: this.options.startTime,
                  endTime: this.options.endTime,
                  recordSource: this.options.recordSource,
                  streamType: this.options.streamType,
                  recordType: this.options.recordType,
                  windowIndex: this.index,
                  playRecordByFile: this.options.playRecordByFile
                })
        }),
        WSPlayerJQ.$('.ws-draw-triangle', this.$el).click((e) => {
          this.currentWindowPlaying &&
            (e.target.getAttribute('class').includes('off')
              ? (WSPlayerJQ.$('.ws-draw-triangle', this.$el).removeClass('off').addClass('on'),
                WSPlayerJQ.$('.draw-triangle-icon', this.$el).removeClass('off').addClass('on'),
                this.playSDK.OpenIVS(this.options.selectIndex),
                this.playSDK.SetIvsLanguageEnvi('en' === localStorage.getItem('language') ? 1 : 0),
                this.wsPlayer.config.ivsTypeArr.includes(1) || this.playSDK.SetIvsEnable(3, 0),
                this.wsPlayer.config.ivsTypeArr.includes(2) ||
                  (this.playSDK.SetIvsEnable(1, 0), this.playSDK.SetIvsEnable(14, 0)))
              : (WSPlayerJQ.$('.ws-draw-triangle', this.$el).removeClass('on').addClass('off'),
                WSPlayerJQ.$('.draw-triangle-icon', this.$el).removeClass('on').addClass('off'),
                this.playSDK.CloseIVS()))
        })
      WSPlayerJQ.$('.ws-talk', this.$el).click(
        c.throttle((e) => {
          if (this.currentWindowPlaying)
            if ('http:' !== location.protocol)
              if (this.wsPlayer.isTalking && !this.isTalking) this.wsPlayer.sendErrorMessage('303')
              else if (this.isTalking) this.stopTalk()
              else {
                const { selectIndex: e, channelData: t } = this.options
                if (this.options && 'url' === this.options.playType)
                  return void this.wsPlayer.sendMessage('notifyTalk', {
                    selectIndex: e,
                    channelData: { ...t }
                  })
                this.resumeAudio(),
                  this.setAuthority(
                    {
                      channelCode: this.options.channelData
                        ? this.options.channelData.channelCode
                        : this.options.channelId,
                      function: '3'
                    },
                    () => {
                      ;(this.wsPlayer.talkIndex = this.index),
                        this.wsPlayer.__startTalk(this.options.channelData)
                    },
                    (s) => {
                      1103 === s.code &&
                        this.wsPlayer.sendErrorMessage(401, {
                          selectIndex: e,
                          channelData: { ...t },
                          type: 'talk'
                        })
                    }
                  )
              }
            else this.wsPlayer.sendErrorMessage('305')
        }, 2e3)
      ),
        WSPlayerJQ.$('.ws-capture', this.$el).click((e) => {
          this.currentWindowPlaying &&
            this.setAuthority(
              {
                channelCode: this.options.channelData
                  ? this.options.channelData.channelCode
                  : this.options.channelId,
                function: '4'
              },
              () => {
                this.picCap()
              },
              (e) => {
                1103 === e.code && this.wsPlayer.sendErrorMessage(401, { type: 'capture' })
              }
            )
        }),
        WSPlayerJQ.$('.ws-close', this.$el).click((e) => {
          this.close()
        }),
        WSPlayerJQ.$('.ws-record', this.$el).click((e) => {
          if (!this.currentWindowPlaying) return
          let t = (this.options.channelData || {}).name || this.i18n.$t('wsPlayer.recording')
          this.isRecording
            ? ((this.isRecording = !1),
              this.playSDK.StopRecord(),
              WSPlayerJQ.$('.ws-record', this.$el).removeClass('recording'))
            : 'playing' === this.status &&
              this.setAuthority(
                {
                  channelCode: this.options.channelData
                    ? this.options.channelData.channelCode
                    : this.options.channelId,
                  function: '8'
                },
                () => {
                  ;(this.isRecording = !0),
                    this.startRecord(
                      `${this.i18n.$t('wsPlayer.video')}_${t}_${c.getDateFormatByUnderline()}`
                    ),
                    WSPlayerJQ.$('.ws-record', this.$el).addClass('recording')
                },
                (e) => {
                  1103 === e.code && this.wsPlayer.sendErrorMessage(401, { type: 'record' })
                }
              )
        }),
        WSPlayerJQ.$('.more-icon', this.$el).click((e) => {
          ;(this.showMoreIcon = !this.showMoreIcon),
            this.setDomVisible(WSPlayerJQ.$('.opt-icons-vertical', this.$el), this.showMoreIcon)
        }),
        WSPlayerJQ.$('.cRaw-container', this.$el).click((e) => {
          let t = e.target.dataset && e.target.dataset.pid
          if (t) {
            'hidden' === WSPlayerJQ.$(`#${t} .ws-tag-content`, this.$el).css('visibility')
              ? (this.setTagContentPosition(t),
                WSPlayerJQ.$(`#${t} .ws-tag-content`, this.$el).css('visibility', 'visible'),
                WSPlayerJQ.$(`#${t} .ws-tag-title`, this.$el).css('max-width', '180px'))
              : (WSPlayerJQ.$(`#${t} .ws-tag-content`, this.$el).css('visibility', 'hidden'),
                WSPlayerJQ.$(`#${t} .ws-tag-title`, this.$el).css('max-width', ''))
          }
        })
    }
    setLoading(e, t) {
      this.spinner && this.spinner.stop(),
        e && ['playing', 'pause'].includes(this.status) && this.close(),
        t && this.setStatus(t),
        e && (this.spinner = new p({ color: '#ffffff' }).spin(this.$el[0]))
    }
    picCap(e) {
      let t = e
      if (!t) {
        let e = (this.options.channelData || {}).name || ''
        t = `${this.i18n.$t('wsPlayer.capture')}_${e}_${c.getDateFormatByUnderline()}`
      }
      this.playSDK.CapturePic(this.wsPlayer.config.picCapCb ? null : t + '.png')
    }
    startRecord(e, t, s) {
      const { downloadMp4Record: i, localRecordSize: r } = this.wsPlayer.config
      let a = 'boolean' == typeof s ? s : i
      a = !localStorage.playSDKLogLevel && a
      let l = t || r
      this.playSDK.StartRecord(a ? 5 : 0, l, `${e}.${a ? 'mp4' : 'dav'}`)
    }
    closeOtherAudio() {
      this.wsPlayer.playerList.forEach((e) => {
        e.isAudioPlay && e.closeVolume()
      })
    }
    openVolume(e = 1) {
      this.closeOtherAudio(),
        this.playSDK.SetSoundState(!0),
        this.playSDK.SetVolume(e > 1 ? 1 : e),
        this.resumeAudio(),
        WSPlayerJQ.$('.ws-audio', this.$el).removeClass('off').addClass('on'),
        WSPlayerJQ.$('.audio-icon', this.$el).removeClass('off').addClass('on'),
        (this.isAudioPlay = !0)
    }
    closeVolume() {
      this.playSDK && this.playSDK.SetSoundState(!1),
        WSPlayerJQ.$('.ws-audio', this.$el).removeClass('on').addClass('off'),
        WSPlayerJQ.$('.audio-icon', this.$el).removeClass('on').addClass('off'),
        (this.isAudioPlay = !1)
    }
    setAuthority(e, t, s) {
      this.wsPlayer.fetchChannelAuthority
        ? this.wsPlayer
            .fetchChannelAuthority(e)
            .then((e) => {
              e.data.result && t()
            })
            .catch((e) => {
              s(e)
            })
        : t()
    }
    resumeAudio() {
      if (window.wsAudioPlayer) window.wsAudioPlayer.manualResume('fromTalk')
      else {
        let e = setInterval(() => {
          window.wsAudioPlayer && (window.wsAudioPlayer.manualResume('fromTalk'), clearInterval(e))
        }, 100)
      }
    }
    setStatus() {}
    showMsgInWindow(e) {
      this.setDomVisible(this.defaultStatus, !1),
        WSPlayerJQ.$('.wsplayer-error-message', this.$el).text(e),
        this.setDomVisible(this.error, !0)
    }
    play() {
      this.playSDK.Pause(0),
        this.playSDK.SetSpeed(this.speed),
        this.setStatus('playing'),
        WSPlayerJQ.$('.ws-record-play', this.wsPlayer.$el).css({ display: 'none' }),
        WSPlayerJQ.$('.ws-record-pause', this.wsPlayer.$el).css({ display: '' })
    }
    pause() {
      this.playSDK.Pause(1),
        this.setStatus('pause'),
        WSPlayerJQ.$('.ws-record-pause', this.wsPlayer.$el).css({ display: 'none' }),
        WSPlayerJQ.$('.ws-record-play', this.wsPlayer.$el).css({ display: '' })
    }
    close(e = !1, t = !1, s = 'closed') {
      if (
        ((this.currentWindowPlaying = !1),
        this.setDomVisible(WSPlayerJQ.$('.play-pause-wrapper', this.$el), !1),
        (this.videoElem.style.display = 'none'),
        (this.canvasElem.style.display = 'none'),
        (this.ivsCanvasElem.style.display = 'none'),
        this.customDomElem && this.customDomElem.css('display', 'none'),
        this.wsPlayer.playerList.forEach((e) => {
          e.closeCRaw()
        }),
        this.isTalking && !t && this.stopTalk(),
        (this.speed = 1),
        this.index === this.wsPlayer.selectIndex &&
          ('real' === this.wsPlayer.type
            ? !e && this.wsPlayer.setPtzChannel()
            : (this.wsPlayer.setTimeLine([]),
              this.wsPlayer.__setPlaySpeed(),
              WSPlayerJQ.$('.ws-record-play', this.wsPlayer.$el).css({ display: '' }),
              WSPlayerJQ.$('.ws-record-pause', this.wsPlayer.$el).css({ display: 'none' }))),
        this.isRecording &&
          ((this.isRecording = !1),
          this.playSDK.StopRecord(),
          WSPlayerJQ.$('.ws-record', this.$el).removeClass('recording')),
        this.wsPlayer.config.openIvs && this.playSDK)
      )
        try {
          this.playSDK.CloseIVS()
        } catch (i) {
          console.debug('关闭规则线')
        }
      this.spinner && this.spinner.stop(),
        WSPlayerJQ.$('.stream-info', this.$el).text(''),
        this.wsPlayer.videoClosed(
          this.index,
          e,
          { ...((this.options && this.options.channelData) || {}) },
          this.customDomId
        ),
        (this.isclosing = !1),
        this.playSDK &&
          (this.playSDK.StopPullStream(),
          this.playSDK.Stop(),
          window.wsPlayerManager.unbindPlayer(this.playSDK.nPlayPort),
          window.wsPlayerManager.unbindHandlePlayer(this.playSDK.m_rtspvHandle)),
        e || ((this.playSDK = null), (this.options = null)),
        this.setStatus(s)
    }
    setDomVisible(e, t) {
      e && e.css({ visibility: t ? 'visible' : 'hidden' })
    }
    setDomDisplay(e, t) {
      e && e.css({ display: t ? '' : 'none' })
    }
    setDomHiddenDeep(e, t) {
      e && (t ? e.removeClass('hidden-deep') : e.addClass('hidden-deep'))
    }
    setLogLevel() {
      this.playSDK.SetPrintLogLevel && this.playSDK.SetPrintLogLevel(localStorage.playSDKLogLevel)
    }
    setCacheMode() {
      '4' === String(this.wsPlayer.config.cacheMode)
        ? (this.playSDK.SetCacheMode(0), this.playSDK.SetPlayMethod(200, 1e3, 3e3))
        : ['0', '1', '2', '3'].includes(String(this.wsPlayer.config.cacheMode))
          ? this.playSDK.SetCacheMode(this.wsPlayer.config.cacheMode || 1)
          : this.playSDK.SetCacheMode(1)
    }
    uint8ArrayToBase64(e) {
      if (!e || 0 === e.byteLength) return ''
      let t = '',
        s = e.byteLength
      for (let i = 0; i < s; i++) t += String.fromCharCode(e[i])
      return window.btoa(t)
    }
    updateAdapter(e, t = {}) {
      let s = t.width / t.height,
        i = 'video' === (t.decodeMode || this.decodeMode) ? this.videoElem : this.canvasElem,
        r = i.parentNode
      t.decodeMode
        ? ((this.decodeMode = t.decodeMode), (this.width = t.width), (this.height = t.height))
        : (s = this.width / this.height)
      let a = '100%',
        l = '100%'
      if ('selfAdaption' === e) {
        let e = r.offsetHeight,
          t = r.offsetWidth,
          n = t / e
        s > n ? (l = t / s + 'px') : s < n && (a = e * s + 'px'),
          'video' === this.decodeMode
            ? (WSPlayerJQ.$(i).css({ width: a, height: l, 'object-fit': 'contain' }),
              WSPlayerJQ.$(this.canvasElem).css({ width: a, height: l, 'object-fit': 'contain' }))
            : WSPlayerJQ.$(i).css({ width: a, height: l }),
          WSPlayerJQ.$(this.ivsCanvasElem).css({ width: a, height: l, 'object-fit': 'contain' }),
          WSPlayerJQ.$(this.pztCanvasElem).css &&
            WSPlayerJQ.$(this.pztCanvasElem).css({ width: a, height: l, 'object-fit': 'contain' }),
          WSPlayerJQ.$(this.cRawCanvasElem).css({ width: a, height: l, 'object-fit': 'contain' })
      } else
        WSPlayerJQ.$(i).css({ width: a, height: l, 'object-fit': 'fill' }),
          'video' === this.decodeMode &&
            WSPlayerJQ.$(this.canvasElem).css({ width: a, height: l, 'object-fit': 'fill' }),
          WSPlayerJQ.$(this.ivsCanvasElem).css({ width: a, height: l, 'object-fit': 'fill' }),
          WSPlayerJQ.$(this.pztCanvasElem).css &&
            WSPlayerJQ.$(this.pztCanvasElem).css({ width: a, height: l, 'object-fit': 'fill' }),
          WSPlayerJQ.$(this.cRawCanvasElem).css({ width: a, height: l, 'object-fit': 'fill' })
      this.customDomElem && this.customDomElem.css({ width: a, height: l }),
        this.customDomElem &&
          this.wsPlayer.sendMessage('customDomInfo', {
            customDomId: this.customDomId,
            width: a,
            height: l,
            currentWindowPlaying: this.currentWindowPlaying || !1
          }),
        this.playSDK &&
          ((this.ivsCanvasElem.width = i.offsetWidth),
          (this.ivsCanvasElem.height = i.offsetHeight),
          (this.cRawCanvasElem.width = i.offsetWidth),
          (this.cRawCanvasElem.height = i.offsetHeight),
          (this.pztCanvasElem.width = i.offsetWidth),
          (this.pztCanvasElem.height = i.offsetHeight),
          setTimeout(() => {
            this.wsPlayer.playerList.forEach((e) => {
              'playing' === e.status && e.setCRawTagsPos()
            })
          }, 100))
    }
    renderDefaultStats() {
      let e = WSPlayerJQ.$('.default-status', this.$el),
        t = e.width(),
        s = e.height(),
        i = WSPlayerJQ.$('.wsplayer-error-message', this.$el)
      t &&
        s &&
        (t < 100 || s < 100
          ? (e.css({ transform: 'scale(0.3)' }), i.css({ transform: 'scale(0.3)' }))
          : t < 200 || s < 200
            ? (e.css({ transform: 'scale(0.5)' }), i.css({ transform: 'scale(0.5)' }))
            : (e.css({ transform: 'scale(1)' }), i.css({ transform: 'scale(1)' })))
    }
    getCRawTemplate(e = !1, t, s, i, r, a, l) {
      let n = t.split('|'),
        o = '',
        c = ''
      for (let p = 0; p < n.length; p++) o += `<p>${n[p]}</p>`
      let { prefixUrl: d } = this.wsPlayer,
        h = `\n            <div> \n                <p class="ws-tag-title" data-pid="${i}" title="${s}">\n                    <img src="${d}/WSPlayer/icon/tag-left-${l ? 'alarm' : 'normal'}.png" class="ws-left-png" data-pid="${i}"/>\n                    <img src="${d}/WSPlayer/icon/tag-middle-${l ? 'alarm' : 'normal'}.png" class="ws-middle-png" height="75" data-pid="${i}"/>\n                    <img src="${d}/WSPlayer/icon/tag-right-${l ? 'alarm' : 'normal'}.png" class="ws-right-png" data-pid="${i}"/>\n                    <span data-pid="${i}">${s}</span>\n                </p>\n                <div class="ws-tag-content ${l ? 'ws-tag-content-alarm' : ''}" style="visibility: hidden">\n                    ${o}\n                </div>\n            </div>`
      return (
        (c = e
          ? h
          : `\n            <div id="${i}" class="ws-tag-container" style="left:${r}px;top:${a}px">\n               ${h}\n            </div>`),
        c
      )
    }
    setTagContentPosition(e) {
      if (!e) return
      let t = WSPlayerJQ.$(`#${this.cRawCanvasId}`, this.$el).outerHeight(),
        s = WSPlayerJQ.$(`#${this.cRawCanvasId}`, this.$el).outerWidth(),
        i = WSPlayerJQ.$(`#${e}`, this.$el),
        r = i[0].offsetTop,
        a = i[0].offsetLeft,
        l = t - r - i.innerHeight(),
        n = WSPlayerJQ.$(`#${e} .ws-tag-content`, this.$el),
        o = n.innerHeight(),
        c = n.innerWidth(),
        d = 64
      l < o && r > l && (d = -o)
      let h = 42
      a + h + c > s && (h = s - a - c),
        WSPlayerJQ.$(`#${e} .ws-tag-content`, this.$el).css('top', `${d}px`),
        WSPlayerJQ.$(`#${e} .ws-tag-content`, this.$el).css('left', `${h}px`)
    }
    getCRawArr(e) {
      let t = {},
        s = {}
      for (let i = 0; i < e.length; i++) {
        const r = e[i]
        if (null !== r) {
          let { DeviceName: e } = r
          ;(t[e] = +new Date() + i), (s[e] = r)
        }
      }
      return { curCRawMap: t, curInfoMap: s }
    }
    handleCRawArrs(e, s, i) {
      let r = e.filter((e) => !s.includes(e)),
        a = s.filter((t) => !e.includes(t)),
        l = e.filter((e) => s.includes(e))
      if (r.length)
        for (let t = 0; t < r.length; t++) {
          const e = r[t]
          let s = `#${this.LastCRawMap[e]}`
          $(s).remove(), delete this.LastCRawMap[e], delete this.LastCRawInfo[e]
        }
      if (a.length) {
        let e = document.createDocumentFragment()
        for (let s = 0; s < a.length; s++) {
          const r = a[s]
          if (i[r]) {
            ;(this.LastCRawMap[r] = +new Date() + s), (this.LastCRawInfo[r] = i[r])
            let { DeviceName: a, Text: l, Rect: n, AlarmEnable: o } = i[r],
              c = this.cRawCanvasElem.width,
              d = this.cRawCanvasElem.height,
              h = (n[0] * c) / t.transParam,
              p = (n[1] * d) / t.transParam,
              y = this.LastCRawMap[a],
              u = this.getCRawTemplate(!1, l, a, y, h, p, o),
              m = document.createRange().createContextualFragment(u)
            e.appendChild(m)
          }
        }
        $('#' + this.cRawCanvasId).append(e)
      }
      if (l.length)
        for (let n = 0; n < l.length; n++) {
          const e = l[n]
          if (i[e]) {
            let { DeviceName: s, Text: r, Rect: a, AlarmEnable: l } = i[e],
              n = `#${this.LastCRawMap[s]}`,
              o = this.LastCRawInfo[e].Rect,
              c = this.LastCRawInfo[e].Text,
              d = this.cRawCanvasElem.width,
              h = this.cRawCanvasElem.height,
              p = (a[0] * d) / t.transParam,
              y = (a[1] * h) / t.transParam
            if (
              ((a[0] === o[0] && a[1] === o[1]) ||
                (WSPlayerJQ.$(n, this.$el).css({ left: `${p}px`, top: `${y}px` }),
                this.setTagContentPosition(this.LastCRawMap[s])),
              c !== r)
            ) {
              let e = this.getCRawTemplate(!0, r, s, this.LastCRawMap[s], p, y, l, !0)
              WSPlayerJQ.$(n, this.$el).html(e)
            }
            this.LastCRawInfo[e] = i[e]
          }
        }
    }
    setCRawTagsPos() {
      for (const e in this.LastCRawMap) {
        let s = `#${this.LastCRawMap[e]}`,
          i = this.cRawCanvasElem.width,
          r = this.cRawCanvasElem.height,
          { Text: a, Rect: l } = this.LastCRawInfo[e],
          n = (l[0] * i) / t.transParam,
          o = (l[1] * r) / t.transParam
        WSPlayerJQ.$(s, this.$el).css({ left: `${n}px`, top: `${o}px` }),
          this.setTagContentPosition(this.LastCRawMap[e])
      }
    }
    closeCRaw() {
      WSPlayerJQ.$(`#${this.cRawCanvasId}`, this.$el).empty(),
        (this.LastCRawMap = {}),
        (this.LastCRawInfo = {})
    }
    updateTopBarStyle() {
      let e = WSPlayerJQ.$('.top-control-bar', this.$el).width(),
        s = 94,
        i = 130
      'zh-cn' !== localStorage.getItem('language') && ((s = 134), (i = 220))
      let r = e - (this.showIcons.streamChangeSelect && 'real' === this.type ? s : 0) - 10
      if (r > 28 * this.showIconNum + i)
        this.setDomDisplay(WSPlayerJQ.$('.more-icon', this.$el), !1),
          this.setDomDisplay(WSPlayerJQ.$('.stream-info', this.$el), !0),
          t.iconConfig.forEach((e, t) => {
            this.setDomDisplay(WSPlayerJQ.$(`.opt-icons > .${e.buttonParentClass}`, this.$el), !0)
          })
      else if (r > 28 * this.showIconNum)
        this.setDomDisplay(WSPlayerJQ.$('.more-icon', this.$el), !1),
          this.setDomDisplay(WSPlayerJQ.$('.stream-info', this.$el), !1),
          t.iconConfig.forEach((e, t) => {
            this.setDomDisplay(WSPlayerJQ.$(`.opt-icons > .${e.buttonParentClass}`, this.$el), !0)
          })
      else {
        let e = Math.floor(r / 28)
        this.setDomDisplay(WSPlayerJQ.$('.stream-info', this.$el), !1),
          this.setDomDisplay(WSPlayerJQ.$('.more-icon', this.$el), !0),
          t.iconConfig.forEach((t, s) => {
            e > s + 2
              ? (this.setDomDisplay(
                  WSPlayerJQ.$(`.opt-icons > .${t.buttonParentClass}`, this.$el),
                  !0
                ),
                this.setDomDisplay(
                  WSPlayerJQ.$(`.opt-icons-vertical > .${t.buttonParentClass}`, this.$el),
                  !1
                ))
              : (this.setDomDisplay(
                  WSPlayerJQ.$(`.opt-icons > .${t.buttonParentClass}`, this.$el),
                  !1
                ),
                this.setDomDisplay(
                  WSPlayerJQ.$(`.opt-icons-vertical > .${t.buttonParentClass}`, this.$el),
                  !0
                ))
          })
      }
    }
  }
  const S = {
    zhCN: {
      'common.more': '更多',
      'common.close': '关闭',
      'common.ok': '确定',
      'common.cancel': '取消',
      'common.reset': '重置',
      'storagePlan.stream.main': '主码流',
      'video.player.sub.stream': '辅码流{0}',
      'video.player.ptz.down': '下',
      'video.player.ptz.left': '左',
      'video.player.ptz.lower.left': '左下',
      'video.player.ptz.lower.right': '右下',
      'video.player.ptz.right': '右',
      'video.player.ptz.up': '上',
      'video.player.ptz.upper.left': '左上',
      'video.player.ptz.upper.right': '右上',
      'inspec.ptz.position1': '三维定位',
      'video.player.ptz.zoom': '变倍',
      'video.player.ptz.focus': '聚焦',
      'video.player.ptz.halo': '光圈',
      'channel.videoSource.video': '视频',
      'wsPlayer.decode.software': '软解',
      'wsPlayer.decode.hardware': '硬解',
      'wsPlayer.proxy': '代理模式, 此模式不做自动处理',
      'wsPlayer.recording': '本地录像',
      'wsplayer.more': '更多',
      'wsPlayer.fastForward': '快进',
      'wsPlayer.fastBackward': '快退',
      'wsPlayer.request.realtime.preview.interface': '请求实时预览接口',
      'wsPlayer.request.realtime.preview.interface.error.tip':
        '在传入的 {0} 方法上，resolve 返回的值应该为一个icc返回的标准对象',
      'wsPlayer.request.recording.interface': '请求录像接口',
      'wsPlayer.request.talk.interface': '请求对讲接口',
      'wsPlayer.screen.custom.split': '自定义分屏',
      'wsPlayer.screen.four': '四分屏',
      'wsPlayer.screen.full': '全屏',
      'wsPlayer.screen.nine': '九分屏',
      'wsPlayer.screen.one': '单屏',
      'wsPlayer.screen.selfAdaption': '自适应',
      'wsPlayer.screen.split': '{0}分屏',
      'wsPlayer.screen.stretching': '拉伸',
      'wsPlayer.speed.sub': '倍速-',
      'wsPlayer.speed.add': '倍速+',
      'wsPlayer.drawTriangle': '智能规则线',
      'wsPlayer.refreshReplay': '刷新重播',
      'wsPlayer.sound': '音频',
      'wsPlayer.talk': '对讲',
      'wsPlayer.establishTalk': '正在建立对讲...',
      'wsPlayer.talking': '对讲中...',
      'wsPlayer.income.format.error': '传入格式错误，请重新传入',
      'wsPlayer.one.click.off': '一键关闭',
      'wsPlayer.pause': '暂停',
      'wsPlayer.play': '播放',
      'ws.device.awakening': '{0}通道正在唤醒中',
      'wsPlayer.auto.recognition.failed':
        '自动识别失败，存在端口为非默认端口，开始通过参数判断，请注意传参！',
      'wsPlayer.capture': '抓图',
      'wsPlayer.video': '视频',
      'wsPlayer.current.window.cannot.be.merged': '当前窗口无法被合并',
      'wsPlayer.custom.player.max.support.tip': '自定义播放器只支持最大分割{0}个窗口',
      'wsPlayer.drag.tip': '不能拖拽到当前位置，请重新拖拽',
      'wsPlayer.play.error.101': '当前浏览器不支持硬解码',
      'wsPlayer.play.error.102': '硬解码播放失败',
      'wsPlayer.play.error.103': '硬解码播放延时超过8秒',
      'wsPlayer.play.error.104': '硬解码失败',
      'wsPlayer.play.error.201': '当前音频无法播放',
      'wsPlayer.play.error.202': 'websocket连接错误',
      'wsPlayer.play.error.203': '文件播放完成',
      'wsPlayer.play.error.401': '该用户无操作权限',
      'wsPlayer.play.error.404': '请求超时或未找到',
      'wsPlayer.play.error.405': '播放超时',
      'wsPlayer.play.error.406': '视频流类型解析失败，请检查通道配置',
      'wsPlayer.play.error.407': '请求超时',
      'wsPlayer.play.error.408': '请求超时或码流不支持',
      'wsPlayer.play.error.409': '请求超时或码流不支持',
      'wsPlayer.play.error.410': '视频流异常断开',
      'wsPlayer.play.error.411': '录像已播放完成',
      'wsPlayer.play.error.457': '时间设置错误',
      'wsPlayer.play.error.503': 'WS连接地址错误，非当前连接的ICC服务器返回',
      'wsPlayer.play.error.504': '对讲失败',
      'wsPlayer.play.error.701': 'Chrome版本低，请升级到最新的Chrome版本',
      'wsPlayer.play.error.702': 'Firefox版本低，请升级到最新的Firefox版本',
      'wsPlayer.play.error.703': 'Edge版本低，请升级到最新的Edge版本',
      'wsPlayer.play.error.default': '播放失败，请检查配置',
      'wsPlayer.error.101': '所选通道离线，无法播放',
      'wsPlayer.error.102': '登录权限过期，查询实时预览rtsp失败',
      'wsPlayer.error.103': '操作失败，请稍后重试',
      'wsPlayer.error.104': '操作失败，请稍后重试',
      'wsPlayer.error.105': '通道休眠，正在唤醒中，请稍后再试',
      'wsPlayer.error.106': '请传入rtsp地址和websocket地址',
      'wsPlayer.error.107': '当前未传records录像文件, 将无法拖动进度条',
      'wsPlayer.error.108': '请求超时，请稍后重试',
      'wsPlayer.error.201': '所选通道未查询到录像文件',
      'wsPlayer.error.202': '查询录像文件列表失败',
      'wsPlayer.error.203': '查询录像rtsp失败',
      'wsPlayer.error.204': '{0}倍速无法播放音频',
      'wsPlayer.error.205': '通道休眠，正在唤醒中，请稍后再试',
      'wsPlayer.error.206': '当前倍速不支持',
      'wsPlayer.error.301': '正在对讲，无法关闭音频',
      'wsPlayer.error.302': '其他设备对讲中，无法开启音频',
      'wsPlayer.error.303': '其他设备对讲中，无法开启对讲',
      'wsPlayer.error.304': '查询对讲rtsp失败',
      'wsPlayer.error.305': 'http协议不支持对讲',
      'wsPlayer.error.306': '设备对讲失败',
      'wsPlayer.error.307': '不支持PCM音频格式对讲',
      'wsPlayer.error.308': '不支持ARM音频格式对讲',
      'wsPlayer.error.309': '不支持G711u音频格式对讲',
      'wsPlayer.error.310': '不支持G726音频格式对讲',
      'wsPlayer.error.311': '不支持AAC音频格式对讲',
      'wsPlayer.error.312': '不支持G722音频格式对讲',
      'wsPlayer.error.501': '解码库未初始化完成，请稍后播放！',
      'wsPlayer.error.502': '解码库未初始化完成，请稍后对讲！',
      'wsPlayer.error.503': '请检查创建播放器时，播放器容器是否存在',
      'wsPlayer.error.601': '所操作播放器不存在',
      'wsPlayer.error.602': '所选播放器正在本地录像中，不可重复本地录像',
      'wsPlayer.error.603': '所选播放器未播放录像，不可本地录像',
      'wsPlayer.error.604': '所选播放器未开始本地录像，不可操作关闭本地录像',
      'wsPlayer.error.605': '时间跳转播放传参错误',
      'wsPlayer.error.606': '设置自适应拉伸传参错误',
      'wsPlayer.error.607': '实时预览不支持倍速播放',
      'wsPlayer.error.608': '需传入正确的{0}方法：{1}',
      'wsPlayer.error.609': '超出当前设置最大窗口布局数量',
      'wsPlayer.error.610': '操作自定义窗口布局失败：{0}',
      'wsPlayer.error.611': '实时预览不支持播放方法',
      'wsPlayer.error.612': '实时预览不支持暂停方法',
      'wsPlayer.error.613': '实时预览不支持跳转播放',
      'wsPlayer.error.614': '当前窗口未播放实时预览画面, 无法进行对讲',
      'wsPlayer.error.701': '云台被用户{0}锁定，无法操作',
      'wsPlayer.error.702': '控制云台三维定位失败{0}',
      'wsPlayer.error.703': '控制云台{0}{1}失败{2}',
      'wsPlayer.error.704': '控制云台方向失败{0}',
      error2084: '您无权限进行此操作',
      'wsPlayer.error.30002002': '对讲被其他客户端打开，请稍后重试'
    },
    enUS: {
      'common.close': 'Close',
      'common.more': 'More',
      'common.ok': 'OK',
      'common.cancel': 'Cancel',
      'common.reset': 'Reset',
      'storagePlan.stream.main': 'Main Stream',
      'video.player.sub.stream': 'Sub Stream {0}',
      'video.player.ptz.down': 'Down',
      'video.player.ptz.left': 'Left',
      'video.player.ptz.lower.left': 'Left Down',
      'video.player.ptz.lower.right': 'Right Down',
      'video.player.ptz.right': 'Right',
      'video.player.ptz.up': 'Up',
      'video.player.ptz.upper.left': 'Left Up',
      'video.player.ptz.upper.right': 'Right Up',
      'inspec.ptz.position1': '3D Position',
      'video.player.ptz.zoom': 'Zoom',
      'video.player.ptz.focus': 'Focus',
      'video.player.ptz.halo': 'Aperture',
      'channel.videoSource.video': 'Video',
      'wsPlayer.proxy': 'The current proxy pattern cannot perform automatic handling.',
      'wsPlayer.recording': 'Local Record',
      'wsplayer.more': 'More',
      'wsPlayer.request.realtime.preview.interface': 'Request to check the interface in live view.',
      'wsPlayer.request.realtime.preview.interface.error.tip':
        'For the {0} method you pass in, the value returned by resolve should be a standard object returned by ICC.',
      'wsPlayer.request.recording.interface': 'Request the recording interface.',
      'wsPlayer.request.talk.interface': 'Request the intercom interface.',
      'wsPlayer.screen.custom.split': 'Custom Split',
      'wsPlayer.screen.four': 'Quad split screen',
      'wsPlayer.screen.full': 'Full Screen',
      'wsPlayer.screen.nine': 'Nine split screens',
      'wsPlayer.screen.one': 'Single',
      'wsPlayer.screen.selfAdaption': 'Fit',
      'wsPlayer.screen.split': '{0} Windows',
      'wsPlayer.screen.stretching': 'Stretch',
      'wsPlayer.speed.sub': 'Speed-',
      'wsPlayer.speed.add': 'Speed+',
      'wsPlayer.sound': 'Audio',
      'wsPlayer.drawTriangle': 'Smart Guideline Triangle',
      'wsPlayer.refreshReplay': 'refresh Replay',
      'wsPlayer.talk': 'Intercom',
      'wsPlayer.establishTalk': 'Establish Talk...',
      'wsPlayer.talking': 'Talking...',
      'wsPlayer.income.format.error': 'The format is incorrect. Please try again.',
      'wsPlayer.one.click.off': 'Close All',
      'wsPlayer.pause': 'Pause',
      'wsPlayer.play': 'Play',
      'ws.device.awakening': 'Waking up channel {0}...',
      'wsPlayer.auto.recognition.failed':
        'Automatic recognition failed. The existing port is not a default one. Begin to judge by parameters. Please pass in parameters!',
      'wsPlayer.capture': 'Snapshot',
      'wsPlayer.current.window.cannot.be.merged': 'The current window cannot be merged.',
      'wsPlayer.custom.player.max.support.tip':
        'Up to {0} window splits are supported for custom players.',
      'wsPlayer.drag.tip': 'You cannot drop it here. Please try again.',
      'wsPlayer.decode.hardware': 'Hardware Decoding',
      'wsPlayer.decode.software': 'Software Decoding',
      'wsPlayer.play.error.101': 'The browser does not support hardware decoding.',
      'wsPlayer.play.error.102': 'Failed to play video for hardware decoding.',
      'wsPlayer.play.error.103': 'The video for hardware decoding delay is higher than 8 seconds.',
      'wsPlayer.play.error.104': 'Hardware decoding failed.',
      'wsPlayer.play.error.201': 'Failed to play the audio.',
      'wsPlayer.play.error.202': 'Websocket connection error.',
      'wsPlayer.play.error.203': 'File ended.',
      'wsPlayer.play.error.401': 'This user has no operation permission.',
      'wsPlayer.play.error.404': 'Request timed out or no videos were found.',
      'wsPlayer.play.error.405': 'Time out playing the video.',
      'wsPlayer.play.error.406':
        'Failed to parse the video stream type. Please check the channel configuration.',
      'wsPlayer.play.error.407': 'Request timed out.',
      'wsPlayer.play.error.408': 'Request timed out or the stream is not supported.',
      'wsPlayer.play.error.409': 'Request timed out or the stream is not supported.',
      'wsPlayer.play.error.410': 'The video stream abnormally disconnected.',
      'wsPlayer.play.error.411': 'Video ended.',
      'wsPlayer.play.error.457': 'The time was incorrectly set.',
      'wsPlayer.play.error.503':
        'Incorrect WS connection address, not returned from the currently connected ICC server.',
      'wsPlayer.play.error.504': 'Talk failed.',
      'wsPlayer.play.error.701':
        'Your Google Chrome is not up to date. Please update it to the latest version.',
      'wsPlayer.play.error.702':
        'Your Firefox is not up to date. Please update it to the latest version.',
      'wsPlayer.play.error.703':
        'Your Edge is not up to date. Please update it to the latest version.',
      'wsPlayer.play.error.default': 'Failed to play. Please check the configuration.',
      'wsPlayer.error.101': 'Failed to play. The selected channel is offline.',
      'wsPlayer.error.102':
        'Failed to search the RTSP address for the live video. Your login permission has expired.',
      'wsPlayer.error.103': 'Operation failed. Please try again later.',
      'wsPlayer.error.104': 'Operation failed. Please try again later.',
      'wsPlayer.error.105':
        'Waking up the channel which is currently in sleep status...Please try again later.',
      'wsPlayer.error.106': 'Please enter the rtsp address and websocket address',
      'wsPlayer.error.107': 'No records file passed, unable to scrub the timeline',
      'wsPlayer.error.201': 'No video files were found from the selected channel.',
      'wsPlayer.error.202': 'Failed to search for the list of video files.',
      'wsPlayer.error.203': 'Failed to search the RTSP address for videos.',
      'wsPlayer.error.204': 'Failed to play audio in {0}x speed.',
      'wsPlayer.error.205':
        'Waking up the channel which is currently in sleep status...Please try again later.',
      'wsPlayer.error.206': 'Unsupported playback speed',
      'wsPlayer.error.301': 'Audio cannot be disabled when using talk.',
      'wsPlayer.error.302': 'Audio cannot be opened when another device is using talk.',
      'wsPlayer.error.303': 'Talk cannot be enabled when another device is using it.',
      'wsPlayer.error.304': 'Failed to search the RTSP address for talk.',
      'wsPlayer.error.305': 'HTTP protocol does not support talk.',
      'wsPlayer.error.306': 'Failed to talk through the device.',
      'wsPlayer.error.307': 'Talk does not support the PCM audio format.',
      'wsPlayer.error.308': 'Talk does not support the ARM audio format.',
      'wsPlayer.error.309': 'Talk does not support the G711u audio format.',
      'wsPlayer.error.310': 'Talk does not support the G726 audio format.',
      'wsPlayer.error.311': 'Talk does not support the AAC audio format.',
      'wsPlayer.error.312': 'Talk does not support the G722 audio format.',
      'wsPlayer.error.501':
        'The decoding database is not yet fully initialized. Please try again later.',
      'wsPlayer.error.502':
        'The decoding database is not yet fully initialized. Please try again later.',
      'wsPlayer.error.503': 'Please check if the player container exists when you create a player.',
      'wsPlayer.error.601': 'The player that you are trying to use does not exist.',
      'wsPlayer.error.602': 'The selected player has already started to make a local recording.',
      'wsPlayer.error.603':
        'The selected player is not playing videos now. You cannot start the local recording.',
      'wsPlayer.error.604':
        'The selected player is not playing a local recording now. You cannot stop the local recording.',
      'wsPlayer.error.605': 'Error jumping to a specific time in a video.',
      'wsPlayer.error.606': 'Error configuring self-adaptive image scale.',
      'wsPlayer.error.607': 'You cannot control the speed of live video.',
      'wsPlayer.error.608': 'Incorrect {0} method: {1}',
      'wsPlayer.error.609': 'Exceeded the current limit for the number of split windows.',
      'wsPlayer.error.610': 'Failed to customize the layout of the window. {0}',
      'wsPlayer.error.611': 'Real-time preview does not support playback method',
      'wsPlayer.error.612': 'Real-time preview does not support pause method',
      'wsPlayer.error.613': 'Real-time preview does not support jump playback',
      'wsPlayer.error.614':
        'Real-time preview is not playing in the current window, unable to perform intercom.',
      'wsPlayer.error.701': 'Failed to operate. The PTZ was locked by user {0}.',
      'wsPlayer.error.702': 'Failed to control PTZ 3D positioning {0}',
      'wsPlayer.error.703': 'Failed to control PTZ {0}{1}. {2}',
      'wsPlayer.error.704': 'Failed to control the PTZ direction {0}',
      error2084: 'You have no permission to perform this operation.'
    }
  }
  let g = {},
    f = null
  const v = {
    setLocal: function (e) {
      g = S[e]
    },
    setI18n: function (e) {
      f = e
    },
    $t: function (e, t) {
      if (f && f.t && f.t(e, t) !== e) return f.t(e, t)
      if (f && f.$t && f.$t(e, t) !== e) return f.$t(e, t)
      let s = g[e]
      return (
        Array.isArray(t) &&
          t.forEach((e, t) => {
            s = s.replace(`{${t}}`, e)
          }),
        s
      )
    }
  }
  class x extends P {
    constructor(e) {
      super(e),
        (this.currentIndex = e.index),
        (this.wrapperDomId = e.wrapperDomId),
        (this.canvasId = `${this.domId}-livecanvas`),
        (this.ivsCanvasId = `${this.domId}-ivs-livecanvas`),
        (this.cRawCanvasId = `${this.domId}-cRaw-livecanvas`),
        (this.pztCanvasId = `${this.domId}-pzt-livecanvas`),
        (this.videoId = `${this.domId}-liveVideo`),
        (this.type = 'real'),
        (this.options = {}),
        (this.chromeResourceOutOfMemory = !1),
        this.initDom(),
        (this.defaultStatus = WSPlayerJQ.$('.default-status', this.$el)),
        (this.error = WSPlayerJQ.$('.error', this.$el)),
        (this.controller = WSPlayerJQ.$('.player-control', this.$el)),
        this.controller.dblclick((e) => {
          e.stopPropagation()
        }),
        this.initMouseEvent(),
        this.setStatus('created'),
        (this.isFirstInfo = !0)
    }
    getTemplate() {
      return `\n        <div id="${this.domId}" style="visibility: hidden; top: 150%; left: 0; width: 0; height: 0;" class="wsplayer-item wsplayer-item-${this.index} ${0 === this.index ? 'selected' : 'unselected'}">\n            <div class="ws-full-content ws-flex">\n                <canvas id="${this.canvasId}" class="kind-stream-canvas" kind-channel-id="0" width="800" height="600"></canvas>\n                <video id="${this.videoId}" class="kind-stream-canvas" kind-channel-id="0" muted style="display:none" width="800" height="600"></video>\n                <canvas id="${this.ivsCanvasId}" class="kind-stream-canvas" style="position: absolute" kind-channel-id="0" width="800" height="600"></canvas>\n                <div id="${this.cRawCanvasId}" class="kind-stream-canvas cRaw-container" style="position: absolute" kind-channel-id="0" width="800" height="600"></div>\n                <canvas id="${this.pztCanvasId}" class="kind-stream-canvas" style="display: none; position: absolute; z-index: 2;" kind-channel-id="0" width="800" height="600"></canvas>\n            </div>\n            <div class="default-status">\n                <img src="${this.wsPlayer.prefixUrl}/WSPlayer/icon/default.png" alt="">\n            </div>\n            <div class="player-control top-control-bar">\n                <div class="stream">\n                    <div class="select-container">\n                        <div class="select-show select">\n                            <div class="code-stream">${v.$t('storagePlan.stream.main')}</div>\n                            \x3c!-- 下拉箭头 --\x3e\n                            <img src="${this.wsPlayer.prefixUrl}/WSPlayer/icon/spread.png" />\n                        </div>\n                        <div class="stream-type" style="display: none">\n                            <ul class="select-ul">\n                                \x3c!--主码流--\x3e\n                                <li title='${v.$t('storagePlan.stream.main')}' stream-type="1" class="stream-type-item">${v.$t('storagePlan.stream.main')}</li>\n                                \x3c!--辅码流1--\x3e\n                                <li title='${v.$t('video.player.sub.stream', [1])}' stream-type="2" class="stream-type-item">${v.$t('video.player.sub.stream', [1])}</li>\n                                \x3c!--辅码流2--\x3e\n                                <li title='${v.$t('video.player.sub.stream', [2])}' stream-type="3" class="stream-type-item">${v.$t('video.player.sub.stream', [2])}</li>\n                            </ul>\n                        </div>\n                    </div>\n                    <span class="stream-info"></span>\n                </div>\n                <div class="opt-icons">\n                    \x3c!--刷新重播--\x3e\n                    <div class="opt-icon ws-refresh-replay refresh-replay-icon" title='${v.$t('wsPlayer.refreshReplay')}'></div>\n                    \x3c!--智能帧--\x3e\n                    <div class="opt-icon ws-draw-triangle draw-triangle-icon off" title='${v.$t('wsPlayer.drawTriangle')}'></div>\n                    \x3c!--对讲--\x3e\n                    <div class="opt-icon ws-talk talk-icon off" title='${v.$t('wsPlayer.talk')}'></div>\n                    \x3c!--录像--\x3e\n                    <div class="opt-icon ws-record record-icon" title='${v.$t('wsPlayer.recording')}'></div>\n                    \x3c!--声音--\x3e\n                    <div class="opt-icon ws-audio audio-icon off" title='${v.$t('wsPlayer.sound')}'></div>\n                    \x3c!--抓图--\x3e\n                    <div class="opt-icon ws-capture capture-icon" title='${v.$t('wsPlayer.capture')}'></div>\n                    \x3c!--更多--\x3e\n                    <div class="opt-icon ws-more more-icon" title='${v.$t('common.more')}'>\n                        <div class="opt-icons-vertical">\n                            \x3c!--刷新重播--\x3e\n                            <div class="ws-refresh-replay" title='${v.$t('wsPlayer.refreshReplay')}'><div class="opt-icon refresh-replay-icon"></div><div class="ws-icon-tip">${v.$t('wsPlayer.refreshReplay')}</div></div>\n                            \x3c!--智能帧--\x3e\n                            <div class="ws-draw-triangle" title='${v.$t('wsPlayer.drawTriangle')}'><div class="opt-icon draw-triangle-icon off"></div><div class="ws-icon-tip">${v.$t('wsPlayer.drawTriangle')}</div></div>\n                            \x3c!--对讲--\x3e\n                            <div class="ws-talk" title='${v.$t('wsPlayer.talk')}'><div class="opt-icon talk-icon off"></div><div class="ws-icon-tip">${v.$t('wsPlayer.talk')}</div></div>\n                            \x3c!--录像--\x3e\n                            <div class="ws-record" title='${v.$t('wsPlayer.recording')}'><div class="opt-icon record-icon"></div><div class="ws-icon-tip">${v.$t('wsPlayer.recording')}</div></div>\n                            \x3c!--声音--\x3e\n                            <div class="ws-audio" title='${v.$t('wsPlayer.sound')}'><div class="opt-icon audio-icon off"></div><div class="ws-icon-tip">${v.$t('wsPlayer.sound')}</div></div>\n                            \x3c!--抓图--\x3e\n                            <div class="ws-capture" title='${v.$t('wsPlayer.capture')}'><div class="opt-icon capture-icon"></div><div class="ws-icon-tip">${v.$t('wsPlayer.capture')}</div></div>\n                        </div>\n                    </div>\n                    \x3c!--关闭--\x3e\n                    <div class="opt-icon ws-close close-icon" title='${v.$t('common.close')}'></div>\n                </div>\n            </div>\n            \x3c!--对讲中--\x3e\n            <div class="ws-talking"></div>\n            <div class="error">\n                <div class="wsplayer-error-message"></div>\n            </div>\n        </div>\n        `
    }
    initMouseEvent() {
      super.initMouseEvent()
      let e = this
      ;(this.hideTimer = null),
        this.wsPlayer.config.draggable &&
          (this.$el.on('mousedown', (e) => {
            ;(this.wsPlayer.currentDragWindowIndex = this.currentIndex), e.preventDefault()
          }),
          this.$el.on('mouseup', (e) => {
            this.wsPlayer.changeDragWindow(this.currentIndex), e.preventDefault()
          })),
        this.$el.on('mouseenter mousemove', (e) => {
          ;['created', 'closed'].includes(this.status) ||
            this.setDomVisible(
              WSPlayerJQ.$(
                '.player-control',
                WSPlayerJQ.$(`#${this.wrapperDomId}-${this.currentIndex}`)
              ),
              !0
            ),
            ('playing' !== this.status && 'error' !== this.status) ||
              (this.hideTimer && clearTimeout(this.hideTimer))
        }),
        this.$el.on('mouseleave', (e) => {
          this.hideTimer = setTimeout(() => {
            WSPlayerJQ.$('.stream-type', this.$el).hide(),
              this.setDomVisible(
                WSPlayerJQ.$(
                  '.player-control',
                  WSPlayerJQ.$(`#${this.wrapperDomId}-${this.currentIndex}`)
                ),
                !1
              ),
              (this.streamSelectShow = !1),
              this.setDomVisible(WSPlayerJQ.$('.opt-icons-vertical', this.$el), !1),
              (this.showMoreIcon = !1)
          }, 300)
        }),
        (this.streamSelectShow = !1),
        WSPlayerJQ.$('.select', this.$el).click((e) => {
          this.streamSelectShow
            ? (WSPlayerJQ.$('.stream-type', this.$el).hide(), (this.streamSelectShow = !1))
            : (WSPlayerJQ.$('.stream-type', this.$el).show(), (this.streamSelectShow = !0))
        }),
        WSPlayerJQ.$('.stream-type', this.$el).click((t) => {
          let s = t.target.getAttribute('stream-type')
          e.streamType !== s &&
            e.options &&
            ('url' === e.playType
              ? e.wsPlayer.sendMessage('changeStreamType', {
                  channelData: e.options.channelData,
                  streamType: Number(s),
                  selectIndex: e.index
                })
              : e.wsPlayer.changeStreamType(e.options.channelData, s, e.index))
        })
    }
    setStreamType(e) {
      this.streamType = e
      let t = WSPlayerJQ.$('.stream-type .select-ul', this.$el)[0].children[e - 1],
        s = t && WSPlayerJQ.$(t).attr('title')
      WSPlayerJQ.$('.code-stream', this.$el).text(s),
        WSPlayerJQ.$('.code-stream', this.$el).attr('title', s),
        t &&
          WSPlayerJQ.$(t)
            .addClass('stream-type-select')
            .siblings()
            .removeClass('stream-type-select')
    }
    setStatus(e, s) {
      switch (
        (this.wsPlayer.sendMessage('statusChanged', { status: e, windowIndex: this.index }),
        (this.status = e),
        (this.currentWindowPlaying = !1),
        this.status)
      ) {
        case 'created':
        case 'closed':
          this.setDomVisible(this.defaultStatus, !0),
            this.setDomVisible(this.error, !1),
            this.setDomVisible(this.controller, !1),
            (this.videoElem.src = ''),
            WSPlayerJQ.$('.audio-icon', this.$el).removeClass('on').addClass('off')
          break
        case 'loading':
        case 'ready':
        case 'showmsg':
          this.setDomVisible(this.defaultStatus, !1), this.setDomVisible(this.error, !1)
          break
        case 'playing':
        case 'pause':
          ;(self.isclosing = !1),
            (this.currentWindowPlaying = !0),
            this.setDomVisible(this.defaultStatus, !1),
            this.setDomVisible(this.error, !1)
          break
        case 'streamError':
          const { selectIndex: e, channelData: i = {} } = this.options
          this.wsPlayer.sendMessage(
            'realError',
            { selectIndex: e, channelData: { ...i } },
            { code: s.errorCode, msg: t.errorVideoInfo[s.errorCode] }
          ),
            this.close(!0, !1, 'none')
        case 'error':
          ;(self.isclosing = !1),
            this.showMsgInWindow(
              t.errorVideoInfo[s.errorCode]
                ? t.errorVideoInfo[s.errorCode]
                : t.errorVideoInfo.defaultErrorMsg
            )
      }
    }
    init(e) {
      if (this.wsPlayer.config.isDynamicLoadLib && !window.m_nModuleInitialized) {
        let t = setTimeout(() => {
          this.init(e), clearTimeout(t)
        }, 100)
        return
      }
      let t = (this.options || {}).channelId === e.channelId
      ;(this.options = e),
        this.playSDK &&
          (this.isAudioPlay &&
            WSPlayerJQ.$('.audio-icon', this.$el).removeClass('on').addClass('off'),
          this.close(!0, t)),
        this.setLoading(!0, 'ready'),
        this.setStreamType(e.streamType),
        this.createPlayer(e)
    }
    startPlay(e, t) {
      'video' === t.decodeMode
        ? ((this.videoElem.style.display = ''), (this.canvasElem.style.display = 'none'))
        : ((this.videoElem.style.display = 'none'), (this.canvasElem.style.display = '')),
        this.customDomElem && this.customDomElem.css('display', ''),
        this.updateAdapter(e.playerAdapter, t),
        (this.width = t.width),
        (this.height = t.height),
        WSPlayerJQ.$('.stream-info', this.$el).text(
          `${t.encodeMode ? `${t.encodeMode}, ` : ''}${t.width ? `${t.width}*` : ''}${t.height ? t.height : ''}${'video' === t.decodeMode ? `, ${v.$t('wsPlayer.decode.hardware')}` : `, ${v.$t('wsPlayer.decode.software')}`}`
        )
    }
    createPlayer(e) {
      if (this.chromeResourceOutOfMemory)
        return (
          this.wsPlayer.close(this.currentIndex),
          console.warn('浏览器资源不足, 请等待3-5秒后重试'),
          this.wsPlayer.sendMessage('chromeMemoryOut', {
            message: '浏览器资源不足, 请等待3-5秒后重试'
          })
        )
      const {
        onlyLoadSingleLib: s,
        useH264MSE: i,
        useH265MSE: r,
        useNginxProxy: a,
        openIvs: l,
        ivsTypeArr: n
      } = this.wsPlayer.config
      let o = this
      if (this.isclosing) return void (this.isclosing = !1)
      ;(this.playSDK = new window.PlaySDKInterface(!s)),
        this.setLogLevel(),
        this.playSDK.SetCallBack('StreamRedirect', function (t) {
          let s = ''
          if (this.wsPlayer.setWSUrl) s = this.wsPlayer.setRedirectWSUrl()
          else if (a) {
            let i = (s = t.split('rtsp://')[1].split('/')[0])
            s = e.wsURL.split('serverIp=')[0] + 'serverIp=' + i
          } else s = e.wsURL.split('://')[0] + t.split('rtsp://')[1].split('/')[0]
          o.playSDK.StartPullStream({
            strRtspvUri: s,
            strRtspvUrl: t,
            strSourceId: '',
            bTalkService: !1,
            nRange: 0
          })
        }),
        this.playSDK.SetCallBack('GetPlayPort', (e) => {
          if ((1 == localStorage.WSPlayerLogLevel && console.log('浏览器资源nPort', e), e > 160)) {
            ;(o.chromeResourceOutOfMemory = !0),
              console.warn('浏览器资源不足, 请等待3-5秒后重试'),
              o.wsPlayer.sendMessage('chromeMemoryOut', {
                message: '浏览器资源不足, 请等待3-5秒后重试'
              })
            let e = setTimeout(() => {
              o.wsPlayer.close(o.currentIndex), clearTimeout(e)
            }, 10)
            setTimeout(() => {
              o.chromeResourceOutOfMemory = !1
            }, 3e3)
          } else
            (o.playSDK.nPlayPort = e),
              window.wsPlayerManager.bindPlayer(o.playSDK.nPlayPort, o.playSDK)
        }),
        this.playSDK.SetCallBack('PlayStart', (t) => {
          console.log('PlayStart时间', t),
            this.closeCRaw(),
            o.startPlay(e, t),
            (o.talkWSUrl = e.wsURL),
            o.setLoading(!1, 'playing'),
            WSPlayerJQ.$('.ws-draw-triangle', o.$el)
              .removeClass(l ? 'off' : 'on')
              .addClass(l ? 'on' : 'off'),
            l
              ? (o.playSDK.OpenIVS(o.initIndex),
                this.playSDK.SetIvsLanguageEnvi('en' === localStorage.getItem('language') ? 1 : 0),
                n.includes(1) || o.playSDK.SetIvsEnable(3, 0),
                n.includes(2) || (o.playSDK.SetIvsEnable(1, 0), o.playSDK.SetIvsEnable(14, 0)))
              : o.playSDK.CloseIVS(),
            o.wsPlayer.sendMessage('realSuccess', {
              channelData: { ...(e.channelData || {}) },
              selectIndex: e.selectIndex,
              customDomId: o.customDomId
            })
        }),
        this.playSDK.SetCallBack('DecodeStart', (e) => {}),
        this.playSDK.SetCallBack('Disconnect', () => {
          ;(o.isclosing = !1),
            o.isError
              ? o.setLoading(!1, 'closed')
              : o.setStatus('streamError', {
                  errorCode: '410',
                  description: 'Video Stream Abnormality'
                })
        }),
        this.playSDK.SetCallBack('VideoFrameInfo', (e) => {
          o.wsPlayer.sendMessage('getVideoFrameInfo', {
            channelData: o.options.channelData,
            selectIndex: o.index,
            audioFrameInfo: e
          })
        }),
        this.playSDK.SetCallBack('AudioFrameInfo', (e) => {
          o.wsPlayer.sendMessage('getAudioFrameInfo', {
            channelData: o.options.channelData,
            selectIndex: o.index,
            audioFrameInfo: e
          })
        }),
        this.playSDK.SetCallBack('CapturePicDataCallBack', (e) => {
          o.wsPlayer.sendMessage('picCap', {
            channelData: o.options.channelData,
            selectIndex: o.index,
            base64Img: o.currentWindowPlaying ? this.uint8ArrayToBase64(e) : ''
          })
        }),
        this.playSDK.SetCallBack('IVSDrawData', (e, s, i, r, a) => {
          if (5 === Number(i)) {
            let e = new Uint8Array(s),
              i = new TextDecoder('utf-8').decode(e),
              r = JSON.parse(i)
            if (r.Events) {
              if (!r.Events[0].Data) return
              let e = r.Events[0].Data.RealtimeData,
                { curCRawMap: s, curInfoMap: i } = this.getCRawArr(e)
              if (o.isFirstInfo) {
                console.log('第一次接收'), (o.LastCRawMap = s), (o.LastCRawInfo = i)
                let r = o.cRawCanvasElem.clientWidth,
                  a = o.cRawCanvasElem.clientHeight,
                  l = document.createDocumentFragment(),
                  n = $('#' + o.cRawCanvasId)
                for (let s = 0; s < e.length; s++) {
                  const i = e[s]
                  if (i) {
                    let { DeviceName: e, Text: s, Rect: n } = i,
                      c = (n[0] * r) / t.transParam,
                      d = (n[1] * a) / t.transParam,
                      h = o.LastCRawMap[e],
                      p = o.getCRawTemplate(!1, s, e, h, c, d, i.AlarmEnable),
                      y = document.createRange().createContextualFragment(p)
                    l.appendChild(y)
                  }
                }
                n.append(l)
              } else {
                let e = Object.keys(o.LastCRawMap),
                  t = Object.keys(s)
                o.handleCRawArrs(e, t, i)
              }
              o.isFirstInfo && (o.isFirstInfo = !1)
            }
          }
        }),
        this.playSDK.SetCallBack('Error', (s) => {
          o.playSDK &&
            ![101, 102, 103, 104, 204].includes(Number(s.errorCode)) &&
            ((o.isError = !0),
            o.setLoading(!1),
            console.log('Error: ' + JSON.stringify(s)),
            o.setStatus('error', s),
            o.wsPlayer.sendMessage(
              'realError',
              { selectIndex: e.selectIndex, channelData: { ...(e.channelData || {}) } },
              { code: s.errorCode, msg: t.errorVideoInfo[s.errorCode] }
            ))
        }),
        this.playSDK.Init(
          {
            canvasElem: this.canvasElem,
            videoElem: this.videoElem,
            ivsCanvasElem: this.ivsCanvasElem,
            bPlayBack: 0,
            strDecodeFilePath: `${this.wsPlayer.prefixUrl}/WSPlayer/singleThread`
          },
          null,
          this.wsPlayer.config.isWebView
        )
      const { platform: c } = navigator
      0 == c.indexOf('iPhone')
        ? this.playSDK.SetDecodeMode(!1, !1)
        : this.playSDK.SetDecodeMode(i, r),
        this.setCacheMode(),
        (this.playSDK.m_rtspvHandle = this.playSDK.StartPullStream({
          strRtspvUri: e.wsURL,
          strRtspvUrl: e.rtspURL,
          strSourceId: '',
          bTalkService: !1,
          nRange: 0,
          nShortTimeout: this.wsPlayer.rtspResponseTimeout || 8,
          nRtspResponseTimeout: this.wsPlayer.rtspResponseTimeout + 2 || 10
        })),
        null != this.playSDK.m_rtspvHandle &&
          window.wsPlayerManager.bindHandlePlayer(this.playSDK.m_rtspvHandle, this.playSDK)
    }
    startTalk(e) {
      if (this.wsPlayer.config.isDynamicLoadLib && !window.m_nModuleInitialized)
        return void this.wsPlayer.sendErrorMessage('502')
      ;(this.wsPlayer.isTalking = !0),
        (this.isTalking = !0),
        WSPlayerJQ.$('.talk-icon', this.$el).removeClass('off').addClass('on')
      let s = this
      ;(this.talkData = e.talkData),
        (this.talkPlaySDK = new window.PlaySDKInterface(!this.wsPlayer.config.onlyLoadSingleLib)),
        this.setLogLevel(),
        this.talkPlaySDK.SetCallBack('GetPlayPort', (e) => {
          ;(this.talkPlaySDK.nPlayPort = e),
            window.wsPlayerManager.bindPlayer(this.talkPlaySDK.nPlayPort, this.talkPlaySDK)
        }),
        this.talkPlaySDK.SetCallBack('TalkSuccess', (t) => {
          WSPlayerJQ.$('.ws-talking', this.$el).html(`${v.$t('wsPlayer.talking')}`),
            s.wsPlayer.sendMessage('talkSuccess', {
              selectIndex: e.selectIndex,
              channelData: { ...(this.options.channelData || {}) }
            })
        }),
        this.talkPlaySDK.SetCallBack('Error', (i) => {
          s.wsPlayer.sendMessage(
            'talkError',
            { selectIndex: e.selectIndex, channelData: { ...(this.options.channelData || {}) } },
            { code: i.errorCode, msg: t.errorVideoInfo[i.errorCode] }
          ),
            s.stopTalk()
        }),
        this.talkPlaySDK.SetCallBack('Disconnect', (e) => {
          s.stopTalk()
        }),
        this.talkPlaySDK.Init({
          canvasElem: null,
          videoElem: null,
          ivsCanvasElem: null,
          bPlayBack: 0,
          strDecodeFilePath: `${this.wsPlayer.prefixUrl}/WSPlayer/singleThread`
        })
      let i = this.talkPlaySDK.StartPullStream({
        strRtspvUri: e.wsURL || this.talkWSUrl,
        strRtspvUrl: e.rtspURL,
        strSourceId: '',
        bTalkService: !0,
        nRange: 0,
        nShortTimeout: this.wsPlayer.rtspResponseTimeout || 8,
        nRtspResponseTimeout: this.wsPlayer.rtspResponseTimeout + 2 || 10,
        bBroadcast: Boolean(e.gbDevice)
      })
      null != i && window.wsPlayerManager.bindHandlePlayer(i, this.talkPlaySDK),
        '4' === e.talkData.audioType
          ? this.talkPlaySDK.StartTalk(1)
          : this.talkPlaySDK.StartTalk(0),
        window.wsPlayerManager.bindPlayer(this.talkPlaySDK.nPlayPort, this.talkPlaySDK),
        WSPlayerJQ.$('.ws-talking', this.$el).html(`${v.$t('wsPlayer.establishTalk')}`),
        WSPlayerJQ.$('.ws-talking', this.$el).css({ visibility: 'visible' }),
        this.openVolume()
    }
    talkByUrl(e) {
      if (this.wsPlayer.config.isDynamicLoadLib && !window.m_nModuleInitialized)
        return void this.wsPlayer.sendErrorMessage('502')
      if (!this.currentWindowPlaying) return void this.wsPlayer.sendErrorMessage('614')
      if (![2, 4].includes(Number(e.audioType))) {
        let t = { 1: 307, 3: 308, 4: 309, 5: 310, 8: 311, 101: 312 }
        return void this.wsPlayer.sendErrorMessage(t[e.audioType], { channelList: [e] })
      }
      ;(this.wsPlayer.isTalking = !0),
        (this.isTalking = !0),
        WSPlayerJQ.$('.talk-icon', this.$el).removeClass('off').addClass('on')
      let s = this
      ;(this.talkPlaySDK = new window.PlaySDKInterface(!this.wsPlayer.config.onlyLoadSingleLib)),
        this.setLogLevel(),
        this.talkPlaySDK.SetCallBack('GetPlayPort', (e) => {
          ;(s.talkPlaySDK.nPlayPort = e),
            window.wsPlayerManager.bindPlayer(s.talkPlaySDK.nPlayPort, s.talkPlaySDK)
        }),
        this.talkPlaySDK.SetCallBack('Error', (i) => {
          s.wsPlayer.sendMessage(
            'talkError',
            { selectIndex: e.selectIndex },
            { code: i.errorCode, msg: t.errorVideoInfo[i.errorCode] }
          ),
            s.stopTalk()
        }),
        this.talkPlaySDK.Init({
          canvasElem: null,
          videoElem: null,
          ivsCanvasElem: null,
          bPlayBack: 0,
          strDecodeFilePath: `${this.wsPlayer.prefixUrl}/WSPlayer/singleThread`
        })
      let i = this.talkPlaySDK.StartPullStream({
        strRtspvUri: e.wsURL,
        strRtspvUrl: e.rtspURL + '&trackID=501',
        strSourceId: '',
        bTalkService: !0,
        nRange: 0,
        nShortTimeout: this.wsPlayer.rtspResponseTimeout || 8,
        nRtspResponseTimeout: this.wsPlayer.rtspResponseTimeout + 2 || 10,
        bBroadcast: Boolean(e.gbDevice)
      })
      null != i && window.wsPlayerManager.bindHandlePlayer(i, this.talkPlaySDK),
        '4' === String(e.audioType) ? this.talkPlaySDK.StartTalk(1) : this.talkPlaySDK.StartTalk(0),
        window.wsPlayerManager.bindPlayer(this.talkPlaySDK.nPlayPort, this.talkPlaySDK),
        WSPlayerJQ.$('.ws-talking', this.$el).css({ visibility: 'visible' }),
        this.openVolume()
    }
    stopTalk(e) {
      if (
        (this.talkPlaySDK && window.wsPlayerManager.unbindPlayer(this.talkPlaySDK.nPlayPort),
        'url' !== e && this.talkData && this.wsPlayer.stopTalk(this.talkData),
        this.isTalking && ((this.wsPlayer.isTalking = !1), (this.isTalking = !1)),
        this.talkPlaySDK &&
          (this.talkPlaySDK.StopTalk(),
          this.talkPlaySDK.StopPullStream(),
          (this.talkPlaySDK = null)),
        WSPlayerJQ.$('.talk-icon', this.$el).removeClass('on').addClass('off'),
        WSPlayerJQ.$('.ws-talking', this.$el).css({ visibility: 'hidden' }),
        this.closeVolume(),
        this.options && 'url' === this.options.playType)
      ) {
        const { selectIndex: e, channelData: t = {} } = this.options
        this.wsPlayer.sendMessage('stopTalk', { selectIndex: e, channelData: { ...t } })
      }
    }
  }
  class I extends P {
    constructor(e) {
      super(e),
        (this.currentIndex = e.index),
        (this.wrapperDomId = e.wrapperDomId),
        (this.speed = 1),
        (this.canvasId = `${this.domId}-recordcanvas`),
        (this.ivsCanvasId = `${this.domId}-ivs-livecanvas`),
        (this.cRawCanvasId = `${this.domId}-cRaw-livecanvas`),
        (this.videoId = `${this.domId}-recordVideo`),
        (this.curTimestamp = 0),
        (this.type = 'record'),
        this.initDom(),
        (this.defaultStatus = WSPlayerJQ.$('.default-status', this.$el)),
        (this.error = WSPlayerJQ.$('.error', this.$el)),
        (this.controller = WSPlayerJQ.$('.player-control', this.$el)),
        (this.timeInfo = WSPlayerJQ.$('.time-info', this.$el)),
        (this.chromeResourceOutOfMemory = !1),
        this.initMouseEvent(),
        this.setStatus('created'),
        (this.isFirstInfo = !0)
    }
    getTemplate() {
      return `\n        <div id="${this.domId}" style="visibility: hidden; top: 150%; left: 0; width: 0; height: 0;" class="wsplayer-item wsplayer-item-${this.index} ${0 === this.index ? 'selected' : 'unselected'}">\n            <canvas id="${this.canvasId}" class="kind-stream-canvas" kind-channel-id="0" width="800" height="600"></canvas>\n            <video id="${this.videoId}" class="kind-stream-canvas" kind-channel-id="0" muted style="display:none" width="800" height="600"></video>\n            <canvas id="${this.ivsCanvasId}" class="kind-stream-canvas" style="position: absolute" kind-channel-id="0" width="800" height="600"></canvas>\n            <div id="${this.cRawCanvasId}" class="kind-stream-canvas cRaw-container" style="position: absolute" kind-channel-id="0" width="800" height="600"></div>\n            <div class="default-status">\n                <img src="${this.wsPlayer.prefixUrl}/WSPlayer/icon/default.png" alt="">\n            </div>\n            <div class="player-control top-control-bar">\n                <span class="stream-info"></span>\n                <div class="opt-icons">\n                    \x3c!--刷新重播--\x3e\n                    <div class="opt-icon ws-refresh-replay refresh-replay-icon" title='${v.$t('wsPlayer.refreshReplay')}'></div>\n                    \x3c!--智能帧--\x3e\n                    <div class="opt-icon ws-draw-triangle draw-triangle-icon off" title='${v.$t('wsPlayer.drawTriangle')}'></div>\n                    \x3c!--录像--\x3e\n                    <div class="opt-icon ws-record record-icon" title='${v.$t('wsPlayer.recording')}'></div>\n                    \x3c!--声音--\x3e\n                    <div class="opt-icon ws-audio audio-icon off" title='${v.$t('wsPlayer.sound')}'></div>\n                    \x3c!--抓图--\x3e\n                    <div class="opt-icon ws-capture capture-icon" title='${v.$t('wsPlayer.capture')}'></div>\n                    \x3c!--更多--\x3e\n                    <div class="opt-icon ws-more more-icon" title='${v.$t('common.more')}'>\n                        <div class="opt-icons-vertical">\n                            \x3c!--刷新重播--\x3e\n                            <div class="ws-refresh-replay" title='${v.$t('wsPlayer.refreshReplay')}'><div class="opt-icon refresh-replay-icon"></div><div class="ws-icon-tip">${v.$t('wsPlayer.refreshReplay')}</div></div>\n                            \x3c!--智能帧--\x3e\n                            <div class="ws-draw-triangle" title='${v.$t('wsPlayer.drawTriangle')}'><div class="opt-icon draw-triangle-icon off"></div><div class="ws-icon-tip">${v.$t('wsPlayer.drawTriangle')}</div></div>\n                            \x3c!--录像--\x3e\n                            <div class="ws-record" title='${v.$t('wsPlayer.recording')}'><div class="opt-icon record-icon"></div><div class="ws-icon-tip">${v.$t('wsPlayer.recording')}</div></div>\n                            \x3c!--声音--\x3e\n                            <div class="ws-audio" title='${v.$t('wsPlayer.sound')}'><div class="opt-icon audio-icon off"></div><div class="ws-icon-tip">${v.$t('wsPlayer.sound')}</div></div>\n                            \x3c!--抓图--\x3e\n                            <div class="ws-capture" title='${v.$t('wsPlayer.capture')}'><div class="opt-icon capture-icon"></div><div class="ws-icon-tip">${v.$t('wsPlayer.capture')}</div></div>\n                        </div>\n                    </div>\n                    \x3c!--关闭--\x3e\n                    <div class="ws-close opt-icon close-icon" title='${v.$t('common.close')}'></div>\n                </div>\n            </div>\n            <div class="player-control record-control-bar">\n                <div class="wsplayer-progress-bar">\n                    <div class="progress-bar_background"></div>\n                    <div class="progress-bar_hover_light"></div>\n                    <div class="progress-bar_light"></div>\n                </div>\n                <div class="record-control-left">\n                    <div class="opt-icon play-ctrl-btn play-icon play"></div>\n                    <div class="time-info"></div>/<div class="time-long"></div>\n                </div>\n                <div class="record-control-right">\n                    <div class="opt-icon close-icon"></div>\n                </div>\n            </div>\n            <div class="error">\n                <div class="wsplayer-error-message"></div>\n            </div>\n            <div class="play-pause-wrapper">\n                <div class="play-ctrl-btn center-play-icon"></div>\n            </div>\n        </div>\n        `
    }
    initMouseEvent() {
      super.initMouseEvent(),
        (this.hideTimer = null),
        this.wsPlayer.config.draggable &&
          (this.$el.on('mousedown', (e) => {
            ;(this.wsPlayer.currentDragWindowIndex = this.currentIndex), e.preventDefault()
          }),
          this.$el.on('mouseup', (e) => {
            this.wsPlayer.changeDragWindow(this.currentIndex), e.preventDefault()
          })),
        this.$el.on('mouseenter mousemove', (e) => {
          ;['created', 'closed'].includes(this.status) ||
            this.setDomVisible(
              WSPlayerJQ.$(
                '.player-control',
                WSPlayerJQ.$(`#${this.wrapperDomId}-${this.currentIndex}`)
              ),
              !0
            ),
            'playing' === this.status
              ? this.hideTimer && clearTimeout(this.hideTimer)
              : 'ready' === this.status && this.setDomVisible(this.progressBar, !0)
        }),
        this.$el.on('mouseleave', (e) => {
          'pause' !== this.status &&
            (this.hideTimer = setTimeout(() => {
              this.setDomVisible(
                WSPlayerJQ.$(
                  '.player-control',
                  WSPlayerJQ.$(`#${this.wrapperDomId}-${this.currentIndex}`)
                ),
                !1
              ),
                this.setDomVisible(WSPlayerJQ.$('.opt-icons-vertical', this.$el), !1),
                (this.showMoreIcon = !1)
            }, 300))
        }),
        WSPlayerJQ.$('.wsplayer-progress-bar', this.$el).on('mousemove', (e) => {
          WSPlayerJQ.$('.progress-bar_hover_light', this.$el).css({ width: e.offsetX + 'px' })
        }),
        WSPlayerJQ.$('.wsplayer-progress-bar', this.$el).on('mouseleave', (e) => {
          WSPlayerJQ.$('.progress-bar_hover_light', this.$el).css({ width: 0 })
        }),
        WSPlayerJQ.$('.play-ctrl-btn', this.$el).click((e) => {
          'playing' === this.status
            ? (this.pause(),
              WSPlayerJQ.$('.play-icon', this.$el).removeClass('play').addClass('pause'))
            : (this.play(),
              WSPlayerJQ.$('.play-icon', this.$el).removeClass('pause').addClass('play'))
        })
    }
    setStatus(e, s) {
      switch (
        (this.wsPlayer.sendMessage('statusChanged', { status: e, windowIndex: this.index }),
        (this.status = e),
        (this.currentWindowPlaying = !1),
        this.status)
      ) {
        case 'created':
        case 'closed':
          this.setDomVisible(this.defaultStatus, !0),
            this.setDomVisible(this.error, !1),
            this.setDomVisible(this.controller, !1),
            (this.isAudioPlay = !1),
            WSPlayerJQ.$('.audio-icon', this.$el).removeClass('on').addClass('off')
          break
        case 'ready':
          this.setDomVisible(this.defaultStatus, !1), this.setDomVisible(this.error, !1)
          break
        case 'playing':
          ;(this.currentWindowPlaying = !0),
            this.setDomVisible(this.defaultStatus, !1),
            this.setDomVisible(this.error, !1),
            this.setDomVisible(WSPlayerJQ.$('.play-pause-wrapper', this.$el), !1)
          break
        case 'pause':
          ;(this.currentWindowPlaying = !0),
            this.setDomVisible(this.defaultStatus, !1),
            this.setDomVisible(this.error, !1),
            this.setDomVisible(this.controller, !1),
            this.setDomVisible(WSPlayerJQ.$('.play-pause-wrapper', this.$el), !0)
          break
        case 'streamError':
          setTimeout(() => this.close(!0, !1, 'none'), 0)
          const { selectIndex: e, channelData: i = {} } = this.options
          '411' === s.errorCode
            ? this.wsPlayer.sendMessage('recordFinish', { selectIndex: e, channelData: { ...i } })
            : this.wsPlayer.sendMessage(
                'recordError',
                { selectIndex: e, channelData: { ...i } },
                { code: s.errorCode, msg: t.errorVideoInfo[s.errorCode] }
              )
        case 'error':
          this.showMsgInWindow(
            t.errorVideoInfo[s.errorCode]
              ? t.errorVideoInfo[s.errorCode]
              : t.errorVideoInfo.defaultErrorMsg
          )
      }
    }
    init(e) {
      if (!this.wsPlayer.config.isDynamicLoadLib || window.m_nModuleInitialized)
        (this.options = e),
          this.playSDK &&
            (this.isAudioPlay &&
              WSPlayerJQ.$('.audio-icon', this.$el).removeClass('on').addClass('off'),
            this.close(!0)),
          this.setLoading(!0),
          this.createPlayer(e)
      else {
        let t = setTimeout(() => {
          this.init(e), clearTimeout(t)
        }, 100)
      }
    }
    createPlayer(e) {
      if (this.chromeResourceOutOfMemory)
        return (
          this.wsPlayer.close(this.currentIndex),
          console.warn('浏览器资源不足, 请等待3-5秒后重试'),
          this.wsPlayer.sendMessage('chromeMemoryOut', {
            message: '浏览器资源不足, 请等待3-5秒后重试'
          })
        )
      let s = this
      const {
        useH264MSE: i,
        useH265MSE: r,
        onlyLoadSingleLib: a,
        useNginxProxy: l,
        ivsTypeArr: n,
        openIvs: o
      } = this.wsPlayer.config
      ;(this.playSDK = new window.PlaySDKInterface(!a)),
        this.setLogLevel(),
        this.playSDK.SetCallBack('StreamRedirect', function (t) {
          let i = ''
          if (this.wsPlayer.setWSUrl) i = this.wsPlayer.setRedirectWSUrl(e.wsURL, t)
          else if (l) {
            let s = (i = t.split('rtsp://')[1].split('/')[0])
            i = e.wsURL.split('serverIp=')[0] + 'serverIp=' + s
          } else i = e.wsURL.split('://')[0] + t.split('rtsp://')[1].split('/')[0]
          s.playSDK.StartPullStream({
            strRtspvUri: i,
            strRtspvUrl: t,
            strSourceId: '',
            bTalkService: !1,
            nRange: 0,
            nShortTimeout: rtspResponseTimeout || 8,
            nRtspResponseTimeout: rtspResponseTimeout + 2 || 10
          })
        }),
        this.playSDK.SetCallBack('GetPlayPort', (e) => {
          if ((1 == localStorage.WSPlayerLogLevel && console.log('浏览器资源nPort', e), e > 160)) {
            ;(s.chromeResourceOutOfMemory = !0),
              console.warn('浏览器资源不足, 请等待3-5秒后重试'),
              s.wsPlayer.sendMessage('chromeMemoryOut', {
                message: '浏览器资源不足, 请等待3-5秒后重试'
              })
            let e = setTimeout(() => {
              s.wsPlayer.close(s.currentIndex), clearTimeout(e)
            }, 10)
            setTimeout(() => {
              s.chromeResourceOutOfMemory = !1
            }, 3e3)
          } else
            (this.playSDK.nPlayPort = e),
              window.wsPlayerManager.bindPlayer(this.playSDK.nPlayPort, this.playSDK)
        }),
        this.playSDK.SetCallBack('PlayStart', (t) => {
          console.log('PlayStart', t),
            s.setLoading(!1, 'playing'),
            'video' === t.decodeMode
              ? ((s.videoElem.style.display = ''), (s.canvasElem.style.display = 'none'))
              : ((s.videoElem.style.display = 'none'), (s.canvasElem.style.display = '')),
            this.customDomElem && this.customDomElem.css('display', ''),
            s.updateAdapter(e.playerAdapter, t),
            WSPlayerJQ.$('.stream-info', s.$el).text(
              `${t.encodeMode ? `${t.encodeMode}, ` : ''}${t.width ? `${t.width}*` : ''}${t.height ? t.height : ''}${'video' === t.decodeMode ? `, ${v.$t('wsPlayer.decode.hardware')}` : `, ${v.$t('wsPlayer.decode.software')}`}`
            ),
            s.wsPlayer.selectIndex === s.index &&
              (WSPlayerJQ.$('.ws-record-play', s.wsPlayer.$el).css({ display: 'none' }),
              WSPlayerJQ.$('.ws-record-pause', s.wsPlayer.$el).css({ display: 'block' })),
            this.closeCRaw(),
            (this.isFirstInfo = !0),
            WSPlayerJQ.$('.ws-draw-triangle', s.$el)
              .removeClass(o ? 'off' : 'on')
              .addClass(o ? 'on' : 'off'),
            o
              ? (s.playSDK.OpenIVS(s.initIndex),
                this.playSDK.SetIvsLanguageEnvi('en' === localStorage.getItem('language') ? 1 : 0),
                n.includes(1) || s.playSDK.SetIvsEnable(3, 0),
                n.includes(2) || (s.playSDK.SetIvsEnable(1, 0), s.playSDK.SetIvsEnable(14, 0)))
              : s.playSDK.CloseIVS(),
            s.wsPlayer.sendMessage('recordSuccess', {
              selectIndex: e.selectIndex,
              channelData: { ...(e.channelData || {}) },
              customDomId: s.customDomId
            })
        }),
        this.playSDK.SetCallBack('DecodeStart', (e) => {
          s.DecodeStart &&
            s.wsPlayer.config.playCenterRecordByTime &&
            (s.DecodeStart(), (s.DecodeStart = null))
        }),
        this.playSDK.SetCallBack('Disconnect', () => {
          s.isError
            ? s.setLoading(!1, 'closed')
            : s.setStatus('streamError', {
                errorCode: '410',
                description: 'Video Stream Abnormality'
              })
        }),
        this.playSDK.SetCallBack('VideoFrameInfo', (e) => {
          'playing' === s.status &&
            s.wsPlayer.__setPlayingTime(
              s.index,
              e.nYear,
              e.nMonth,
              e.nDay,
              e.nHour,
              e.nMinute,
              e.nSecond
            ),
            s.wsPlayer.sendMessage('getVideoFrameInfo', {
              channelData: s.options.channelData,
              selectIndex: s.index,
              videoFrameInfo: e,
              timeStamp:
                new Date(
                  `${e.nYear}-${e.nMonth}-${e.nDay} ${e.nHour}:${e.nMinute}:${e.nSecond}`
                ).getTime() / 1e3
            })
        }),
        this.playSDK.SetCallBack('AudioFrameInfo', (e) => {
          s.wsPlayer.sendMessage('getAudioFrameInfo', {
            channelData: s.options.channelData,
            selectIndex: s.index,
            audioFrameInfo: e
          })
        }),
        this.playSDK.SetCallBack(
          'CapturePicDataCallBack',
          (e) => (
            s.wsPlayer.sendMessage('picCap', {
              channelData: s.options.channelData,
              selectIndex: s.index,
              base64Img: s.currentWindowPlaying ? this.uint8ArrayToBase64(e) : ''
            }),
            !1
          )
        ),
        this.playSDK.SetCallBack('PlayBackStreamRange', (t) => {
          this.options && 'url' === e.playType && s.wsPlayer.setTimeLine(e.records),
            s.wsPlayer.sendMessage('getPlayBackStreamRange', {
              channelData: s.options.channelData,
              selectIndex: s.index,
              recordRange: t
            })
        }),
        this.playSDK.SetCallBack('StreamPlayOver', (e) => {
          if ((console.log('回放播放完成', e), s.options.playRecordByFile))
            s.wsPlayer.playNextRecordByFile(s.index, () => {
              s.setStatus('streamError', { errorCode: '411', description: 'Record Finished' })
            })
          else {
            if (!s.options.playRecordByTime) {
              let e = '',
                t = s.options.ssId,
                i = s.options.ssIdList || []
              if ((t && (e = i[i.indexOf(t) + 1]), e))
                return void s.wsPlayer.playNextRecord(s.index, e)
            }
            s.setStatus('streamError', { errorCode: '411', description: 'Record Finished' })
          }
        }),
        this.playSDK.SetCallBack('IVSDrawData', (e, s, i, r, a) => {
          if (5 === Number(i)) {
            let e = new Uint8Array(s),
              i = new TextDecoder('utf-8').decode(e),
              r = JSON.parse(i)
            if (r.Events) {
              if (!r.Events[0].Data) return
              let e = r.Events[0].Data.RealtimeData.filter((e) => null !== e),
                { curCRawMap: s, curInfoMap: i } = this.getCRawArr(e)
              if (this.isFirstInfo) {
                ;(this.LastCRawMap = s), (this.LastCRawInfo = i)
                let r = this.cRawCanvasElem.width,
                  a = this.cRawCanvasElem.height,
                  l = document.createDocumentFragment(),
                  n = $('#' + this.cRawCanvasId)
                for (let s = 0; s < e.length; s++) {
                  const i = e[s]
                  if (i) {
                    let { DeviceName: e, Text: s, Rect: n } = i,
                      o = (n[0] * r) / t.transParam,
                      c = (n[1] * a) / t.transParam,
                      d = this.LastCRawMap[e],
                      h = this.getCRawTemplate(!1, s, e, d, o, c, i.AlarmEnable),
                      p = document.createRange().createContextualFragment(h)
                    l.appendChild(p)
                  }
                }
                n.append(l)
              } else {
                let e = Object.keys(this.LastCRawMap),
                  t = Object.keys(s)
                this.handleCRawArrs(e, t, i)
              }
              this.isFirstInfo && (this.isFirstInfo = !1)
            }
          }
        }),
        this.playSDK.SetCallBack('Error', (i) => {
          if (s.playSDK) {
            if (((s.isError = !0), [101, 102, 103, 104, 204, 408].includes(Number(i.errorCode))))
              return
            s.setLoading(!1),
              console.log('Error: ' + JSON.stringify(i)),
              s.setStatus('error', i),
              s.wsPlayer.sendMessage(
                'recordError',
                { selectIndex: e.selectIndex, channelData: { ...(e.channelData || {}) } },
                { code: i.errorCode, msg: t.errorVideoInfo[i.errorCode] }
              )
          }
        }),
        this.playSDK.Init(
          {
            canvasElem: this.canvasElem,
            videoElem: this.videoElem,
            ivsCanvasElem: this.ivsCanvasElem,
            bPlayBack: 1,
            strDecodeFilePath: `${this.wsPlayer.prefixUrl}/WSPlayer/singleThread`
          },
          null,
          this.wsPlayer.config.isWebView
        )
      const { platform: c } = navigator
      0 == c.indexOf('iPhone')
        ? this.playSDK.SetDecodeMode(!1, !1)
        : this.playSDK.SetDecodeMode(i, r),
        (this.playSDK.m_rtspvHandle = this.playSDK.StartPullStream({
          strRtspvUri: e.wsURL,
          strRtspvUrl: e.rtspURL,
          strSourceId: '',
          bTalkService: !1,
          nRange: 0,
          nShortTimeout: this.wsPlayer.rtspResponseTimeout || 8,
          nRtspResponseTimeout: this.wsPlayer.rtspResponseTimeout + 2 || 10
        })),
        null != this.playSDK.m_rtspvHandle &&
          window.wsPlayerManager.bindHandlePlayer(this.playSDK.m_rtspvHandle, this.playSDK),
        (this.timeLong = e.endTime - e.startTime)
      let d = this.timeLong % 60,
        h = parseInt(this.timeLong / 60) % 60,
        p = parseInt(this.timeLong / 3600) % 60
      ;(this.timeLongStr = `${p > 0 ? p + ':' : ''}${h < 10 ? '0' + h : h}:${d < 10 ? '0' + d : d}`),
        WSPlayerJQ.$('.time-long', this.$el).text(this.timeLongStr),
        this.setStatus('ready'),
        window.wsPlayerManager.bindPlayer(this.playSDK.nPlayPort, this.playSDK)
    }
    playSpeed(e) {
      ;(this.speed = e), 1 !== e && this.closeVolume(), this.playSDK && this.playSDK.SetSpeed(e)
    }
  }
  class C {
    constructor() {
      __publicField(this, 'cPlusMediaFrameCallBack', function (e, t, s, i) {
        this.handleToPlayer[e] && this.handleToPlayer[e].InputDataEx(s, i)
      }),
        __publicField(this, 'cPlusRtspMsgCallBack', function (e, t, s) {
          switch (t) {
            case 4096:
              setTimeout(() => {
                this.handleToPlayer[e].StopPullStream(),
                  this.handleToPlayer[e].StreamFailedCallback(s),
                  [285868034, 285868036, 6500424].includes(s) ||
                    this.handleToPlayer[e].StreamDisconnectCallback()
              }, 500)
              break
            case 4097:
              break
            case 4099:
              this.handleToPlayer[e].StreamMsgPlay()
              break
            case 4100:
              this.handleToPlayer[e].StreamFinishCallback()
              break
            case 4102:
              let t = this.handleToPlayer[e]
              this.wsPlayerList.forEach((e) => {
                e.playerList.forEach((i, r) => {
                  ;(i.talkPlaySDK !== t && i.playSDK !== t) || e.dealRTSPError(s, r)
                })
              })
              break
            case 4104:
              this.handleToPlayer[e].StreamRedirectCallback(s)
              break
            case 4109:
              this.handleToPlayer[e].StopPullStream(),
                this.handleToPlayer[e].StreamDisconnectCallback()
          }
        }),
        __publicField(this, 'cPlusRtsvMsgCallBack', function (e, t, s) {
          switch (t) {
            case 16385:
              break
            case 16386:
              this.handleToPlayer[e] && this.handleToPlayer[e].StreamFinishCallback()
          }
        }),
        __publicField(this, 'cIVSDrawDataCallBack', function (e, t, s, i, r) {
          this.portToPlayer[e] && this.portToPlayer[e].SetIVSDrawData(e, t, s, i, r)
        }),
        (this.wsPlayerList = []),
        (this.portToPlayer = {}),
        (this.handleToPlayer = {}),
        (window.cPlusVisibleDecCallBack = this.cPlusVisibleDecCallBack.bind(this)),
        (window.cDigitalSignCallBack = this.cDigitalSignCallBack.bind(this)),
        (window.cRecordDataCallBack = this.cRecordDataCallBack.bind(this)),
        (window.cIVSDrawDataCallBack = this.cIVSDrawDataCallBack.bind(this)),
        (window.cPlusMediaFrameCallBack = this.cPlusMediaFrameCallBack.bind(this)),
        (window.cPlusRtspMsgCallBack = this.cPlusRtspMsgCallBack.bind(this)),
        (window.cPlusRtsvMsgCallBack = this.cPlusRtsvMsgCallBack.bind(this))
    }
    cPlusVisibleDecCallBack(e, t, s, i, r, a) {
      this.portToPlayer[e] && this.portToPlayer[e].SetFrameData(e, t, s, i, r, a)
    }
    cDigitalSignCallBack(e, t, s) {
      this.portToPlayer[e] && this.portToPlayer[e].SetDecryptionResult(e, t, s)
    }
    cRecordDataCallBack(e, t, s, i, r) {
      this.portToPlayer[e] && this.portToPlayer[e].SetRecordData(e, t, s, i, r)
    }
    bindPlayer(e, t) {
      this.portToPlayer[e] || (this.portToPlayer[e] = t)
    }
    unbindPlayer(e) {
      this.portToPlayer[e] = null
    }
    bindHandlePlayer(e, t) {
      this.handleToPlayer[e] || (this.handleToPlayer[e] = t)
    }
    unbindHandlePlayer(e) {
      this.handleToPlayer[e] = null
    }
    addWSPlayer(e) {
      this.wsPlayerList.push(e)
    }
    removeWSPlayer(e) {
      this.wsPlayerList = this.wsPlayerList.filter((t) => t === e)
    }
  }
  const T = {
      clientType: 'WINPC',
      clientMac: '30:9c:23:79:40:08',
      clientPushId: '',
      project: 'PSDK',
      method: 'MTS.Video.StartVideo',
      data: {
        optional: '/admin/API/MTS/Video/StartVideo',
        dataType: '3',
        streamType: '2',
        channelId: '',
        trackId: '',
        urlType: 1,
        packtype: 'Private'
      }
    },
    b = {
      clientType: 'WINPC',
      clientMac: '30:9c:23:79:40:08',
      clientPushId: '',
      project: 'PSDK',
      method: 'MTS.Audio.StartTalk',
      data: {
        optional: '/admin/API/MTS/Audio/StartTalk?token=ff93dabe5d754ea8acb0a95dbe6c4a0f',
        source: '',
        deviceCode: '',
        talkType: '1',
        target: '',
        audioBit: 16,
        audioType: 2,
        broadcastChannels: '',
        sampleRate: 8e3,
        talkMode: '',
        channelSeq: '0',
        enableGBParamAutoAdapt: 1,
        urlType: 1,
        optModule: 'ADMIN_001031',
        audioTypeList: ['2', '4']
      }
    },
    D = {
      clientType: 'WINPC',
      clientMac: '30:9c:23:79:40:08',
      clientPushId: '',
      project: 'PSDK',
      method: 'SS.Record.QueryRecords',
      data: {
        cardNo: '',
        optional: '/admin/API/SS/Record/QueryRecords',
        diskPath: '',
        startIndex: '',
        streamType: '0',
        recordType: '0',
        recordSource: '3',
        endIndex: '',
        startTime: '',
        endTime: '',
        channelId: ''
      }
    },
    k = {
      clientType: 'WINPC',
      clientMac: '30:9c:23:79:40:08',
      clientPushId: '',
      project: 'PSDK',
      method: 'SS.Playback.StartPlaybackByTime',
      data: {
        nvrId: '',
        optional: '/admin/API/SS/Playback/StartPlaybackByTime',
        recordType: '0',
        recordSource: '1',
        streamType: '1',
        channelId: '',
        startTime: '',
        endTime: '',
        urlType: '1'
      }
    },
    R = {
      clientType: 'WINPC',
      clientMac: '30:9c:23:79:40:08',
      clientPushId: '',
      project: 'PSDK',
      method: 'SS.Playback.StartPlaybackByFile',
      data: {
        ssId: '',
        optional: '/evo-apigw/admin/API/SS/Playback/StartPlaybackByFile',
        startTime: '',
        endTime: '',
        fileName: '',
        diskId: '',
        nvrId: '',
        recordSource: '',
        channelId: '',
        playbackMode: '0',
        streamId: '',
        urlType: '1'
      }
    },
    L = { 1: 307, 3: 308, 4: 309, 5: 310, 8: 311, 101: 312 }
  class W {
    constructor(e) {
      ;(this.realPlayer = null),
        (this.recordPlayer = null),
        (this.playCenterRecordByTime = e.playCenterRecordByTime),
        (this.i18n = e.i18n),
        'real' === e.type ? (this.realPlayer = e.player) : (this.recordPlayer = e.player),
        (this.playIndex = 0),
        (this.recordList = []),
        (this.getRealRtsp = e.getRealRtsp),
        (this.getRecords = e.getRecords),
        (this.getRecordRtspByTime = e.getRecordRtspByTime),
        (this.getRecordRtspByFile = e.getRecordRtspByFile),
        (this.getTalkRtsp = e.getTalkRtsp),
        (this.stopTalk = e.stopTalk)
    }
    concatToken(e, t) {
      return e.includes('myToken')
        ? e
        : t && !e.includes('token=')
          ? e + (e.includes('?') ? '&' : '?') + 'token=' + t
          : e
    }
    getCurrentRtsp(e, t) {
      const s = this.getRTSPUrls(e),
        i = s.find((e) => e.includes(window.location.hostname)) || s[0]
      return this.concatToken(i, t)
    }
    getRTSPUrls(e) {
      return e
        .split('|')
        .filter(
          (e) => !e.includes('localhost') && !e.includes('127.0.0.1') && !e.startsWith('rtsp://[')
        )
    }
    processResponse(e, t) {
      try {
        return 'string' == typeof e && e.includes('rtsp://') && e.includes('&token=')
          ? { url: e, innerIp: null }
          : (e.url && e) || (e.data.url && e.data) || (e.data.data.url && e.data.data)
      } catch {
        return null
      }
    }
    getWSUrl(e) {
      return this.getRTSPUrls(e).map((e) => e.split('//')[1].split('/')[0])
    }
    wssRedirectParam(e) {
      'wss' ===
        ((this.realPlayer || this.recordPlayer).protocol ||
          ('https:' === window.location.protocol ? 'wss' : 'ws')) &&
        ((e.data.clientSupportWSSDirect = 1), (e.data.enableXNetFlag = 1))
    }
    openSomeWindow(e) {
      let t = this.realPlayer || this.recordPlayer
      e > t.showNum &&
        (e < t.maxWindow
          ? (this.playNum = e > 16 ? 25 : e > 9 ? 16 : e > 4 ? 9 : 4)
          : (this.playNum = t.maxWindow),
        t.setPlayerNum(this.playNum))
    }
    playRealVideo(e, t = '2', s, i = !1) {
      c.validFunction(this.getRealRtsp)
        ? (this.openSomeWindow(e.length),
          e.map((r, a) => {
            let l = s > -1 ? s : this.playIndex
            e.length > 1 && (l += a),
              (T.data.streamType = t),
              (T.data.channelId = r.id),
              this.wssRedirectParam(T),
              this.realPlayer.setLoading(l, !r.isWaiting, 'loading'),
              this.getRealRtsp(JSON.parse(JSON.stringify(T))).then(
                (e) => {
                  if (this.realPlayer.playerList[l].isclosing)
                    return void (this.realPlayer.playerList[l].isclosing = !1)
                  let s = this.processResponse(e, r)
                  if (!s) return this.realPlayer.sendErrorMessage(104, { channelList: [r] })
                  ;(s.rtspURL = this.getCurrentRtsp(s.url, s.token)),
                    this.realPlayer.playReal({
                      selectIndex: l,
                      streamServerIp: s.innerIp,
                      rtspURL: s.rtspURL,
                      channelId: r.id,
                      channelData: r,
                      streamType: t,
                      wsList: this.getWSUrl(s.url),
                      wssDirect: s.wssDirect
                    })
                },
                (e) => {
                  this.realPlayer.setLoading(l, !1, 'closed'),
                    e
                      ? 401 === Number(e.status) || 27001007 === Number(e.code)
                        ? this.realPlayer.sendErrorMessage(102, {
                            channelList: [r],
                            selectIndex: l,
                            apiErrorInfo: e
                          })
                        : 3033 === Number(e.code)
                          ? this.realPlayer.sendErrorMessage(105, {
                              channelList: [r],
                              selectIndex: l,
                              apiErrorInfo: e
                            })
                          : i
                            ? this.realPlayer.sendErrorMessage(103, {
                                channelList: [r],
                                selectIndex: l,
                                apiErrorInfo: e
                              })
                            : this.playRealVideo([r], '1', l, !0)
                      : this.realPlayer.sendErrorMessage(108, {
                          channelList: [r],
                          selectIndex: l,
                          apiErrorInfo: e
                        })
                }
              )
          }))
        : this.realPlayer.sendErrorMessage(608, {
            insert: [this.i18n.$t('wsPlayer.request.realtime.preview.interface'), 'getRealRtsp']
          })
    }
    startTalk(e) {
      if (!c.validFunction(this.getTalkRtsp))
        return void this.realPlayer.sendErrorMessage(608, {
          insert: [this.i18n.$t('wsPlayer.request.talk.interface'), 'getTalkRtsp']
        })
      ;(b.data.deviceCode = e.deviceCode),
        (b.data.audioBit = e.audioBit || 16),
        (b.data.sampleRate = e.sampleRate || 8e3),
        [1, 3, 6, 10, 13, 14, 43].includes(e.deviceType)
          ? ((b.data.talkType = '2'), (b.data.channelSeq = e.channelSeq))
          : ((b.data.talkType = '1'), (b.data.channelSeq = '0')),
        this.wssRedirectParam(b)
      let t = {
          talkType: b.data.talkType,
          deviceCode: e.deviceCode,
          session: '',
          channelSeq: b.data.channelSeq,
          audioType: 2
        },
        s = (s) => {
          this.realPlayer.playTalk({
            selectIndex: this.realPlayer.talkIndex,
            channelId: e.id,
            channelData: e,
            wssDirect: s.wssDirect,
            rtspURL: this.getCurrentRtsp(s.url, s.token),
            streamServerIp: s.innerIp,
            wsList: this.getWSUrl(s.url),
            talkData: t,
            gbDevice: s.gbDevice
          })
        }
      this.getTalkRtsp(JSON.parse(JSON.stringify(b)))
        .then((i) => {
          let r = this.processResponse(i, e)
          return r
            ? ((t.session = r.session),
              (t.audioType = r.audioType),
              '2' !== t.audioType && '4' !== t.audioType
                ? (this.stopTalk({ data: t }),
                  (b.data.audioType = 4),
                  void this.getTalkRtsp(JSON.parse(JSON.stringify(b)))
                    .then(
                      (i) => (
                        (r = this.processResponse(i, e)),
                        r
                          ? ((t.session = r.session),
                            (t.audioType = r.audioType),
                            '4' !== i.audioType
                              ? (this.realPlayer.sendErrorMessage(L[t.audioType], {
                                  channelList: [e]
                                }),
                                void this.stopTalk({ data: t }))
                              : void s(r))
                          : this.realPlayer.sendErrorMessage(104, { channelList: [e] })
                      )
                    )
                    .catch((t) => {
                      this.realPlayer.sendErrorMessage(304, { channelList: [e], apiErrorInfo: t })
                    }))
                : void s(r))
            : this.realPlayer.sendErrorMessage(104, { channelList: [e] })
        })
        .catch((t) => {
          this.realPlayer.sendErrorMessage(304, { channelList: [e], apiErrorInfo: t })
        })
    }
    getRecordList(e, t) {
      if (!c.validFunction(this.getRecords))
        return void this.recordPlayer.sendErrorMessage(608, {
          insert: [this.i18n.$t('wsPlayer.request.recording.interface'), 'getRecords']
        })
      ;(e.playRecordByTime = !e.playRecordByFile),
        (D.data.streamType = e.streamType || '0'),
        (D.data.recordType = e.recordType || '0'),
        (D.data.recordSource = e.recordSource),
        (D.data.startTime = e.startTime),
        (D.data.endTime = e.endTime)
      let s = 'number' == typeof e.windowIndex ? e.windowIndex : this.playIndex
      e.channelList.length > 1 && this.openSomeWindow(s + e.channelList.length),
        e.channelList.forEach((i, r) => {
          let a = s + r
          this.recordPlayer.setLoading(a, !i.isWaiting, 'loading'), (D.data.channelId = i.id)
          let l = []
          if ('0' == D.data.streamType) {
            l.push(this.getRecords(JSON.parse(JSON.stringify(D))))
            let e = JSON.parse(JSON.stringify(D))
            ;(e.data.streamType = '3'), l.push(this.getRecords(e))
          } else l.push(this.getRecords(JSON.parse(JSON.stringify(D))))
          Promise.all(l).then(
            (s) => {
              let r = (
                [
                  ...((s[0] &&
                    (s[0].records ||
                      (s[0].data && s[0].data.records) ||
                      (s.data && s.data.data && s.data.data.records))) ||
                    []),
                  ...((s[1] &&
                    (s[1].records ||
                      (s[1].data && s[1].data.records) ||
                      (s.data && s.data.data && s.data.data.records))) ||
                    [])
                ] || []
              ).sort((e, t) => e.startTime - t.startTime)
              if (!r.length)
                return (
                  this.recordPlayer.setLoading(a, !1, 'closed'),
                  void this.recordPlayer.sendErrorMessage(201, { channelList: [i] })
                )
              this.getRecordRtsp(
                { ...e, channel: i },
                r.map((e) => ((e.isImportant = ['2', '4', '5'].includes(e.recordType)), e)),
                !e.isUpdateRecords,
                a,
                t
              )
            },
            (t) => {
              this.recordPlayer.setLoading(a, !1, 'closed'),
                t
                  ? 3033 === Number(t.code)
                    ? this.recordPlayer.sendErrorMessage(205, {
                        channelList: [i],
                        options: e,
                        selectIndex: a,
                        apiErrorInfo: t
                      })
                    : this.recordPlayer.sendErrorMessage(202, { channelList: [i], apiErrorInfo: t })
                  : this.recordPlayer.sendErrorMessage(108, {
                      channelList: [i],
                      selectIndex: a,
                      apiErrorInfo: t
                    })
            }
          )
        })
    }
    getRecordRtsp(e, t, s = !0, i, r) {
      let a = null,
        l = t[0].recordSource || e.recordSource,
        n = e.ssId,
        o = [],
        d = 'byTime',
        h = t[0].poolType
      if (!['3', '5'].includes(h) || e.playRecordByTime)
        (k.data.streamType = t[0].streamType || e.streamType || '0'),
          (k.data.recordType = '1'),
          (k.data.recordSource = l),
          (k.data.startTime = e.startTime),
          (k.data.endTime = e.endTime),
          (k.data.channelId = e.channel.id),
          (k.data.streamId = t[0].streamId || ''),
          (n = n || t[0].ssId),
          (o = Array.from(new Set(t.map((e) => e.ssId)))),
          (k.data.ssId = n),
          this.wssRedirectParam(k),
          (a = this.getRecordRtspByTime(JSON.parse(JSON.stringify(k))))
      else {
        if (((d = 'byFile'), !c.validFunction(this.getRecordRtspByFile)))
          return void this.recordPlayer.sendErrorMessage(608, {
            insert: [this.i18n.$t('wsPlayer.request.recording.interface'), 'getRecordRtspByFile']
          })
        let s = t[0]
        ;(R.data.ssId = s.ssId),
          (R.data.startTime = s.startTime),
          (R.data.endTime = s.endTime),
          (R.data.fileName = s.recordName),
          (R.data.diskId = s.diskId),
          (R.data.recordSource = s.recordSource || '3'),
          (R.data.channelId = e.channel.id),
          (R.data.streamId = s.streamId),
          this.wssRedirectParam(R),
          (a = this.getRecordRtspByFile(JSON.parse(JSON.stringify(R))))
      }
      a &&
        a.then(
          (a) => {
            let c = this.processResponse(a, e.channel)
            return c
              ? ((c.channelId = e.channel.id),
                (c.rtspURL = this.getCurrentRtsp(c.url, c.token)),
                (c.wsList = this.getWSUrl(c.url)),
                c.rtspURL
                  ? ((c.channelData = e.channel),
                    (c.startTime = e.startTime),
                    (c.endTime = e.endTime),
                    (c.recordSource = l),
                    void this.recordPlay(c, i, n, o, e.isJumpPlay, r, d, t).then(() => {
                      let r = this.recordList[i]
                      if (s)
                        this.recordList[i] = { ...e, recordList: t, recordIndex: 0, isPlaying: !0 }
                      else {
                        let e = t[0].recordName
                        ;(r.recordIndex = r.recordList.findIndex((t) => t.recordName === e)),
                          (r.isPlaying = !0)
                      }
                      this.playIndex === i &&
                        (s || ((t = r.recordList), (r.isPlaying = !0)), this.setTimeLine(t))
                    }))
                  : (this.recordPlayer.setLoading(i, !1, 'closed'),
                    this.recordPlayer.sendErrorMessage(201, { channelList: [e.channel] }),
                    void console.warn('所选通道未查询到录像文件')))
              : (this.recordPlayer.setLoading(i, !1, 'closed'),
                this.realPlayer.sendErrorMessage(104, { channelList: [e.channel] }))
          },
          (t) => {
            this.recordPlayer.setLoading(i, !1, 'closed'),
              t
                ? this.recordPlayer.sendErrorMessage(203, {
                    channelList: [e.channel],
                    apiErrorInfo: t
                  })
                : this.recordPlayer.sendErrorMessage(108, {
                    channelList: [e.channel],
                    selectIndex: i,
                    apiErrorInfo: t
                  })
          }
        )
    }
    recordPlay(e, t, s, i, r, a, l, n) {
      return new Promise((o) => {
        this.recordPlayer
          .playRecord(
            {
              ...e,
              streamServerIp: e.innerIp,
              selectIndex: t,
              ssId: s,
              ssIdList: i,
              isJumpPlay: r,
              playRecordByFile: 'byFile' === l,
              playRecordByTime:
                'byTime' === l || (this.recordPlayer.config.playCenterRecordByTime && !!s),
              recordList: n
            },
            a
          )
          .then(() => o())
      })
    }
    setTimeLine(e) {
      this.recordPlayer.setTimeLine(e)
    }
    clickRecordTimeLine(e, t) {
      let s = this.recordList[this.playIndex],
        i = {
          channelList: [s.channel],
          startTime: e,
          endTime: s.endTime,
          recordSource: s.recordSource,
          isUpdateRecords: !0,
          ssId: t,
          isJumpPlay: !0
        }
      this.getRecordList(i, {
        DecodeStart() {
          this.player.playByTime(e)
        }
      })
    }
    playNextRecordByFile(e, t) {
      if (!c.validFunction(this.getRecordRtspByFile))
        return void this.recordPlayer.sendErrorMessage(608, {
          insert: [this.i18n.$t('wsPlayer.request.recording.interface'), 'getRecordRtspByFile']
        })
      let s = this.recordList[e]
      if (s.isGetRtspByFile) return
      ;(s.isGetRtspByFile = !0), s.recordIndex++, (s.isPlaying = !0)
      let i = s.recordList[s.recordIndex]
      i
        ? ((R.data.ssId = i.ssId),
          (R.data.startTime = i.startTime),
          (R.data.endTime = i.endTime),
          (R.data.fileName = i.recordName),
          (R.data.diskId = i.diskId),
          (R.data.recordSource = i.recordSource || '3'),
          (R.data.channelId = i.channelId),
          (R.data.streamId = i.streamId),
          this.wssRedirectParam(R),
          this.getRecordRtspByFile(JSON.parse(JSON.stringify(R))).then(
            (t) => {
              if (
                ((t.rtspURL = this.getCurrentRtsp(t.url, t.token)),
                (t.wsList = this.getWSUrl(t.url)),
                !t.rtspURL)
              )
                return (
                  (s.isGetRtspByFile = !1),
                  void this.recordPlayer.sendErrorMessage(201, { channelList: [s.channel] })
                )
              this.recordPlay(t, e, '', [], !0).then(() => {
                ;(s.isGetRtspByFile = !1), this.setTimeLine(s.recordList)
              })
            },
            (e) => {
              ;(s.isGetRtspByFile = !1),
                this.recordPlayer.sendErrorMessage(203, {
                  channelList: [s.channel],
                  apiErrorInfo: e
                })
            }
          ))
        : ((s.isGetRtspByFile = !1), t())
    }
    playNextRecord(e, t) {
      if (t) {
        if (!c.validFunction(this.getRecordRtspByTime))
          return void this.recordPlayer.sendErrorMessage(608, {
            insert: [this.i18n.$t('wsPlayer.request.recording.interface'), 'getRecordRtspByTime']
          })
        let s = this.recordList[e],
          i = s.recordList.find((e) => e.ssId === t)
        ;(k.data.streamType = i.streamType || '0'),
          (k.data.recordType = '1'),
          (k.data.recordSource = i.recordSource),
          (k.data.startTime = new Date(1e3 * i.startTime).setHours(0, 0, 0) / 1e3),
          (k.data.endTime = new Date(1e3 * i.endTime).setHours(23, 59, 59) / 1e3),
          (k.data.channelId = i.channelId),
          (k.data.ssId = t),
          (k.data.streamId = i.streamId || ''),
          this.wssRedirectParam(k)
        let r = Array.from(new Set(s.recordList.map((e) => e.ssId)))
        this.getRecordRtspByTime(JSON.parse(JSON.stringify(k))).then((a) => {
          let l = this.processResponse(a, opt.channel)
          if (!l) return this.realPlayer.sendErrorMessage(104, { channelList: [opt.channel] })
          ;(l.channelId = i.channelId),
            (l.rtspURL = this.getCurrentRtsp(l.url, l.token)),
            (l.wsList = this.getWSUrl(l.url)),
            (l.startTime = k.data.startTime),
            (l.endTime = k.data.endTime),
            this.recordPlay(l, e, t, r, !0).then(() => {
              this.setTimeLine(s.recordList)
            })
        })
      } else;
    }
    changeTimeLine(e) {
      let t = this.recordList[e]
      t && t.isPlaying && this.setTimeLine(t.recordList)
    }
    videoClosed(e, t, s) {
      this.recordList[e] && (this.recordList[e].isPlaying = !1)
    }
    setPlayIndex(e) {
      this.playIndex = e
    }
  }
  class E {
    constructor(e = {}, t) {
      ;(this.i18n = e.locale),
        (this.el = e.el),
        (this.wsPlayer = t),
        (this.prefixUrl = e.prefixUrl || './static'),
        (this.$el = WSPlayerJQ.$('#' + this.el)),
        this.$el && !this.$el.children().length && this.__createPanTilt(),
        (this.channel = null),
        (this.channelCodeForPositionList = []),
        (this.setPtzDirection = e.setPtzDirection),
        (this.setPtzCamera = e.setPtzCamera),
        (this.controlSitPosition = e.controlSitPosition),
        (this.mousedownCanvasEvent = this.__mousedownCanvasEvent.bind(this)),
        (this.mousemoveCanvasEvent = this.__mousemoveCanvasEvent.bind(this)),
        (this.mouseupCanvasEvent = this.__mouseupCanvasEvent.bind(this)),
        (this.clickDirectFlag = !1),
        (this.setPtzDirectionPromiseList = []),
        (this.setPtzDirectionRun = !1),
        (this.setPtzCameraPromiseList = []),
        (this.setPtzCameraRun = !1)
    }
    setChannel(e) {
      this.channel = { ...e }
      let t = this.wsPlayer.selectIndex,
        s = this.channelCodeForPositionList[t]
      if (!e)
        return (
          WSPlayerJQ.$('.ws-pan-tilt-mask', this.$el).css({ display: 'block' }),
          WSPlayerJQ.$('.ws-pan-tilt-mask-position', this.$el).css({ display: 'none' }),
          void this.__removeCanvasEvent()
        )
      s
        ? s !== e.id
          ? ((this.channelCodeForPositionList[t] = null), this.__removeCanvasEvent())
          : this.__openSitPosition(!0)
        : this.openSitPositionFlag && this.__removeCanvasEvent()
      let i = e.capability
      switch (e.cameraType + '') {
        case '1':
          parseInt(i, 2) & parseInt('100', 2) || parseInt(i, 2) & parseInt('10000000000000000', 2)
            ? WSPlayerJQ.$('.ws-pan-tilt-mask-zoom', this.$el).css({ display: 'none' })
            : WSPlayerJQ.$('.ws-pan-tilt-mask-zoom', this.$el).css({ display: 'block' }),
            parseInt(i, 2) & parseInt('10000000000000000', 2)
              ? (WSPlayerJQ.$('.ws-pan-tilt-mask-direction', this.$el).css({ display: 'none' }),
                WSPlayerJQ.$('.ws-pan-tilt-mask-position', this.$el).css({ display: 'block' }),
                this.__removeCanvasEvent())
              : (WSPlayerJQ.$('.ws-pan-tilt-mask-direction', this.$el).css({ display: 'block' }),
                WSPlayerJQ.$('.ws-pan-tilt-mask-position', this.$el).css({ display: 'none' })),
            WSPlayerJQ.$('.ws-pan-tilt-mask-aperture', this.$el).css({ display: 'block' })
          break
        case '2':
          WSPlayerJQ.$('.ws-pan-tilt-mask', this.$el).css({ display: 'none' }),
            WSPlayerJQ.$('.ws-pan-tilt-mask-position', this.$el).css({ display: 'none' })
          break
        default:
          WSPlayerJQ.$('.ws-pan-tilt-mask', this.$el).css({ display: 'block' }),
            WSPlayerJQ.$('.ws-pan-tilt-mask-position', this.$el).css({ display: 'none' }),
            this.__removeCanvasEvent()
      }
    }
    __createPanTilt() {
      this.$el.append(
        `\n            <div class="ws-pan-tilt-control">\n                <div class="ws-pan-tilt-circle-wrapper">\n                    \x3c!--云台方向控制--\x3e\n                    <div class="ws-pan-tilt-circle-rotate">\n                        <div class="ws-pan-tilt-circle">\n                            <div class="ws-pan-tilt-direction-item"><img src="${this.prefixUrl}/WSPlayer/icon/arrow-t.svg" title="${this.i18n.$t('video.player.ptz.up')}" direct="1"/></div>\n                            <div class="ws-pan-tilt-direction-item"><img src="${this.prefixUrl}/WSPlayer/icon/arrow-rt.svg" title="${this.i18n.$t('video.player.ptz.upper.right')}" direct="7"/></div>\n                            <div class="ws-pan-tilt-direction-item"><img src="${this.prefixUrl}/WSPlayer/icon/arrow-r.svg" title="${this.i18n.$t('video.player.ptz.right')}" direct="4"/></div>\n                            <div class="ws-pan-tilt-direction-item"><img src="${this.prefixUrl}/WSPlayer/icon/arrow-rb.svg" title="${this.i18n.$t('video.player.ptz.lower.right')}" direct="8"/></div>\n                            <div class="ws-pan-tilt-direction-item"><img src="${this.prefixUrl}/WSPlayer/icon/arrow-b.svg" title="${this.i18n.$t('video.player.ptz.down')}" direct="2"/></div>\n                            <div class="ws-pan-tilt-direction-item"><img src="${this.prefixUrl}/WSPlayer/icon/arrow-lb.svg" title="${this.i18n.$t('video.player.ptz.lower.left')}" direct="6"/></div>\n                            <div class="ws-pan-tilt-direction-item"><img src="${this.prefixUrl}/WSPlayer/icon/arrow-l.svg" title="${this.i18n.$t('video.player.ptz.left')}" direct="3"/></div>\n                            <div class="ws-pan-tilt-direction-item"><img src="${this.prefixUrl}/WSPlayer/icon/arrow-lt.svg" title="${this.i18n.$t('video.player.ptz.upper.left')}" direct="5"/></div>\n                            <div class="ws-pan-tilt-inner-circle">\n                                <img\n                                    class="ws-pan-tilt-pzt-select"\n                                    src="${this.prefixUrl}/WSPlayer/icon/ptz-select.svg"\n                                    title="${this.i18n.$t('inspec.ptz.position1')}"\n                                />\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                \n                \x3c!--云台变倍、聚焦、光圈控制--\x3e\n                <div class="ws-cloud-control-wrapper">\n                    <div class="ws-pan-tilt-control-item"><img src="${this.prefixUrl}/WSPlayer/icon/ptz-icon1.svg" title="${this.i18n.$t('video.player.ptz.zoom')}-" operateType="1" direct="2"/></div>\n                    <div class="ws-pan-tilt-control-item"><img src="${this.prefixUrl}/WSPlayer/icon/ptz-icon2.svg" title="${this.i18n.$t('video.player.ptz.zoom')}+" operateType="1" direct="1"/></div>\n                    <div class="cloud-control-separate"></div>\n                    <div class="ws-pan-tilt-control-item"><img src="${this.prefixUrl}/WSPlayer/icon/ptz-icon3.svg" title="${this.i18n.$t('video.player.ptz.focus')}-" operateType="2" direct="2"/></div>\n                    <div class="ws-pan-tilt-control-item"><img src="${this.prefixUrl}/WSPlayer/icon/ptz-icon4.svg" title="${this.i18n.$t('video.player.ptz.focus')}+" operateType="2" direct="1"/></div>\n                    <div class="cloud-control-separate"></div>\n                    <div class="ws-pan-tilt-control-item"><img src="${this.prefixUrl}/WSPlayer/icon/ptz-icon5.svg" title="${this.i18n.$t('video.player.ptz.halo')}-" operateType="3" direct="2"/></div>\n                    <div class="ws-pan-tilt-control-item"><img src="${this.prefixUrl}/WSPlayer/icon/ptz-icon6.svg" title="${this.i18n.$t('video.player.ptz.halo')}+" operateType="3" direct="1"/></div>\n                </div>\n                \n                \x3c!--遮罩，当通道没有云台功能时，使用遮罩遮住云台按钮--\x3e\n                \x3c!--方向按钮遮罩--\x3e\n                <div class="ws-pan-tilt-mask ws-pan-tilt-mask-direction"></div>\n                \x3c!--三维定位遮罩--\x3e\n                <div class="ws-pan-tilt-mask ws-pan-tilt-mask-position"></div>\n                \x3c!--变倍、聚焦遮罩--\x3e\n                <div class="ws-pan-tilt-mask ws-pan-tilt-mask-zoom"></div>\n                \x3c!--光圈遮罩--\x3e\n                <div class="ws-pan-tilt-mask ws-pan-tilt-mask-aperture"></div>\n            </div>\n        `
      ),
        WSPlayerJQ.$('.ws-pan-tilt-circle', this.$el).mouseup((e) => {
          if (this.clickDirectFlag) {
            this.clickDirectFlag = !1
            let t = this.__getDirect(e.target)
            t && this.__setPtzDirection.call(this, t, '0')
          }
        }),
        WSPlayerJQ.$('.ws-pan-tilt-circle', this.$el).mouseout((e) => {
          if (this.clickDirectFlag) {
            this.clickDirectFlag = !1
            let t = this.__getDirect(e.target)
            t && this.__setPtzDirection.call(this, t, '0')
          }
        }),
        WSPlayerJQ.$('.ws-pan-tilt-circle', this.$el).mousedown(
          this._throttle((e) => {
            if (!this.clickDirectFlag) {
              let t = this.__getDirect(e.target)
              t && ((this.clickDirectFlag = !0), this.__setPtzDirection.call(this, t, '1'))
            }
          }, 1e3)
        ),
        WSPlayerJQ.$('.ws-pan-tilt-control-item img', this.$el).mouseup((e) => {
          this.operateFlag &&
            ((this.operateFlag = !1),
            this.__setPtzCamera(
              e.target.getAttribute('operateType'),
              e.target.getAttribute('direct'),
              '0'
            ))
        }),
        WSPlayerJQ.$('.ws-pan-tilt-control-item img', this.$el).mouseout((e) => {
          this.operateFlag &&
            ((this.operateFlag = !1),
            this.__setPtzCamera(
              e.target.getAttribute('operateType'),
              e.target.getAttribute('direct'),
              '0'
            ))
        }),
        WSPlayerJQ.$('.ws-pan-tilt-control-item img', this.$el).mousedown(
          this._throttle((e) => {
            ;(this.operateFlag = !0),
              this.__setPtzCamera(
                e.target.getAttribute('operateType'),
                e.target.getAttribute('direct'),
                '1'
              )
          }, 1e3)
        ),
        WSPlayerJQ.$('.ws-pan-tilt-pzt-select', this.$el).click((e) => {
          this.__openSitPosition(!this.openSitPositionFlag)
        })
    }
    __getDirect(e) {
      let t = e.getAttribute('direct')
      if (!t) {
        let s = e.childNodes[0]
        s && s.getAttribute && (t = s.getAttribute('direct'))
      }
      return t
    }
    __setPtzDirection(e, t) {
      const s = {
        project: 'PSDK',
        method: 'DMS.Ptz.OperateDirect',
        data: { direct: e, command: t, stepX: '4', stepY: '4', channelId: this.channel.id }
      }
      if (
        (this.setPtzDirectionPromiseList.push(
          () =>
            new Promise((e, i) => {
              this.setPtzDirection &&
                this.setPtzDirection(s)
                  .then((s) => {
                    let i = s.data || s
                    '1' === t &&
                      i.result &&
                      '0' === i.result &&
                      this.wsPlayer.sendErrorMessage(701, {
                        insert: [s.data.lockUser.userName],
                        apiErrorInfo: s
                      }),
                      e()
                  })
                  .catch((e) => {
                    let s = ['']
                    1103 === (e.data || e).code && (s = [`：${this.i18n.$t('error2084')}`]),
                      '1' === t &&
                        this.wsPlayer.sendErrorMessage(704, { apiErrorInfo: e, insert: s }),
                      i()
                  })
            })
        ),
        !this.setPtzDirectionRun)
      ) {
        let e = () => {
          ;(this.setPtzDirectionRun = !0),
            this.setPtzDirectionPromiseList[0]()
              .then(() => {})
              .catch(() => {})
              .finally(() => {
                this.setPtzDirectionPromiseList.shift(),
                  this.setPtzDirectionPromiseList.length ? e() : (this.setPtzDirectionRun = !1)
              })
        }
        e()
      }
    }
    __setPtzCamera(e, t, s) {
      const i = {
        project: 'PSDK',
        method: 'DMS.Ptz.OperateCamera',
        data: { operateType: e, direct: t, command: s, step: '4', channelId: this.channel.id }
      }
      if (
        (this.setPtzCameraPromiseList.push(
          () =>
            new Promise((e, t) => {
              this.setPtzCamera &&
                this.setPtzCamera(i)
                  .then((t) => {
                    let i = t.data || t
                    '1' === s &&
                      i.result &&
                      '0' === i.result &&
                      this.wsPlayer.sendErrorMessage(701, {
                        insert: [t.data.lockUser.userName],
                        apiErrorInfo: t
                      }),
                      e()
                  })
                  .catch((e) => {
                    let r = [
                        '',
                        this.i18n.$t('video.player.ptz.zoom'),
                        this.i18n.$t('video.player.ptz.focus'),
                        this.i18n.$t('video.player.ptz.halo')
                      ],
                      a = ['', '+', '-'],
                      l = ['', '', '']
                    1103 === (e.data || e).code &&
                      (l = [
                        r[i.data.operateType],
                        a[i.data.direct],
                        `：${this.i18n.$t('error2084')}`
                      ]),
                      '1' === s &&
                        this.wsPlayer.sendErrorMessage(703, { apiErrorInfo: e, insert: l }),
                      t()
                  })
            })
        ),
        !this.setPtzCameraRun)
      ) {
        let e = () => {
          ;(this.setPtzCameraRun = !0),
            this.setPtzCameraPromiseList[0]()
              .then(() => {})
              .catch(() => {})
              .finally(() => {
                this.setPtzCameraPromiseList.shift(),
                  this.setPtzCameraPromiseList.length ? e() : (this.setPtzCameraRun = !1)
              })
        }
        e()
      }
    }
    __openSitPosition(e) {
      this.openSitPositionFlag = e
      let t = this.wsPlayer.playerList,
        s = this.wsPlayer.selectIndex
      ;(this.canvasElem = t[s].pztCanvasElem),
        this.canvasElem.addEventListener('mousedown', this.mousedownCanvasEvent),
        this.canvasElem.addEventListener('mousemove', this.mousemoveCanvasEvent),
        this.canvasElem.addEventListener('mouseup', this.mouseupCanvasEvent),
        (this.canvasContext = this.canvasElem.getContext('2d')),
        (this.canvasContext.lineWidth = 2),
        (this.canvasContext.strokeStyle = '#009cff'),
        this.openSitPositionFlag
          ? ((this.channelCodeForPositionList[s] = this.channel.id),
            WSPlayerJQ.$(this.canvasElem).css({ display: 'block' }),
            (WSPlayerJQ.$('.ws-pan-tilt-pzt-select', this.$el)[0].src =
              `${this.prefixUrl}/WSPlayer/icon/ptz-select-hover.svg`))
          : ((this.channelCodeForPositionList[s] = null),
            WSPlayerJQ.$(this.canvasElem).css({ display: 'none' }),
            (WSPlayerJQ.$('.ws-pan-tilt-pzt-select', this.$el)[0].src =
              `${this.prefixUrl}/WSPlayer/icon/ptz-select.svg`))
    }
    __mousedownCanvasEvent(e) {
      e.target === this.canvasElem &&
        (e.offsetX || e.layerX) &&
        ((this.pointX = e.offsetX || e.layerX),
        (this.pointY = e.offsetY || e.layerY),
        (this.startDraw = !0))
    }
    __mousemoveCanvasEvent(e) {
      if (e.target === this.canvasElem && this.startDraw && (e.offsetX || e.layerX)) {
        const t = e.offsetX || e.layerX,
          s = e.offsetY || e.layerY,
          i = t - this.pointX,
          r = s - this.pointY
        this.canvasContext.clearRect(0, 0, this.canvasElem.width, this.canvasElem.height),
          this.canvasContext.beginPath(),
          this.canvasContext.strokeRect(this.pointX, this.pointY, i, r)
      }
    }
    __mouseupCanvasEvent(e) {
      if (e.target === this.canvasElem && (e.offsetX || e.layerX)) {
        this.startDraw = !1
        const t = e.offsetX || e.layerX,
          s = e.offsetY || e.layerY
        let i = '',
          r = '',
          a = ''
        const l = (t + this.pointX) / 2,
          n = (s + this.pointY) / 2,
          o = this.canvasElem.width / 2,
          c = this.canvasElem.height / 2,
          d = Math.abs(t - this.pointX),
          h = Math.abs(s - this.pointY),
          p = t < this.pointX
        ;(i = (8192 * (l - o) * 2) / this.canvasElem.width),
          (r = (8192 * (n - c) * 2) / this.canvasElem.height),
          t === this.pointX || s === this.pointY
            ? (a = 0)
            : ((a = (this.canvasElem.width * this.canvasElem.height) / (d * h)), p && (a = -a)),
          this.canvasContext.clearRect(0, 0, this.canvasElem.width, this.canvasElem.height),
          this.__controlSitPosition(i, r, a)
      }
    }
    __removeCanvasEvent() {
      this.canvasElem &&
        (this.canvasElem.removeEventListener('mousedown', this.mousedownCanvasEvent),
        this.canvasElem.removeEventListener('mousemove', this.mousemoveCanvasEvent),
        this.canvasElem.removeEventListener('mouseup', this.mouseupCanvasEvent),
        WSPlayerJQ.$(this.canvasElem).css({ display: 'none' }),
        (this.canvasElem = null),
        (this.canvasContext = null),
        (this.openSitPositionFlag = !1),
        (WSPlayerJQ.$('.ws-pan-tilt-pzt-select', this.$el)[0].src =
          `${this.prefixUrl}/WSPlayer/icon/ptz-select.svg`))
    }
    __controlSitPosition(e, t, s) {
      const i = {
        project: 'PSDK',
        method: 'DMS.Ptz.SitPosition',
        data: {
          magicId: localStorage.getItem('magicId') || '',
          pointX: String(Math.round(e)),
          pointY: String(Math.round(t)),
          pointZ: String(Math.round(s)),
          extend: '1',
          channelId: this.channel.id
        }
      }
      this.controlSitPosition &&
        this.controlSitPosition(i)
          .then((e) => {
            let t = e.data || e
            t.result &&
              '0' === t.result &&
              this.wsPlayer.sendErrorMessage(701, {
                insert: [e.data.lockUser.userName],
                apiErrorInfo: e
              })
          })
          .catch((e) => {
            let t = ['']
            1103 === (e.data || e).code && (t[0] = `：${this.i18n.$t('error2084')}`),
              this.wsPlayer.sendErrorMessage(702, { apiErrorInfo: e, insert: t })
          })
    }
    _throttle(e, t) {
      let s = 0
      return function (...i) {
        const r = new Date().getTime()
        if (!(r - s < t)) return (s = r), e.apply(this, i)
      }
    }
  }
  const M = {
      num: 1,
      maxNum: 25,
      showControl: !0,
      draggable: !1,
      showRecordProgressBar: !0,
      isDynamicLoadLib: !0,
      onlyLoadSingleLib: !1,
      useNginxProxy: !1,
      openIvs: !0,
      ivsTypeArr: [1, 2],
      useH264MSE: !0,
      useH265MSE: !0,
      showIcons: {
        streamChangeSelect: !0,
        ivsIcon: !0,
        talkIcon: !0,
        localRecordIcon: !0,
        audioIcon: !0,
        snapshotIcon: !0,
        closeIcon: !0
      },
      downloadMp4Record: !0,
      localRecordSize: 100,
      playCenterRecordByTime: !1,
      localeLang: 'zhCN',
      cacheMode: 1,
      isWebView: !1,
      picCapCb: !1
    },
    _ = 100
  let J,
    Q,
    N = null,
    A = 20,
    F = {},
    B = !0,
    K = '',
    z = { x: 0, y: 0 },
    U = [],
    V = {},
    O = {},
    H = 0,
    q = '',
    j = () => {
      try {
        J.removeEventListener('selectstart', X),
          J.removeEventListener('mousemove', Y),
          J.removeEventListener('mouseup', G),
          J.addEventListener('selectstart', X),
          J.addEventListener('mousemove', Y),
          J.addEventListener('mouseup', G)
      } catch (e) {
        setTimeout(() => {
          j()
        }, 300)
      }
    }
  function X(e) {
    return e.preventDefault(), !1
  }
  function Y(e) {
    if (!K) return
    e.preventDefault()
    let t = Q.getBoundingClientRect(),
      s = t.width / _,
      i = t.height / _
    switch (K) {
      case 'right-border':
        if (((z.x += e.movementX), H !== Math.round(z.x / s))) {
          if (((H = Math.round(z.x / s)), O.lStep + H <= 0)) return void (H = 1 - O.lStep)
          if (O.lStep + H >= _) return void (H = _ - O.lStep - 1)
          document.querySelector('#division-move-line').style.left = 1 * (O.lStep + H) + '%'
        }
        break
      case 'bottom-border':
        if (((z.y += e.movementY), H !== Math.round(z.y / i))) {
          if (((H = Math.round(z.y / i)), O.tStep + H <= 0)) return void (H = 1 - O.tStep)
          O.tStep + H >= _ && (H = _ - O.tStep - 1),
            (document.querySelector('#division-move-line').style.top = 1 * (O.tStep + H) + '%')
        }
    }
  }
  function G(e) {
    if (K) {
      e.preventDefault()
      let t = [],
        s = !0
      switch (K) {
        case 'right-border':
          if (!H) return
          let { l_window: e, r_window: i } = V
          if (
            ((t = [
              ...e.map((e) => ((e.wStep -= H), (e.lStep += H), e.wStep < 1 && (s = !1), e)),
              ...i.map((e) => ((e.wStep += H), e.wStep < 1 && (s = !1), e))
            ]),
            !s)
          )
            return (
              (K = ''),
              document.querySelector('#division-move-line').remove(),
              ae(),
              void F.onError(N.$t('wsPlayer.drag.tip'))
            )
          t.forEach((e) => (U[e.selectIndex] = e))
          break
        case 'bottom-border':
          if (!H) return
          let { t_window: r, b_window: a } = V
          if (
            ((t = [
              ...r.map((e) => ((e.hStep -= H), (e.tStep += H), e.hStep < 1 && (s = !1), e)),
              ...a.map((e) => ((e.hStep += H), e.hStep < 1 && (s = !1), e))
            ]),
            !s)
          )
            return (
              (K = ''),
              document.querySelector('#division-move-line').remove(),
              ae(),
              void F.onError(N.$t('wsPlayer.drag.tip'))
            )
          t.forEach((e) => (U[e.selectIndex] = e))
      }
      ;(K = ''), ae()
    }
  }
  function Z(e = []) {
    if (!e.length || 1 === e.length)
      return (U = [{ wStep: _, hStep: _, tStep: 0, lStep: 0, selectIndex: 0 }]), U
    e = e.sort((e, t) => e.tStep - t.tStep)
    for (let t = 0; t < e.length - 1; t++)
      if (e[t].tStep === e[t + 1].tStep && e[t].lStep > e[t + 1].lStep) {
        let s = { ...e[t] }
        ;(e[t] = { ...e[t + 1] }), (e[t + 1] = s)
      }
    return e.map((e, t) => ((e.selectIndex = t), e))
  }
  function ee(e, t) {
    let s = document.querySelector('#temp-preview-line')
    ;(s.style.display = 'block'),
      (s.style.background = '#1d79f4'),
      (s.style.opacity = 0.4),
      'col' === t &&
        ((s.style.left = `${Math.floor(e.lStep + e.wStep / 2)}%`),
        (s.style.top = `${e.tStep}%`),
        (s.style.width = '2px'),
        (s.style.height = `${e.hStep}%`)),
      'raw' === t &&
        ((s.style.left = `${e.lStep}%`),
        (s.style.top = `${Math.floor(e.tStep + e.hStep / 2)}%`),
        (s.style.width = `${e.wStep}%`),
        (s.style.height = '2px'))
  }
  function te(e, t) {
    U.length !== A
      ? (1 === e.wStep && 1 === e.hStep) ||
        ('col' === t &&
          (U.push({
            ...e,
            lStep: e.lStep + Math.floor(e.wStep / 2),
            wStep: Math.ceil(e.wStep / 2)
          }),
          (U[e.selectIndex] = { ...e, wStep: Math.floor(e.wStep / 2) })),
        'raw' === t &&
          (U.push({
            ...e,
            tStep: e.tStep + Math.floor(e.hStep / 2),
            hStep: Math.ceil(e.hStep / 2)
          }),
          (U[e.selectIndex] = { ...e, hStep: Math.floor(e.hStep / 2) })),
        ae())
      : F.onError(N.$t('wsPlayer.custom.player.max.support.tip', [A]))
  }
  function se(e, t) {
    let s = ''
    return (
      'x' === t &&
        (s = `<div id="division-move-line" style="position: absolute; background: #1d79f4; top: ${1 * e.tStep}%; left: ${1 * e.lStep}%; width: 2px; height: ${1 * e.hStep}%; "></div>`),
      'y' === t &&
        (s = `<div id="division-move-line" style="position: absolute; background: #1d79f4; top: ${1 * e.tStep}%; left: ${1 * e.lStep}%; width: ${1 * e.wStep}%; height: 2px; "></div>`),
      s
    )
  }
  function ie(e) {
    return `<div\n        id="dom-item-${e.selectIndex}"\n        class="${B ? 'dom-item-flex' : 'dom-item'}"\n        style="top: ${e.top}%; left: ${e.left}%; width: ${e.width}%; height: ${e.height}%;"\n    >\n        <div class="point-n-resize" id="dom-item-${e.selectIndex}-bottom-border" style="bottom: -4px; left: 0; width: 100%; height: 6px;"></div>\n        <div class="point-e-resize" id="dom-item-${e.selectIndex}-right-border" style="top: 0; right: -4px; width: 6px; height: 100%;"></div>\n        <div style="width: 68px; display: flex;">\n            <div id="dom-item-${e.selectIndex}-btn-col" class="dom-item-split-btn"></div>\n            <div id="dom-item-${e.selectIndex}-btn-raw" class="dom-item-split-btn" style="margin-left: 11px; transform: rotate(90deg)"></div>\n        </div>\n        <div id="dom-item-${e.selectIndex}-delete-btn" class="dom-item-merge-btn"></div>\n    </div>`
  }
  function re(e = []) {
    B = !1
    let t = []
    e.forEach((e) => {
      t.push(
        ie({
          selectIndex: e.selectIndex,
          width: 1 * e.wStep,
          height: 1 * e.hStep,
          top: 1 * e.tStep,
          left: 1 * e.lStep
        })
      )
    }),
      (Q.innerHTML =
        t.join('') +
        '<div id="temp-preview-line" style="position: absolute; z-index: 10; display: none;"></div>'),
      e.forEach((e) => {
        document
          .querySelector(`#dom-item-${e.selectIndex}-btn-raw`)
          .addEventListener('click', () => {
            te(e, 'raw')
          }),
          document
            .querySelector(`#dom-item-${e.selectIndex}-btn-col`)
            .addEventListener('click', () => {
              te(e, 'col')
            }),
          document
            .querySelector(`#dom-item-${e.selectIndex}-delete-btn`)
            .addEventListener('click', () => {
              !(function (e) {
                if (1 === U.length)
                  return void F.onError(N.$t('wsPlayer.current.window.cannot.be.merged'))
                let t = U.findIndex(
                  (t) =>
                    (t.wStep === e.wStep &&
                      t.lStep === e.lStep &&
                      (t.tStep + t.hStep === e.tStep || e.tStep + e.hStep === t.tStep)) ||
                    (t.hStep === e.hStep &&
                      t.tStep === e.tStep &&
                      (t.lStep + t.wStep === e.lStep || e.lStep + e.wStep === t.lStep))
                )
                if (t > -1) {
                  let s = U[t]
                  s.tStep === e.tStep &&
                    (s.selectIndex > e.selectIndex
                      ? ((U[e.selectIndex].wStep = e.wStep + s.wStep), U.splice(s.selectIndex, 1))
                      : ((U[s.selectIndex].wStep = s.wStep + e.wStep), U.splice(e.selectIndex, 1))),
                    s.lStep === e.lStep &&
                      (s.selectIndex > e.selectIndex
                        ? ((U[e.selectIndex].hStep = e.hStep + s.hStep), U.splice(s.selectIndex, 1))
                        : ((U[s.selectIndex].hStep = s.hStep + e.hStep),
                          U.splice(e.selectIndex, 1)))
                } else F.onError(N.$t('wsPlayer.current.window.cannot.be.merged'))
                ae()
              })(e)
            }),
          document
            .querySelector(`#dom-item-${e.selectIndex}-btn-raw`)
            .addEventListener('mouseenter', (t) => {
              ee(e, 'raw')
            }),
          document
            .querySelector(`#dom-item-${e.selectIndex}-btn-raw`)
            .addEventListener('mouseleave', (e) => {
              document.querySelector('#temp-preview-line').style.display = 'none'
            }),
          document
            .querySelector(`#dom-item-${e.selectIndex}-btn-col`)
            .addEventListener('mouseenter', (t) => {
              ee(e, 'col')
            }),
          document
            .querySelector(`#dom-item-${e.selectIndex}-btn-col`)
            .addEventListener('mouseleave', (e) => {
              document.querySelector('#temp-preview-line').style.display = 'none'
            }),
          document
            .querySelector(`#dom-item-${e.selectIndex}-right-border`)
            .addEventListener('mousedown', (t) => {
              t.stopPropagation(),
                e.lStep + e.wStep !== _ &&
                  ((K = 'right-border'),
                  (z = { x: 0 }),
                  (H = 0),
                  (V = (function ({ wStep: e, lStep: t, selectIndex: s }, i) {
                    let r = i
                        .filter((s) => s.lStep + s.wStep === t + e)
                        .sort((e, t) => e.tStep - t.tStep),
                      a = i.filter((s) => s.lStep === t + e).sort((e, t) => e.tStep - t.tStep)
                    for (var l = 0; l < r.length - 1; l++)
                      r[l].tStep + r[l].hStep !== r[l + 1].tStep &&
                        (s <= r[l].selectIndex ? r.splice(l + 1) : (r.splice(0, l), (l = -1)))
                    let n = -1,
                      o = -1
                    return (
                      a.forEach((e) => {
                        let t = r.findIndex((t) => e.tStep === t.tStep),
                          s = r.findLastIndex((t) => e.tStep + e.hStep === t.tStep + t.hStep)
                        t > -1 && (n = 0 === t ? 0 : Math.min(t, n)), s > -1 && (o = Math.max(s, o))
                      }),
                      (a = a.filter(
                        (e) => e.tStep >= r[n].tStep && e.tStep + e.hStep <= r[o].tStep + r[o].hStep
                      )),
                      { l_window: a, r_window: r }
                    )
                  })({ ...e }, JSON.parse(JSON.stringify(U)))),
                  (O = {
                    hStep: V.r_window.reduce((e, t) => e + t.hStep, 0),
                    tStep: V.r_window[0].tStep,
                    lStep: V.l_window[0].lStep
                  }),
                  (q = se(O, 'x')),
                  (Q.innerHTML += q))
            }),
          document
            .querySelector(`#dom-item-${e.selectIndex}-bottom-border`)
            .addEventListener('mousedown', (t) => {
              e.tStep + e.hStep !== _ &&
                ((K = 'bottom-border'),
                (z = { y: 0 }),
                (H = 0),
                (V = (function ({ hStep: e, tStep: t, selectIndex: s }, i) {
                  let r = i
                      .filter((s) => s.tStep + s.hStep === t + e)
                      .sort((e, t) => e.lStep - t.lStep),
                    a = i.filter((s) => s.tStep === t + e).sort((e, t) => e.lStep - t.lStep)
                  for (var l = 0; l < r.length - 1; l++)
                    r[l].lStep + r[l].wStep !== r[l + 1].lStep &&
                      (s <= r[l].selectIndex ? r.splice(l + 1) : (r.splice(0, l), (l = -1)))
                  let n = -1,
                    o = -1
                  return (
                    a.forEach((e) => {
                      let t = r.findIndex((t) => e.lStep === t.lStep),
                        s = r.findLastIndex((t) => e.lStep + e.wStep === t.lStep + t.wStep)
                      t > -1 && (n = 0 === t ? 0 : Math.min(t, n)), s > -1 && (o = Math.max(s, o))
                    }),
                    (a = a.filter(
                      (e) => e.lStep >= r[n].lStep && e.lStep + e.wStep <= r[o].lStep + r[o].wStep
                    )),
                    { t_window: a, b_window: r }
                  )
                })({ ...e }, JSON.parse(JSON.stringify(U)))),
                (O = {
                  wStep: V.b_window.reduce((e, t) => e + t.wStep, 0),
                  lStep: V.b_window[0].lStep,
                  tStep: V.t_window[0].tStep
                }),
                (q = se(O, 'y')),
                (Q.innerHTML += q))
            })
      })
  }
  function ae() {
    ;(Q.innerHTML = ''), (U = Z(U)), re(U)
  }
  const le = (e) => {
      let { division: t, windowId: s, callback: i, maxNum: r, locale: a } = e
      if (
        ((N = a),
        (A = r),
        (J = document.querySelector(`#${s}`)),
        (J.innerHTML = (function (e) {
          return `<div id="${e}-container" style="width: 100%; height: 100%; border: 1px solid #aaa; position: relative;"></div>\n        <div class="window-division-bottom-container">\n            <button class="window-division-btn window-division-btn-primary" id="${e}-confirm-btn">${N.$t('common.ok')}</button>\n            <button class="window-division-btn window-division-btn-info" id="${e}-reset-btn">${N.$t('common.reset')}</button>\n            <button class="window-division-btn window-division-btn-info" id="${e}-cancel-btn">${N.$t('common.cancel')}</button>\n        </div>\n    `
        })(s)),
        (Q = document.querySelector(`#${s}-container`)),
        (F = i),
        j(),
        document.querySelector(`#${s}-confirm-btn`).addEventListener('click', () => {
          i.onConfirm && i.onConfirm(JSON.stringify(U))
        }),
        document.querySelector(`#${s}-cancel-btn`).addEventListener('click', () => {
          i.onCancel && i.onCancel()
        }),
        document.querySelector(`#${s}-reset-btn`).addEventListener('click', () => {
          ;(U = [{ wStep: 100, hStep: 100, tStep: 0, lStep: 0, selectIndex: 0 }]), re(U)
        }),
        t)
      )
        if ([1, 4, 9, 16, 25].includes(Number(t)))
          !(function (e = 4) {
            B = !0
            let t = [],
              s = 100 / Math.sqrt(e)
            for (var i = 0; i < e; i++) t.push({ selectIndex: i, width: s, height: s })
            var r = t.map((e) => ie(e))
            Q.innerHTML = r.join('')
          })(Number(t))
        else if ('string' != typeof t) i.onError && i.onError(N.$t('wsPlayer.income.format.error'))
        else
          try {
            ;(U = Z(JSON.parse(t))), re(U)
          } catch (l) {
            i.onError && i.onError(N.$t('wsPlayer.income.format.error'))
          }
      else re(Z())
    },
    ne = [3600, 1800, 900, 300]
  const oe = class {
    constructor(e) {
      ;(this.records = e.records || []),
        (this.currentTime = Number(e.currentTime)),
        (this.getCurrentTime = e.getCurrentTime),
        (this.rulerCanvas = null),
        (this.rulerCtx = null),
        (this.canvas_middle_length = 0),
        (this.rulerLength = e.rulerLength || { short: 15, middle: 25, long: 40 }),
        (this.canvasBgColor = 'transparent'),
        (this.rulerColor = '#afafaf'),
        (this.textColor = '#fff'),
        (this.recordsColor = '#318efd'),
        (this.alarmRecordsColor = '#f64153'),
        (this.cursorColor = '#fff'),
        (this.oldScaleInfo = {
          scale_step: this.scale_step,
          scale_step_offsetX: this.scale_step_offsetX
        }),
        (this.scale_step = 5 * e.rulerScale || 10),
        (this.scale_step_offsetX = this.scale_step % 30),
        (this.time_ruler_line = ne[0]),
        (this.move_1px_time = 1),
        (this.moveCurrentTime = 0),
        (this.move_offset = 0),
        (this.isMouseDown = !1),
        this.initRuler(e)
    }
    initRuler(e) {
      if (!e.canvasId) return e.onError && e.onError({ code: 801, message: '请传入canvas的id' })
      ;(this.rulerCanvas = document.querySelector(e.canvasId)),
        (this.rulerCtx = this.rulerCanvas.getContext('2d')),
        (this.canvas_middle_length = Math.floor(this.rulerCanvas.width / 2)),
        this.initDrawLine(),
        this.addEventListener()
    }
    initDrawLine() {
      this.clearRuler(),
        (this.rulerCtx.fillStyle = this.canvasBgColor),
        this.rulerCtx.fillRect(0, 0, this.rulerCanvas.width, this.rulerCanvas.height),
        this.setScaleStepOffset(),
        this.setMoveTime(),
        !this.isMouseDown && this.getMoveOffset(),
        this.renderRecordList(),
        this.renderLeftRuler(),
        this.renderRightRuler(),
        this.drawMarkRuler()
    }
    updateProgress(e) {
      e && (e.currentTime || (e.records && e.records.length))
        ? ((this.currentTime = e.currentTime || this.currentTime),
          (this.records = e.records || this.records),
          this.initDrawLine())
        : ((this.currentTime = 0), (this.records = []), this.initDrawLine())
    }
    changeProgress(e) {
      ;(this.currentTime = e.currentTime || 0), this.initDrawLine()
    }
    addEventListener() {
      let e,
        t = null
      const s = (e) => {
          e.preventDefault(),
            (t = null),
            (this.isMouseDown = !1),
            0 !== this.moveCurrentTime &&
              ((this.currentTime = this.currentTime - this.moveCurrentTime),
              (this.moveCurrentTime = 0),
              this.initDrawLine(),
              this.getCurrentTime && this.getCurrentTime(parseInt(this.currentTime)))
        },
        i = (e) => {
          e.preventDefault(),
            (t = null),
            (this.isMouseDown = !1),
            (this.currentTime = this.currentTime - this.moveCurrentTime),
            (this.moveCurrentTime = 0),
            this.initDrawLine()
        },
        r = (e) => {
          if ((e.preventDefault(), !this.isMouseDown)) {
            this.oldScaleInfo = {
              scale_step: this.scale_step,
              scale_step_offsetX: this.scale_step_offsetX
            }
            const t = e.deltaY || e.wheelDelta
            t < 0 && ((this.scale_step += 5), this.scale_step >= 120 && (this.scale_step = 115)),
              t > 0 && ((this.scale_step -= 5), this.scale_step < 10 && (this.scale_step = 10)),
              this.initDrawLine()
          }
        },
        a = (s) => {
          if ((s.preventDefault(), s.touches && 2 === s.touches.length && null !== t)) {
            this.isMouseDown = !1
            const e = s.touches[0],
              i = s.touches[1],
              a = this.calculateDistance(e, i)
            return r({ wheelDelta: a / t - 1 }), void (t = a)
          }
          if (this.isMouseDown) {
            const t = 'movementX' in s ? s.movementX : s.touches[0].clientX - e
            ;(this.move_offset += t),
              (this.moveCurrentTime += t * this.move_1px_time),
              this.initDrawLine()
          }
        }
      this.rulerCanvas.addEventListener('touchstart', (s) => {
        if ((s.preventDefault(), s.touches && 2 === s.touches.length)) {
          const e = s.touches[0],
            i = s.touches[1]
          t = this.calculateDistance(e, i)
        } else (e = s.touches[0].clientX), (this.isMouseDown = !0)
      }),
        this.rulerCanvas.addEventListener('mousedown', (t) => {
          ;(e = t.clientX), (this.isMouseDown = !0)
        }),
        this.rulerCanvas.addEventListener(
          'touchmove',
          (function (e, t = 160) {
            var s,
              i,
              r = 0
            return function () {
              var a = +new Date()
              ;(s = this), (i = arguments), a - r > t && (e.apply(s, i), (r = a))
            }
          })(a)
        ),
        this.rulerCanvas.addEventListener('mousemove', a),
        this.rulerCanvas.addEventListener('mouseup', s),
        this.rulerCanvas.addEventListener('touchend', s),
        this.rulerCanvas.addEventListener('mouseleave', i),
        this.rulerCanvas.addEventListener('touchleave', i),
        this.rulerCanvas.addEventListener('mouseout', i),
        this.rulerCanvas.addEventListener('mousewheel', r)
      let l = () => {
        ;(this.rulerCanvas.width === this.rulerCanvas.getBoundingClientRect().width &&
          this.rulerCanvas.height === this.rulerCanvas.getBoundingClientRect().height) ||
          ((this.rulerCanvas.width = this.rulerCanvas.getBoundingClientRect().width),
          (this.rulerCanvas.height = this.rulerCanvas.getBoundingClientRect().height),
          (this.canvas_middle_length = Math.floor(this.rulerCanvas.width / 2)),
          this.initDrawLine()),
          window.requestAnimationFrame(l)
      }
      window.requestAnimationFrame(l)
    }
    calculateDistance(e, t) {
      const s = t.clientX - e.clientX,
        i = t.clientY - e.clientY
      return Math.sqrt(s * s + i * i)
    }
    clearRuler() {
      this.rulerCtx &&
        this.rulerCtx.clearRect(0, 0, this.rulerCanvas.width, this.rulerCanvas.height)
    }
    setScaleStepOffset() {
      ;(this.scale_step_offsetX = this.scale_step % 30),
        this.scale_step_offsetX < 10 && (this.scale_step_offsetX = 10)
    }
    setMoveTime() {
      ;(this.time_ruler_line = ne[Math.floor((this.scale_step + 1) / 30)]),
        (this.move_1px_time = this.time_ruler_line / 10 / this.scale_step_offsetX)
    }
    getMoveOffset() {
      this.move_offset = -Math.floor((this.currentTime % this.time_ruler_line) / this.move_1px_time)
    }
    renderLeftRuler() {
      let e = 0
      for (; e <= this.canvas_middle_length + this.move_offset; ) {
        let t = this.canvas_middle_length + this.move_offset - e
        ;(e / this.scale_step_offsetX) % 10 == 0
          ? (this.drawRulerLine(t, this.rulerLength.long),
            (this.rulerCtx.fillStyle = this.textColor),
            this.rulerCtx.fillText(this.getRenderTime(0 - e), t, this.rulerLength.long + 10))
          : (e / this.scale_step_offsetX) % 10 == 5
            ? this.drawRulerLine(t, this.rulerLength.middle)
            : this.drawRulerLine(t, this.rulerLength.short),
          (e += this.scale_step_offsetX)
      }
    }
    renderRightRuler() {
      let e = 0
      for (; e <= this.canvas_middle_length - this.move_offset; ) {
        let t = this.canvas_middle_length + this.move_offset + e
        ;(e / this.scale_step_offsetX) % 10 == 0
          ? (this.drawRulerLine(t, this.rulerLength.long),
            (this.rulerCtx.fillStyle = this.textColor),
            this.rulerCtx.fillText(this.getRenderTime(e), t, this.rulerLength.long + 10))
          : (e / this.scale_step_offsetX) % 10 == 5
            ? this.drawRulerLine(t, this.rulerLength.middle)
            : this.drawRulerLine(t, this.rulerLength.short),
          (e += this.scale_step_offsetX)
      }
    }
    getRenderTime(e) {
      let t = this.currentTime - (this.currentTime % this.time_ruler_line)
      return this.formatter(1e3 * (t + e * this.move_1px_time), 'HH:mm')
    }
    drawRulerLine(e, t, s = this.rulerColor) {
      this.rulerCtx.beginPath(),
        (this.rulerCtx.strokeStyle = s),
        this.rulerCtx.moveTo(e, 0),
        this.rulerCtx.lineTo(e, t),
        this.rulerCtx.stroke(),
        this.rulerCtx.closePath()
    }
    renderRecordList() {
      let e = this.records.filter((e) => !e.isImportant),
        t = this.records.filter((e) => e.isImportant)
      this.renderRecordOnCanvas(e, 'records'), this.renderRecordOnCanvas(t, 'alarmRecords')
    }
    renderRecordOnCanvas(e, t) {
      let s = this.currentTime - this.moveCurrentTime,
        i = s - this.canvas_middle_length * this.move_1px_time,
        r = s + this.canvas_middle_length * this.move_1px_time,
        a = 0
      this.rulerCtx.beginPath(),
        e.forEach((e) => {
          e.endTime < i ||
            e.startTime > r ||
            (e.endTime < r && e.endTime > i
              ? ((a = e.startTime < i ? i : e.startTime),
                (this.rulerCtx.fillStyle = this[`${t}Color`]),
                this.rulerCtx.fillRect(
                  this.canvas_middle_length - Math.floor(s - a) / this.move_1px_time,
                  0,
                  (e.endTime - a) / this.move_1px_time,
                  this.rulerCanvas.height
                ))
              : e.startTime > i && e.startTime < r
                ? ((a = e.endTime > r ? r : e.endTime),
                  (this.rulerCtx.fillStyle = this[`${t}Color`]),
                  this.rulerCtx.fillRect(
                    this.canvas_middle_length - (s - e.startTime) / this.move_1px_time,
                    0,
                    (a - e.startTime) / this.move_1px_time,
                    this.rulerCanvas.height
                  ))
                : e.startTime < i &&
                  e.endTime > r &&
                  ((this.rulerCtx.fillStyle = this[`${t}Color`]),
                  this.rulerCtx.fillRect(0, 0, this.rulerCanvas.width, this.rulerCanvas.height)))
        }),
        this.rulerCtx.closePath()
    }
    drawMarkRuler() {
      this.rulerCtx.beginPath(),
        (this.rulerCtx.strokeStyle = this.cursorColor),
        this.rulerCtx.moveTo(this.canvas_middle_length - 2, 0),
        this.rulerCtx.lineTo(this.canvas_middle_length + 2, 0),
        this.rulerCtx.lineTo(this.canvas_middle_length, 8),
        this.rulerCtx.lineTo(this.canvas_middle_length - 2, 0),
        this.rulerCtx.stroke(),
        (this.rulerCtx.fillStyle = this.cursorColor),
        this.rulerCtx.fill(),
        this.rulerCtx.closePath(),
        (this.rulerCtx.fillStyle = this.textColor),
        this.rulerCtx.fillText(
          this.formatter(1e3 * (this.currentTime - this.moveCurrentTime), 'YYYY-MM-DD HH:mm:ss'),
          this.canvas_middle_length - 55,
          this.rulerCanvas.height - 10
        )
    }
    addZero(e) {
      return e < 10 ? '0' + e : e
    }
    formatter(e, t) {
      let s = (e = new Date(e)).getFullYear(),
        i = this.addZero(e.getMonth() + 1),
        r = this.addZero(e.getDate()),
        a = this.addZero(e.getHours()),
        l = this.addZero(e.getMinutes()),
        n = this.addZero(e.getSeconds()),
        o = ''
      switch (t) {
        case 'YYYY-MM-DD HH:mm:ss':
          o = `${s}-${i}-${r} ${a}:${l}:${n}`
          break
        case 'HH:mm':
          o = `${a}:${l}`
      }
      return o
    }
  }
  !(function (e) {
    function t(e, s) {
      if (!(this instanceof t)) return new t(e, s)
      if (e instanceof Node || e instanceof t) {
        if (e.length) return e
        ;(this.length = 1), (this[0] = e)
      } else if (e instanceof Array)
        (this.length = e.length),
          e.forEach((e, t) => {
            this[t] = e
          })
      else {
        if ('string' != typeof e) return []
        if (e.startsWith('<') || e.startsWith('\n')) {
          const t = new DOMParser().parseFromString(e, 'text/html').body.childNodes
          this.length = t.length
          for (let e = 0; e < this.length; e++) this[e] = t[e]
        } else {
          const i = (s = s instanceof t ? s[0] : s || document).querySelectorAll(e)
          this.length = i.length
          for (let e = 0; e < this.length; e++) this[e] = i[e]
        }
      }
    }
    ;(t.prototype.each = function (e) {
      for (var t = 0; t < this.length; t++) e.call(this[t], t, this[t])
      return this
    }),
      (t.prototype.css = function (e, t) {
        if ('object' == typeof e)
          for (const s in e)
            e.hasOwnProperty(s) &&
              this.each(function () {
                this.style[s] = e[s]
              })
        else {
          if (void 0 === t) return getComputedStyle(this[0])[e]
          this.each(function () {
            this.style[e] = t
          })
        }
        return this
      }),
      (t.prototype.click = function (e) {
        return this.each(function () {
          this.addEventListener('click', e)
        })
      }),
      (t.prototype.src = function (e) {
        return this.each(function () {
          if ('IMG' !== this.tagName && 'AUDIO' !== this.tagName && 'VIDEO' !== this.tagName)
            throw new Error(
              'The "src" method can only be used on <img>, <audio>, or <video> elements.'
            )
          if (void 0 === e) return this.src
          this.src = e
        })
      }),
      (t.prototype.dblclick = function (e) {
        return this.each(function () {
          this.addEventListener('dblclick', e)
        })
      }),
      (t.prototype.parseHTML = function (e) {
        const t = document.createElement('div')
        return (t.innerHTML = e), t.childNodes
      }),
      (t.prototype.html = function (e) {
        return void 0 === e
          ? this[0].innerHTML
          : (this.each(function () {
              this.innerHTML = e
            }),
            this)
      }),
      (t.prototype.empty = function () {
        return this.each(function () {
          for (; this.firstChild; ) this.removeChild(this.firstChild)
        })
      }),
      (t.prototype.addClass = function (e) {
        return this.each(function () {
          this.classList.contains(e) || this.classList.add(e)
        })
      }),
      (t.prototype.removeClass = function (e) {
        if ('string' != typeof e)
          throw new TypeError('Invalid class name provided to removeClass method.')
        return (
          this.each(function () {
            if (this.classList) this.classList.remove(e)
            else {
              const t = this.className.split(/\s+/).filter((t) => t !== e)
              this.className = t.join(' ')
            }
          }),
          this
        )
      }),
      (t.prototype.remove = function () {
        for (let e = 0; e < this.length; e++)
          this[e].parentNode && this[e].parentNode.removeChild(this[e])
      }),
      (t.prototype.children = function (e) {
        const s = []
        return (
          this.each(function () {
            const e = this.children
            for (let t = 0; t < e.length; t++) {
              const i = e[t]
              i.nodeType === Node.ELEMENT_NODE && s.push(i)
            }
          }),
          new t(s, this.context)
        )
      }),
      (t.prototype.siblings = function (e) {
        const s = []
        return (
          this.each(function () {
            const i = this.parentNode
            if (i) {
              const r = i.children
              for (let i = 0; i < r.length; i++) {
                const a = r[i]
                a !== this &&
                  a.nodeType === Node.ELEMENT_NODE &&
                  (e
                    ? e instanceof Node || e instanceof t
                      ? e === a && s.push(a)
                      : a.matches(e) && s.push(a)
                    : s.push(a))
              }
            }
          }),
          new t(s, this.context)
        )
      }),
      (t.prototype.show = function () {
        return (
          this.each(function () {
            this.style.display = ''
          }),
          this
        )
      }),
      (t.prototype.hide = function () {
        return (
          this.each(function () {
            this.style.display = 'none'
          }),
          this
        )
      }),
      (t.prototype.append = function (e) {
        if ('string' == typeof e)
          this.each(function () {
            this.insertAdjacentHTML('beforeend', e)
          })
        else {
          if (!(e instanceof Node || Array.isArray(e)))
            throw new TypeError('Invalid content provided to append method.')
          {
            const t = Array.isArray(e) ? e : [e]
            this.each(function () {
              t.forEach((e) => this.appendChild(e))
            })
          }
        }
        return this
      }),
      (t.prototype.attr = function (e, t) {
        if (void 0 !== t)
          return this.each(function (s, i) {
            i.setAttribute(e, t)
          })
        let s
        for (let i = 0; i < this.length; i++) {
          const t = this[i].getAttribute(e)
          if (null !== t) {
            s = t
            break
          }
        }
        return s
      }),
      (t.prototype.on = function (e, t) {
        return this.each(function () {
          e.split(/\s+/).forEach(function (e) {
            this.addEventListener(e, t)
          }, this)
        })
      }),
      (t.prototype.html = function (e) {
        return void 0 !== e
          ? this.each(function () {
              this.innerHTML = e
            })
          : this[0].innerHTML
      }),
      (t.prototype.text = function (e) {
        return void 0 !== e
          ? this.each(function () {
              this.textContent = e
            })
          : this[0].textContent
      }),
      (t.prototype.width = function (e) {
        return void 0 !== e
          ? this.each(function () {
              this.style.width = e
            })
          : (this[0] && this[0].clientWidth) || null
      }),
      (t.prototype.height = function (e) {
        return void 0 !== e
          ? this.each(function () {
              this.style.height = e
            })
          : (this[0] && this[0].clientHeight) || null
      }),
      (t.prototype.mouseup = function (e) {
        return this.each(function () {
          this.addEventListener('mouseup', e)
        })
      }),
      (t.prototype.mouseout = function (e) {
        return this.each(function () {
          this.addEventListener('mouseout', e)
        })
      }),
      (t.prototype.mousedown = function (e) {
        return this.each(function () {
          this.addEventListener('mousedown', e)
        })
      }),
      (e.WSPlayerJQ = {
        $: function (s, i) {
          return e.$ &&
            e.$.fn &&
            (function (e, t) {
              if (!e || !t) return !1
              let s = e.split('.'),
                i = t.split('.')
              for (let r = 0; r < i.length; r++) {
                if (s[r] > i[r]) return !0
                if (s[r] < i[r]) return !1
              }
              return !0
            })(e.$.fn.jquery, '3.6.0')
            ? e.$(s, i)
            : new t(s, i)
        }
      })
  })(window)
  const ce = '1.3.4'
  class de {
    constructor(e) {
      if (!e.type) return console.error('type 为必传参数，请校验入参'), !1
      if (
        ((this.options = e),
        (this.type = e.type),
        (this.importLoad = e.importLoad),
        (this.config = c.mergeObject(M, e.config)),
        this.config.localeI18n && v.setI18n(this.config.localeI18n),
        v.setLocal(this.config.localeLang),
        (this.wsAdaption = {
          selfAdaption: v.$t('wsPlayer.screen.selfAdaption'),
          stretching: v.$t('wsPlayer.screen.stretching')
        }),
        t.updateLocale(v),
        (this.setWSUrl = e.setWSUrl),
        (this.WS_TIMEOUT = e.WS_TIMEOUT || 1),
        (this.wsProtocol = e.protocol),
        (this.isIntranet = e.isIntranet),
        (this.rememberWSList = []),
        (this.rememberWSErrorList = []),
        (this.ENV = localStorage.ENV || e.ENV),
        (this.intranetMap = e.intranetMap),
        (this.proxyServerIp = e.proxyServerIp || e.serverIp),
        (this.streamServerIp = e.streamServerIp || e.serverIp),
        (this.prefixUrl = this.processPrefixUrl(e.prefixUrl)),
        (this.rtspResponseTimeout = e.rtspResponseTimeout - 0),
        (this.procedure = new W({
          i18n: v,
          type: this.type,
          player: this,
          playCenterRecordByTime: this.config.playCenterRecordByTime,
          getRealRtsp: e.getRealRtsp,
          getRecords: e.getRecords,
          getRecordRtspByTime: e.getRecordRtspByTime,
          getRecordRtspByFile: e.getRecordRtspByFile,
          getTalkRtsp: e.getTalkRtsp,
          stopTalk: e.stopTalk
        })),
        (this.sendMessage = e.receiveMessageFromWSPlayer || function (e, t, s) {}),
        (this.el = e.el),
        (this.fetchChannelAuthority = e.getChannelAuthority),
        (this.$el = WSPlayerJQ.$('#' + this.el)),
        this.$el.empty(),
        !this.$el.length)
      )
        return void this.sendErrorMessage(503)
      ;(this.width = this.$el.attr('width')),
        (this.height = this.$el.attr('height')),
        this.$el.height(`${this.height}px`),
        this.$el.width(`${this.width}px`),
        this.$el.addClass('ws-player'),
        this.$el.append('<div class="player-wrapper"></div>'),
        (this.$wrapper = WSPlayerJQ.$('.player-wrapper', this.$el)),
        (this.playerList = []),
        (this.playerAdapter = 'selfAdaption'),
        (this.canvas = {}),
        (this.ctx = {}),
        (this.showNum = 1),
        (this.maxWindow = 1),
        (this.recordProgressRuler = null),
        (this.clickRecordsTimer = null),
        this.$el.attr('inited', !0)
      let { isVersionCompliance: s, browserType: i, errorCode: r } = c.checkBrowser()
      switch (
        ((this.isHttps = 'https:' === location.protocol),
        this.config.isDynamicLoadLib && this.loadLibPlay(s),
        this.setMaxWindow(),
        (this.currentDragWindowIndex = -1),
        (this.beforeShowNum = 1),
        this.type)
      ) {
        case 'real':
          this.createRealPlayer(e)
          break
        case 'record':
          this.createRecordPlayer(e)
      }
      this.setSelectIndex(0),
        this.setPlayerNum(this.config.division || this.config.num),
        (this.bindUpdatePlayerWindow = this.__updatePlayerWindow.bind(this)),
        window.addEventListener('resize', this.bindUpdatePlayerWindow),
        window.wsPlayerManager || (window.wsPlayerManager = new C()),
        window.wsPlayerManager.addWSPlayer(this),
        this.__updateTopBarStyle(),
        this.__showBottomBarMore()
    }
    processPrefixUrl(e) {
      return e && e.startsWith('./') ? e : e ? `/${e}` : './static'
    }
    setCanvasGetContext() {
      var e
      window.wsCanvasGetContextSet ||
        ((window.wsCanvasGetContextSet = !0),
        (HTMLCanvasElement.prototype.getContext =
          ((e = HTMLCanvasElement.prototype.getContext),
          function (t, s) {
            return (
              'webgl' === t && (s = Object.assign({}, s, { preserveDrawingBuffer: !0 })),
              e.call(this, t, s)
            )
          })))
    }
    setMaxWindow() {
      let e = parseInt(this.config.maxNum, 10)
      this.maxWindow = e > 16 ? 25 : e > 9 ? 16 : e > 8 ? 9 : e > 6 ? 8 : e > 4 ? 6 : e
    }
    createRealPlayer() {
      this.config.showControl ? this.__addRealControl() : this.$wrapper.addClass('nocontrol'),
        Array(this.maxWindow)
          .fill(1)
          .forEach((e, t) => {
            let s = new x({ locale: v, wrapperDomId: this.el, index: t, wsPlayer: this })
            this.playerList.push(s)
          })
    }
    createRecordPlayer() {
      this.config.showRecordProgressBar && this.__addRecordControl(),
        this.config.showControl && this.__addRealControl(),
        !this.config.showRecordProgressBar &&
          !this.config.showControl &&
          this.$wrapper.addClass('nocontrol'),
        Array(this.maxWindow)
          .fill(1)
          .forEach((e, t) => {
            let s = new I({ locale: v, wrapperDomId: this.el, index: t, wsPlayer: this })
            this.playerList.push(s)
          })
    }
    loadCss(e) {
      var t = document.createElement('link')
      ;(t.href = e),
        (t.rel = 'stylesheet'),
        (t.type = 'text/css'),
        document.head
          ? document.head.appendChild(t)
          : document.getElementsByTagName('head')[0].appendChild(t)
    }
    loadScript(e, t) {
      let s = document.createElement('script')
      ;(s.src = e), document.head.appendChild(s)
      var i = !1
      'function' == typeof t &&
        (s.onload = s.onreadystatechange =
          function () {
            i ||
              (s.readyState && !/loaded|complete/.test(s.readyState)) ||
              ((s.onload = s.onreadystatechange = null), (i = !0), t())
          })
    }
    loadLibPlay(e) {
      let t = this
      if (window.loadLibPlayerFlag)
        return (
          (this.config.onlyLoadSingleLib = window.onlyLoadSingleLib),
          void setTimeout(() => {
            t.sendMessage('initializationCompleted')
          }, 300)
        )
      if (
        (this.importLoad && this.loadPlaySDKInterface(),
        (window.loadLibPlayerFlag = !0),
        (window.m_bClientInitialized = !1),
        window.Module || (window.Module = {}),
        (Module.onRuntimeInitialized = function () {
          setTimeout(() => {
            ;(window.m_nModuleInitialized = !0), t.sendMessage('initializationCompleted')
          }, 300)
        }),
        (Module.locateFile = function (e, s) {
          return e.endsWith('.data') ? `${t.prefixUrl}/WSPlayer/multiThread/libplay.data` : s + e
        }),
        !this.isHttps || !e || this.config.onlyLoadSingleLib)
      )
        return (
          console.log(
            `%c wsplayer播放器 版本号V${ce} 单线程`,
            'background: rgb(115, 189, 224); color: #fff; font-size: 20px'
          ),
          this.loadLibRenderEngine(),
          this.loadLibIVSDrawer(),
          !this.config.isWebView && this.loadLibASPLite(),
          (this.config.onlyLoadSingleLib = !0),
          (window.onlyLoadSingleLib = this.config.onlyLoadSingleLib),
          void setTimeout(() => {
            ;(window.m_nModuleInitialized = !0), this.sendMessage('initializationCompleted')
          }, 300)
        )
      try {
        new SharedArrayBuffer(1),
          this.loadLibPlaySDK(),
          this.loadLibStreamClient(),
          console.log(
            `%cwsplayer播放器 版本号V${ce} 多线程`,
            'background: rgb(91, 66, 181); color: #fff; font-size: 20px'
          ),
          this.loadLibRenderEngine()
      } catch (s) {
        this.loadLibRenderEngine(),
          (this.config.onlyLoadSingleLib = !0),
          (window.onlyLoadSingleLib = this.config.onlyLoadSingleLib),
          setTimeout(() => {
            ;(window.m_nModuleInitialized = !0),
              this.sendMessage('initializationCompleted'),
              console.log(
                `%cwsplayer播放器 版本号V${ce} 单线程`,
                'background: rgb(115, 189, 224); color: #fff; font-size: 20px'
              )
          }, 300)
      }
      this.loadLibIVSDrawer(), !this.config.isWebView && this.loadLibASPLite()
    }
    loadPlaySDKInterface() {
      let e = `${this.prefixUrl}/WSPlayer/PlaySDKInterface.js`
      this.loadCss(`${this.prefixUrl}/WSPlayer/player.css`), this.loadScript(e, null)
    }
    loadLibPlaySDK() {
      let e = `${this.prefixUrl}/WSPlayer/multiThread/libplay.js`
      this.loadScript(e, null)
    }
    loadLibStreamClient() {
      let e = `${this.prefixUrl}/WSPlayer/multiThread/libStreamClient.js`
      this.loadScript(e, function () {
        Multi_Client_Module().then((e) => {
          ;(window.SCModule = e),
            window.SCModule._GLOBAL_Init(2),
            (window.m_bClientInitialized = !0)
        })
      })
    }
    loadLibRenderEngine() {
      let e = `${this.prefixUrl}/WSPlayer/commonThread/libRenderEngine.js`
      this.loadScript(e, function () {
        RenderEngine_Module().then((e) => {
          window.REModule = e
        })
      })
    }
    loadLibASPLite() {
      let e = `${this.prefixUrl}/WSPlayer/commonThread/libmavasp_litepacket.js`,
        t = `${this.prefixUrl}/WSPlayer/commonThread/libmavasp_litepacket.data`
      this.loadScript(e, function () {
        ;(ASPLite_Module.locateFile = function (e, s) {
          return e.endsWith('.data') ? t : s + e
        }),
          ASPLite_Module(ASPLite_Module).then((e) => {
            window.ASPLiteModule = e
          })
      })
    }
    loadLibIVSDrawer() {
      let e = `${this.prefixUrl}/WSPlayer/commonThread/libIVSDrawer.js`
      this.loadScript(e, function () {
        IVSDrawer_Module().then((e) => {
          window.IVSModule = e
        })
      })
    }
    playReal(e) {
      this.__getWSUrl(e).then((t) => {
        ;(e.wsURL = e.wsURL || t), (e.playerAdapter = e.playerAdapter || this.playerAdapter)
        let s = this.playerList[e.selectIndex]
        ;(s.playType = e.playType),
          e.selectIndex + 1 < this.showNum
            ? this.setSelectIndex(e.selectIndex + 1)
            : this.selectIndex === e.selectIndex && s && this.setPtzChannel(e.channelData),
          s && s.init(e)
      })
    }
    playTalk(e) {
      let t = this.playerList[e.selectIndex]
      this.autoSetWSUrl(e.wsList, 'wss')
        .then((t) => {
          e.wsURL = t
        })
        .catch(() => {})
        .finally(() => {
          t.startTalk(e)
        })
    }
    playRecord(e, t = {}) {
      return new Promise((s, i) => {
        let r = this.playerList[e.selectIndex]
        r
          ? ((r.playType = e.playType),
            this.__getWSUrl(e).then((i) => {
              ;(e.wsURL = e.wsURL || i),
                (e.playerAdapter = e.playerAdapter || this.playerAdapter),
                (e.isPlayback = !0),
                WSPlayerJQ.$('.ws-record-play', this.$el).css({ display: 'none' }),
                WSPlayerJQ.$('.ws-record-pause', this.$el).css({ display: 'block' })
              for (let e in t) r[e] = t[e]
              r && r.init(e), s()
            }))
          : i()
      })
    }
    openVolume(e) {
      let t = this.playerList[void 0 === e ? this.selectIndex : e]
      !t.isAudioPlay && t.openVolume()
    }
    closeVolume(e) {
      let t = this.playerList[void 0 === e ? this.selectIndex : e]
      t.isAudioPlay && t.closeVolume()
    }
    setVolume(e, t) {
      let s = this.playerList[void 0 === e ? this.selectIndex : e]
      t > 0 ? (this.playSDK.SetSoundState(!0), s.openVolume(t)) : s.closeVolume()
    }
    picCap(e, t) {
      this.playerList[void 0 === e ? this.selectIndex : e].picCap(t)
    }
    play(e) {
      if ('real' === this.type)
        return void this.sendErrorMessage(611, { method: 'play', arguments: { index: e } })
      let t = this.playerList[void 0 === e ? this.selectIndex : e]
      t
        ? 'pause' === t.status && t.play()
        : this.sendErrorMessage(601, { method: 'play', arguments: { index: e } })
    }
    pause(e) {
      if ('real' === this.type)
        return void this.sendErrorMessage(612, { method: 'pause', arguments: { index: e } })
      let t = this.playerList[void 0 === e ? this.selectIndex : e]
      t
        ? 'playing' === t.status && t.pause()
        : this.sendErrorMessage(601, { method: 'pause', arguments: { index: e } })
    }
    playSpeed(e, t) {
      if (![0.125, 0.25, 0.5, 1, 1.25, 1.5, 2, 4, 8].includes(e))
        return void console.error(v.$t('wsPlayer.error.206'))
      if ('real' === this.type)
        return void this.sendErrorMessage(607, {
          method: 'playSpeed',
          arguments: { speed: e, index: t }
        })
      let s = this.playerList[void 0 === t ? this.selectIndex : t]
      s
        ? s.playSpeed(parseFloat(e))
        : this.sendErrorMessage(601, { method: 'playSpeed', arguments: { speed: e, index: t } })
    }
    setSelectIndex(e) {
      if (((e = Number(e)), this.selectIndex === e || void 0 === e)) return
      let t = this.playerList[e]
      if (t) {
        if ((this.procedure && this.procedure.setPlayIndex(e), 'record' === this.type)) {
          let s = t.status
          'playing' === s
            ? (WSPlayerJQ.$('.ws-record-play', this.$el).css({ display: 'none' }),
              WSPlayerJQ.$('.ws-record-pause', this.$el).css({ display: 'block' }))
            : 'pause' === s &&
              (WSPlayerJQ.$('.ws-record-pause', this.$el).css({ display: 'none' }),
              WSPlayerJQ.$('.ws-record-play', this.$el).css({ display: 'block' })),
            ['playing', 'pause'].includes(s)
              ? this.setTimeLine(t.options.recordList, t.currentTime)
              : (this.setTimeLine([]),
                WSPlayerJQ.$('.ws-record-pause', this.$el).css({ display: 'none' }),
                WSPlayerJQ.$('.ws-record-play', this.$el).css({ display: 'block' })),
            this.__setPlaySpeed('', e)
        }
        this.sendMessage('selectWindowChanged', {
          channelId: (t.options || {}).channelId,
          playIndex: e
        }),
          (this.selectIndex = e),
          this.setPtzChannel((t.options || {}).channelData),
          this.playerList.forEach((t, s) => {
            s === e
              ? t.$el.removeClass('unselected').addClass('selected')
              : t.$el.removeClass('selected').addClass('unselected')
          })
      } else this.sendErrorMessage(601, { method: 'setSelectIndex', arguments: { index: e } })
    }
    createCustomDialog() {
      let e = `<div id="${this.el}-custom-container" class="custom-division-container"></div>`
      this.$el.append(e),
        le({
          locale: v,
          maxNum: this.config.maxNum,
          division: localStorage.customDivision || '',
          windowId: `${this.el}-custom-container`,
          callback: {
            onError: (e) => {
              this.sendErrorMessage(610, {
                method: 'customDivisionError',
                arguments: {},
                insert: [e]
              })
            },
            onConfirm: (e) => {
              ;(localStorage.customDivision = e),
                WSPlayerJQ.$(`#${this.el}-custom-container`).remove(),
                this.setCustomPlayer(e)
            },
            onCancel: () => {
              WSPlayerJQ.$(`#${this.el}-custom-container`).remove()
            }
          }
        })
    }
    createCustomDom(e) {
      this.playerList.forEach((t, s) => {
        t.customDomId = `${t.domId}-${e}`
        let i = `\n                <div id="${t.customDomId}" style="display: none;" class="ws-custom-dom-style"></div>\n            `
        WSPlayerJQ.$('.ws-full-content', t.$el).append(i),
          (t.customDomElem = WSPlayerJQ.$(`#${t.customDomId}`))
      })
    }
    numberToList(e) {
      let t = 100 / e,
        s = []
      for (var i = 0; i < e; i++)
        for (var r = 0; r < e; r++) s.push({ lStep: r * t, tStep: i * t, wStep: t, hStep: t })
      return s
    }
    renderPlayerNum(e, t) {
      let s = []
      s = 'number' == typeof t ? this.numberToList(t) : t
      let i = 100
      for (var r = 0; r < e.length; r++) {
        let t = e[r].getAttribute('id'),
          a = t.split('-'),
          l = Number(a[a.length - 1])
        s[l]
          ? WSPlayerJQ.$(`#${t}`).css({
              top: `${s[l].tStep}%`,
              left: `${s[l].lStep}%`,
              width: `${s[l].wStep}%`,
              height: `${s[l].hStep}%`,
              visibility: 'visible',
              'z-index': i--
            })
          : WSPlayerJQ.$(`#${t}`).css({
              top: '150%',
              left: 0,
              width: 0,
              height: 0,
              visibility: 'hidden'
            })
      }
    }
    resetPlayerScreen(e, t) {
      for (let s = 0; s < t; s++)
        e[s] &&
          WSPlayerJQ.$(`#${e[s].getAttribute('id')}`).css({
            top: '150%',
            left: 0,
            width: 0,
            height: 0,
            visibility: 'hidden'
          })
    }
    setPlayerNum(e, t) {
      if (Number(e) > this.config.maxNum) return console.error(v.$t('wsPlayer.error.609'))
      this.setSelectIndex(0),
        Number(e) > 0 ? this.setDefaultPlayer(Number(e), t) : this.setCustomPlayer(e, t)
    }
    setDefaultPlayer(e, s) {
      let i = WSPlayerJQ.$(`#${this.el} .wsplayer-item`)
      this.resetPlayerScreen(i, this.config.maxNum)
      let r = parseInt(e) || 1
      switch (r) {
        case 1:
          ;(r = 1), this.renderPlayerNum(i, 1)
          break
        case 2:
          ;(r = JSON.stringify(t.windowDefaultCustomDivision[2])),
            this.renderPlayerNum(i, t.windowDefaultCustomDivision[2])
          break
        case 3:
          ;(r = JSON.stringify(t.windowDefaultCustomDivision[3])),
            this.renderPlayerNum(i, t.windowDefaultCustomDivision[3])
          break
        case 4:
          ;(r = 4), this.renderPlayerNum(i, 2)
          break
        case 5:
        case 6:
          ;(r = JSON.stringify(t.windowDefaultCustomDivision[6])),
            this.renderPlayerNum(i, t.windowDefaultCustomDivision[6])
          break
        case 7:
        case 8:
          ;(r = JSON.stringify(t.windowDefaultCustomDivision[8])),
            this.renderPlayerNum(i, t.windowDefaultCustomDivision[8])
          break
        case 9:
          ;(r = 9), this.renderPlayerNum(i, 3)
          break
        case 10:
        case 11:
        case 12:
        case 13:
        case 14:
        case 15:
        case 16:
          ;(r = 16), this.renderPlayerNum(i, 4)
          break
        case 17:
        case 18:
        case 19:
        case 20:
        case 21:
        case 22:
        case 23:
        case 24:
        case 25:
          ;(r = 25), this.renderPlayerNum(i, 5)
      }
      r > this.maxWindow && (r = this.maxWindow),
        this.showNum !== r &&
          ((this.showNum = r),
          !s && this.sendMessage('windowNumChanged', this.showNum),
          setTimeout(() => {
            this.__updatePlayerWindow()
          }, 200))
    }
    setCustomPlayer(e, t) {
      let s = WSPlayerJQ.$(`#${this.el} .wsplayer-item`)
      this.resetPlayerScreen(s, this.config.maxNum),
        this.renderPlayerNum(s, JSON.parse(e)),
        (this.showNum = e),
        !t && this.sendMessage('windowNumChanged', this.showNum),
        setTimeout(() => {
          this.__updatePlayerWindow()
        }, 200)
    }
    switchPlayerScreen(e, t, s) {
      WSPlayerJQ.$(`#${this.el}-${t}`).css({
        top: `${e[s].tStep}%`,
        left: `${e[s].lStep}%`,
        width: `${e[s].wStep}%`,
        height: `${e[s].hStep}%`
      }),
        WSPlayerJQ.$(`#${this.el}-${s}`).css({
          top: `${e[t].tStep}%`,
          left: `${e[t].lStep}%`,
          width: `${e[t].wStep}%`,
          height: `${e[t].hStep}%`
        })
      let i = WSPlayerJQ.$(`#${this.el}-${t} .default-status`).css('transform'),
        r = WSPlayerJQ.$(`#${this.el}-${s} .default-status`).css('transform')
      WSPlayerJQ.$(`#${this.el}-${t} .default-status`).css({ transform: r }),
        WSPlayerJQ.$(`#${this.el}-${s} .default-status`).css({ transform: i }),
        WSPlayerJQ.$(`#${this.el}-${t}`).attr('id', `${this.el}-temp`),
        WSPlayerJQ.$(`#${this.el}-${s}`)
          .attr('id', `${this.el}-${t}`)
          .removeClass(`wsplayer-item-${s}`)
          .addClass(`wsplayer-item-${t}`),
        WSPlayerJQ.$(`#${this.el}-temp`)
          .attr('id', `${this.el}-${s}`)
          .removeClass(`wsplayer-item-${t}`)
          .addClass(`wsplayer-item-${s}`)
      let a = this.playerList[t]
      ;(this.playerList[t] = this.playerList[s]),
        (this.playerList[s] = a),
        (this.playerList[t].index = t),
        (this.playerList[t].currentIndex = t),
        (this.playerList[t].domId = `${this.el}-${s}`),
        (this.playerList[s].index = s),
        (this.playerList[s].currentIndex = s),
        (this.playerList[s].domId = `${this.el}-${t}`)
    }
    changeDragWindow(e) {
      if (this.currentDragWindowIndex < 0 || this.currentDragWindowIndex === e)
        return void (this.currentDragWindowIndex = -1)
      let t = []
      ;(t =
        'number' == typeof this.showNum
          ? this.numberToList(Math.sqrt(this.showNum))
          : JSON.parse(this.showNum)),
        this.switchPlayerScreen(t, this.currentDragWindowIndex, e),
        this.sendMessage('dragWindow', { dragIndex: this.currentDragWindowIndex, dropIndex: e }),
        this.currentDragWindowIndex === this.selectIndex && this.setSelectIndex(e),
        (this.currentDragWindowIndex = -1),
        setTimeout(() => {
          this.__updatePlayerWindow()
        }, 200)
    }
    setPlayerAdapter(e) {
      this.playerAdapter !== e &&
        (['selfAdaption', 'stretching'].includes(e)
          ? ((this.playerAdapter = e),
            WSPlayerJQ.$('.ws-select-show-option', this.$el).text(this.wsAdaption[e]),
            this.__updatePlayerWindow())
          : this.sendErrorMessage(606, {
              method: 'setPlayerAdapter',
              arguments: { playerAdapter: e }
            }))
    }
    setTimeLine(e = [], t) {
      this.config.showRecordProgressBar &&
        ((this.timeList = e),
        this.recordProgressRuler &&
          this.recordProgressRuler.updateProgress({ records: e, currentTime: t }))
    }
    setFullScreen() {
      let e = this.$el[0].children[0]
      e.requestFullscreen
        ? e.requestFullscreen()
        : e.webkitRequestFullscreen
          ? e.webkitRequestFullscreen()
          : e.mozRequestFullScreen
            ? e.mozRequestFullScreen()
            : e.msRequestFullscreen && e.msRequestFullscreen(),
        this.__updatePlayerWindow(),
        this.sendMessage('fullScreen', { fullScreen: !0 })
    }
    setExitFullScreen() {
      document.exitFullscreen
        ? document.exitFullscreen()
        : document.webkitExitFullscreen
          ? document.webkitExitFullscreen()
          : document.mozCancelFullScreen
            ? document.mozCancelFullScreen()
            : document.msExitFullscreen && document.msExitFullscreen()
    }
    close(e) {
      let t = Number(e)
      if (isNaN(t))
        return void (
          this.playerList &&
          this.playerList.forEach((e) => {
            ;(e.isclosing = !0), e.close()
          })
        )
      let s = this.playerList[t]
      ;(s.isclosing = !0), s && s.close()
    }
    destroy() {
      this.close(), window.removeEventListener('resize', this.bindUpdatePlayerWindow)
    }
    __addRealControl() {
      this.$el.append(
        `\n            <div class="ws-control">\n                <div class="ws-flex ws-control-record ws-flex-left">\n                    <div class="ws-ctrl-record-icon ws-record-play" style="display: none" title='${v.$t('wsPlayer.play')}'></div>\n                    <div class="ws-ctrl-record-icon ws-record-pause" title='${v.$t('wsPlayer.pause')}'></div>\n                    <div class="ws-ctrl-record-icon ws-record-speed-sub" title='${v.$t('wsPlayer.speed.sub')}'></div>\n                    <div class="ws-ctrl-record-icon ws-record-speed-txt">1x</div>\n                    <div class="ws-ctrl-record-icon ws-record-speed-add" title='${v.$t('wsPlayer.speed.add')}'></div>\n                </div>\n                <div style="flex: 1;"></div>\n                <div class="ws-flex ws-flex-end">\n                    <div id="btn-icon-adaption" class="ws-select-self-adaption">\n                        <div class="ws-select-show select">\n                            <div class="ws-select-show-option" title='${v.$t('wsPlayer.screen.selfAdaption')}'>${v.$t('wsPlayer.screen.selfAdaption')}</div>\n                            \x3c!-- 下拉箭头 --\x3e\n                            <img src="${this.prefixUrl}/WSPlayer/icon/spread.png" />\n                        </div>\n                        <div class="ws-self-adaption-type" style="display: none">\n                            <ul class="ws-select-ul">\n                                \x3c!--自定义--\x3e\n                                <li title='${v.$t('wsPlayer.screen.selfAdaption')}' value="selfAdaption" class="ws-select-type-item">${v.$t('wsPlayer.screen.selfAdaption')}</li>\n                                \x3c!--拉伸--\x3e\n                                <li title='${v.$t('wsPlayer.screen.stretching')}' value="stretching" class="ws-select-type-item">${v.$t('wsPlayer.screen.stretching')}</li>\n                            </ul>\n                        </div>\n                    </div>\n                    <span class="ws-ctrl-btn-spread ws-ctrl-btn-spread-1"></span>\n                    <div id="btn-icon-1" class="ws-ctrl-icon one-screen-icon" title='${v.$t('wsPlayer.screen.one')}'></div>\x3c!--单屏--\x3e\n                    <div id="btn-icon-2" class="ws-ctrl-icon two-screen-icon" title='${v.$t('wsPlayer.screen.split', [2])}'></div>\x3c!--2分屏--\x3e\n                    <div id="btn-icon-4" class="ws-ctrl-icon four-screen-icon" title='${v.$t('wsPlayer.screen.split', [4])}'></div>\x3c!--4分屏--\x3e\n                    <div id="btn-icon-9" class="ws-ctrl-icon nine-screen-icon" title='${v.$t('wsPlayer.screen.split', [9])}'></div>\x3c!--9分屏--\x3e\n                    <div id="btn-icon-16" class="ws-ctrl-icon sixteen-screen-icon" title='${v.$t('wsPlayer.screen.split', [16])}'></div>\x3c!--16分屏--\x3e\n                    <div id="btn-icon-25" class="ws-ctrl-icon twenty-five-screen-icon" title='${v.$t('wsPlayer.screen.split', [25])}'></div>\x3c!--25分屏--\x3e\n                    <div id="btn-icon-3" class="ws-ctrl-icon three-screen-icon" title='${v.$t('wsPlayer.screen.split', [3])}'></div>\x3c!--3分屏--\x3e\n                    <div id="btn-icon-6" class="ws-ctrl-icon six-screen-icon" title='${v.$t('wsPlayer.screen.split', [6])}'></div>\x3c!--6分屏--\x3e\n                    <div id="btn-icon-8" class="ws-ctrl-icon eight-screen-icon" title='${v.$t('wsPlayer.screen.split', [8])}'></div>\x3c!--8分屏--\x3e\n                    <div id="btn-icon-custom" class="ws-ctrl-icon custom-screen-icon" title='${v.$t('wsPlayer.screen.custom.split')}'></div>\x3c!--自定义分屏--\x3e\n                    <span class="ws-ctrl-btn-spread ws-ctrl-btn-spread-2"></span>\n                    <div id="btn-icon-close" class="ws-ctrl-icon close-all-video" title='${v.$t('wsPlayer.one.click.off')}'></div>\x3c!--一键关闭--\x3e\n                    <div id="btn-icon-full" class="ws-ctrl-icon full-screen-icon" title='${v.$t('wsPlayer.screen.full')}'></div>\x3c!--全屏--\x3e\n                </div>\n                <div class="ws-ctrl-ellipsis" title='${v.$t('wsplayer.more')}'>\n                    <ul class="ws-ctrl-ellipsis-list">\n                        <li id="ws-record-pause">${v.$t('wsPlayer.pause')}</li>\n                        <li id="ws-record-play">${v.$t('wsPlayer.play')}</li>\n                        <li id="ws-record-speed-add">${v.$t('wsPlayer.fastForward')}</li>\n                        <li id="ws-record-speed-sub">${v.$t('wsPlayer.fastBackward')}</li>\n                        <li id="ws-select-stretching">${v.$t('wsPlayer.screen.stretching')}</li>\n                        <li id="ws-select-selfAdaption">${v.$t('wsPlayer.screen.selfAdaption')}</li>\n                        <li id="one-screen-icon">${v.$t('wsPlayer.screen.one')}</li>\n                        <li id="two-screen-icon">${v.$t('wsPlayer.screen.split', [2])}</li>\n                        <li id="four-screen-icon">${v.$t('wsPlayer.screen.split', [4])}</li>\n                        <li id="nine-screen-icon">${v.$t('wsPlayer.screen.split', [9])}</li>\n                        <li id="sixteen-screen-icon">${v.$t('wsPlayer.screen.split', [16])}</li>\n                        <li id="twenty-five-screen-icon">${v.$t('wsPlayer.screen.split', [25])}</li>\n                        <li id="three-screen-icon">${v.$t('wsPlayer.screen.split', [3])}</li>\n                        <li id="six-screen-icon">${v.$t('wsPlayer.screen.split', [6])}</li>\n                        <li id="eight-screen-icon">${v.$t('wsPlayer.screen.split', [8])}</li>\n                        <li id="custom-screen-icon">${v.$t('wsPlayer.screen.custom.split')}</li>\n                        <li id="close-all-video">${v.$t('wsPlayer.one.click.off')}</li>\n                        <li id="full-screen-icon">${v.$t('wsPlayer.screen.full')}</li>\n                    </ul>\n                </div>\n            </div>\n        `
      ),
        this.maxWindow <= 16 &&
          (WSPlayerJQ.$('.twenty-five-screen-icon', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('#twenty-five-screen-icon', this.$el).css({ display: 'none' })),
        this.maxWindow <= 9 &&
          (WSPlayerJQ.$('.sixteen-screen-icon', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('#sixteen-screen-icon', this.$el).css({ display: 'none' })),
        this.maxWindow <= 8 &&
          (WSPlayerJQ.$('.nine-screen-icon', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('#nine-screen-icon', this.$el).css({ display: 'none' })),
        this.maxWindow <= 6 &&
          (WSPlayerJQ.$('.eight-screen-icon', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('#eight-screen-icon', this.$el).css({ display: 'none' })),
        this.maxWindow <= 4 &&
          (WSPlayerJQ.$('.six-screen-icon', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('#six-screen-icon', this.$el).css({ display: 'none' })),
        this.maxWindow <= 3 &&
          (WSPlayerJQ.$('.four-screen-icon', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('#four-screen-icon', this.$el).css({ display: 'none' })),
        this.maxWindow <= 2 &&
          (WSPlayerJQ.$('.three-screen-icon', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('#three-screen-icon', this.$el).css({ display: 'none' })),
        1 === this.maxWindow &&
          (WSPlayerJQ.$('.two-screen-icon', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('#two-screen-icon', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('.one-screen-icon', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('#one-screen-icon', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('.custom-screen-icon', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('#custom-screen-icon', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('.ws-ctrl-btn-spread-2', this.$el).css({ display: 'none' })),
        WSPlayerJQ.$('#ws-select-stretching', this.$el).click(() => {
          this.setPlayerAdapter('stretching')
        }),
        WSPlayerJQ.$('#ws-select-selfAdaption', this.$el).click(() => {
          this.setPlayerAdapter('selfAdaption')
        }),
        WSPlayerJQ.$('.full-screen-icon', this.$el).click(() => {
          this.setFullScreen()
        }),
        WSPlayerJQ.$('#full-screen-icon', this.$el).click(() => {
          this.setFullScreen()
        }),
        WSPlayerJQ.$('.one-screen-icon', this.$el).click(() => {
          this.setPlayerNum(1)
        }),
        WSPlayerJQ.$('#one-screen-icon', this.$el).click(() => {
          this.setPlayerNum(1)
        }),
        WSPlayerJQ.$('.two-screen-icon', this.$el).click(() => {
          this.setPlayerNum(2)
        }),
        WSPlayerJQ.$('#two-screen-icon', this.$el).click(() => {
          this.setPlayerNum(2)
        }),
        WSPlayerJQ.$('.three-screen-icon', this.$el).click(() => {
          this.setPlayerNum(3)
        }),
        WSPlayerJQ.$('#three-screen-icon', this.$el).click(() => {
          this.setPlayerNum(3)
        }),
        WSPlayerJQ.$('.four-screen-icon', this.$el).click(() => {
          this.setPlayerNum(4)
        }),
        WSPlayerJQ.$('#four-screen-icon', this.$el).click(() => {
          this.setPlayerNum(4)
        }),
        WSPlayerJQ.$('.six-screen-icon', this.$el).click(() => {
          this.setPlayerNum(6)
        }),
        WSPlayerJQ.$('#six-screen-icon', this.$el).click(() => {
          this.setPlayerNum(6)
        }),
        WSPlayerJQ.$('.eight-screen-icon', this.$el).click(() => {
          this.setPlayerNum(8)
        }),
        WSPlayerJQ.$('#eight-screen-icon', this.$el).click(() => {
          this.setPlayerNum(8)
        }),
        WSPlayerJQ.$('.nine-screen-icon', this.$el).click(() => {
          this.setPlayerNum(9)
        }),
        WSPlayerJQ.$('#nine-screen-icon', this.$el).click(() => {
          this.setPlayerNum(9)
        }),
        WSPlayerJQ.$('.sixteen-screen-icon', this.$el).click(() => {
          this.setPlayerNum(16)
        }),
        WSPlayerJQ.$('#sixteen-screen-icon', this.$el).click(() => {
          this.setPlayerNum(16)
        }),
        WSPlayerJQ.$('.twenty-five-screen-icon', this.$el).click(() => {
          this.setPlayerNum(25)
        }),
        WSPlayerJQ.$('#twenty-five-screen-icon', this.$el).click(() => {
          this.setPlayerNum(25)
        }),
        WSPlayerJQ.$('.close-all-video', this.$el).click(() => {
          this.close()
        }),
        WSPlayerJQ.$('#close-all-video', this.$el).click(() => {
          this.close()
        }),
        WSPlayerJQ.$('.custom-screen-icon', this.$el).click(() => {
          this.createCustomDialog()
        }),
        WSPlayerJQ.$('#custom-screen-icon', this.$el).click(() => {
          this.createCustomDialog()
        }),
        WSPlayerJQ.$('.ws-ctrl-ellipsis', this.$el).click(() => {
          this.createHideList()
        }),
        (this.selfAdaptionSelectShow = !1),
        WSPlayerJQ.$('.ws-select-self-adaption', this.$el).click((e) => {
          this.selfAdaptionSelectShow
            ? (WSPlayerJQ.$('.ws-self-adaption-type', this.$el).hide(),
              (this.selfAdaptionSelectShow = !1))
            : (WSPlayerJQ.$('.ws-self-adaption-type', this.$el).show(),
              (this.selfAdaptionSelectShow = !0),
              WSPlayerJQ.$('.ws-select-ul .ws-select-type-item', this.$el).css({
                background: 'none'
              }),
              WSPlayerJQ.$(`.ws-select-ul [value=${this.playerAdapter}]`, this.$el).css({
                background: '#1A78EA'
              }))
        }),
        WSPlayerJQ.$('.ws-self-adaption-type', this.$el).click((e) => {
          let t = e.target.getAttribute('value')
          this.setPlayerAdapter(t),
            WSPlayerJQ.$('.ws-select-show-option', this.$el).text(this.wsAdaption[t])
        }),
        'record' !== this.type &&
          (WSPlayerJQ.$('.ws-control-record', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('#ws-record-pause', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('#ws-record-play', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('#ws-record-speed-add', this.$el).css({ display: 'none' }),
          WSPlayerJQ.$('#ws-record-speed-sub', this.$el).css({ display: 'none' })),
        WSPlayerJQ.$('.ws-record-pause', this.$el).click((e) => {
          this.pause()
        }),
        WSPlayerJQ.$('#ws-record-pause', this.$el).click((e) => {
          this.pause()
        }),
        WSPlayerJQ.$('.ws-record-play', this.$el).click((e) => {
          this.play()
        }),
        WSPlayerJQ.$('#ws-record-play', this.$el).click((e) => {
          this.play()
        }),
        WSPlayerJQ.$('.ws-record-speed-sub', this.$el).click((e) => {
          'playing' === this.playerList[this.selectIndex].status && this.__setPlaySpeed('PREV')
        }),
        WSPlayerJQ.$('#ws-record-speed-sub', this.$el).click((e) => {
          'playing' === this.playerList[this.selectIndex].status && this.__setPlaySpeed('PREV')
        }),
        WSPlayerJQ.$('.ws-record-speed-add', this.$el).click((e) => {
          'playing' === this.playerList[this.selectIndex].status && this.__setPlaySpeed('NEXT')
        }),
        WSPlayerJQ.$('#ws-record-speed-add', this.$el).click((e) => {
          'playing' === this.playerList[this.selectIndex].status && this.__setPlaySpeed('NEXT')
        })
    }
    createHideList() {
      let e = WSPlayerJQ.$('.ws-ctrl-ellipsis-list', this.$el)
      '' === e.css('display') || 'none' === e.css('display')
        ? e.css('display', 'block')
        : e.css('display', 'none')
    }
    __setPlaySpeed(e, t) {
      let s,
        i,
        r = [
          { value: 0.125, label: '0.125x' },
          { value: 0.25, label: '0.25x' },
          { value: 0.5, label: '0.5x' },
          { value: 1, label: '1x' },
          { value: 1.25, label: '1.25x' },
          { value: 1.5, label: '1.5x' },
          { value: 2, label: '2x' },
          { value: 4, label: '4x' },
          { value: 8, label: '8x' }
        ],
        a = this.playerList[void 0 === t ? this.selectIndex : t]
      r.some((l, n) => {
        if (l.value === a.speed)
          return (
            (i = 'PREV' === e ? n - 1 : 'NEXT' === e ? n + 1 : n),
            (s = r[i]),
            !s ||
              (i
                ? i === r.length - 1
                  ? WSPlayerJQ.$('.ws-record-speed-add', this.$el).css({ cursor: 'not-allowed' })
                  : (WSPlayerJQ.$('.ws-record-speed-sub', this.$el).css({ cursor: 'pointer' }),
                    WSPlayerJQ.$('.ws-record-speed-add', this.$el).css({ cursor: 'pointer' }))
                : WSPlayerJQ.$('.ws-record-speed-sub', this.$el).css({ cursor: 'not-allowed' }),
              WSPlayerJQ.$('.ws-record-speed-txt', this.$el).text(s.label),
              'playing' === a.status && this.playSpeed(s.value, t),
              !0)
          )
      })
    }
    __addRecordControl() {
      this.$el.append(
        '\n            <div class="ws-control ws-record-control">\n            <canvas id="wsplayer-record-progress" height="60" class="ws-record-area"></canvas>\n            </div>\n        '
      ),
        (this.recordProgressRuler = new oe({
          canvasId: `#${this.el} canvas#wsplayer-record-progress`,
          records: [],
          currentTime: 0,
          rulerLength: { short: 10, middle: 20, long: 25 },
          rulerScale: 2,
          getCurrentTime: (e) => {
            this.clickRecordsTimer && clearTimeout(this.clickRecordsTimer),
              (this.clickRecordsTimer = setTimeout(() => {
                clearTimeout(this.clickRecordsTimer), (this.clickRecordsTimer = null)
              }, 1500)),
              this.clickRecordTimeLine(e),
              this.play()
          }
        }))
    }
    __setTimeRecordArea(e = []) {
      if (e.length) {
        let t = WSPlayerJQ.$('.ws-record-control', this.$el).width()
        this.canvas.width = t
        let s = [],
          i = [],
          r = this.ctx.createLinearGradient(0, 0, 0, 60)
        r.addColorStop(0, 'rgba(77, 201, 233, 0.1)'), r.addColorStop(1, '#1c79f4')
        let a = this.ctx.createLinearGradient(0, 0, 0, 60)
        a.addColorStop(0, 'rgba(251, 121, 101, 0.1)'),
          a.addColorStop(1, '#b52c2c'),
          e.forEach((e) => {
            e.width = ((e.endTime - e.startTime) * t) / 86400
            let r = new Date(1e3 * e.startTime),
              a = r.getHours(),
              l = r.getMinutes(),
              n = r.getSeconds()
            ;(e.left = ((3600 * a + 60 * l + n) / 86400) * t), e.isImportant ? i.push(e) : s.push(e)
          }),
          s.forEach((e) => {
            this.ctx.clearRect(e.left, 0, e.width, 60),
              (this.ctx.fillStyle = r),
              this.ctx.fillRect(e.left, 0, e.width, 60)
          }),
          i.forEach((e) => {
            this.ctx.clearRect(e.left, 0, e.width, 60),
              (this.ctx.fillStyle = a),
              this.ctx.fillRect(e.left, 0, e.width, 60)
          })
      } else this.canvas.width = 0
    }
    __setPlayingTime(e, t, s, i, r, a, l) {
      if (this.selectIndex === e) {
        let n = new Date(`${t}-${s}-${i} ${r}:${a}:${l}`).getTime() / 1e3
        ;(this.playerList[e].currentTime = n),
          this.clickRecordsTimer ||
            (this.recordProgressRuler &&
              this.recordProgressRuler.updateProgress({ currentTime: n }))
      }
    }
    showMsgInWindow(e, t, s) {
      let i = this.playerList[void 0 === e ? this.selectIndex : e]
      i
        ? ((i.options = { ...i.options, ...s, channelId: s.id || s.channelId }),
          i.setStatus('showmsg'),
          i.showMsgInWindow(t))
        : this.sendErrorMessage(601, {
            method: 'showMsgInWindow',
            arguments: { index: e, msg: t, channelData: s }
          })
    }
    autoSetWSUrl(e, t) {
      return new Promise((s, i) => {
        let r = (e = e.map((e) => t + '://' + e)).filter((e) => this.rememberWSList.includes(e))
        if (r.length) return void s(r[0])
        if (e.every((e) => this.rememberWSErrorList.includes(e)))
          return void i({ code: -105, message: v.$t('wsPlayer.auto.recognition.failed') })
        let a = 0,
          l = (t) => {
            let r,
              n = new WebSocket(t),
              o = !1
            ;(n.onopen = () => {
              ;(o = !0), this.rememberWSList.push(t), clearTimeout(r), n.close(), s(e[a - 1])
            }),
              (n.onerror = () => {
                this.rememberWSErrorList.push(t),
                  clearTimeout(r),
                  (o = !1),
                  a >= e.length
                    ? i({ code: -105, message: v.$t('wsPlayer.auto.recognition.failed') })
                    : l(e[a++])
              }),
              (r = setTimeout(() => {
                this.rememberWSErrorList.push(t), !o && n.close()
              }, 1e3 * this.WS_TIMEOUT))
          }
        l(e[a++])
      })
    }
    _getStreamWSUrl(e, s) {
      if (this.intranetMap)
        for (let t in this.intranetMap) t.includes(s) && (s = this.intranetMap[t])
      if (!s)
        return void console.warn('please configure 【streamServerIp】 in new PlayerManager({...})')
      if (s.includes(':')) return `${e}://${s}`
      let i = ''
      return (
        (i =
          'wss' === e
            ? 'real' === this.type
              ? t.websocketPorts.realmonitor_wss
              : t.websocketPorts.playback_wss
            : 'real' === this.type
              ? t.websocketPorts.realmonitor_ws
              : t.websocketPorts.playback_ws),
        `${e}://${s}:${i}`
      )
    }
    _getNginxWSUrl(e, s, i) {
      let r = ''
      s.match(/\d{1,3}(\.\d{1,3}){3}/g) && (r = s.split('//')[1].split(':')[0])
      let a = 'real' === this.type ? t.websocketPorts.realmonitor : t.websocketPorts.playback
      return this.proxyServerIp
        ? `${e}://${this.proxyServerIp}/${a}?serverIp=${i || r}`
        : (console.warn(
            'please configure 【proxyServerIp】 in new PlayerManager({...})\n now i use 【location.host】 instead of 【proxyServerIp】 or set configure【localStorage.wsplayerProxyIp】'
          ),
          `${e}://${localStorage.wsplayerProxyIp || location.host}/${a}?serverIp=${i || r}`)
    }
    __getWSUrl(e) {
      let { rtspURL: t, streamServerIp: s, wsList: i, playType: r, wssDirect: a, talkData: l } = e
      return new Promise((e, l) => {
        if ('url' === r) return void e()
        if (localStorage.wsUrl) return void e(localStorage.wsUrl)
        if (this.setWSUrl) return void e(this.setWSUrl(i))
        let n = this.wsProtocol || (this.isHttps ? 'wss' : 'ws')
        'DEVTOOL' === this.ENV && this.config.useNginxProxy
          ? e(this._getNginxWSUrl('wss', t, s || this.streamServerIp))
          : this.autoSetWSUrl(i, n, a)
              .then((t) => {
                e(t)
              })
              .catch((i) => {
                if (
                  (console.warn(i.message),
                  this.isHttps && this.config.useNginxProxy && this.proxyServerIp)
                )
                  return void e(
                    this._getNginxWSUrl(
                      'wss',
                      t,
                      ('ICC' === this.ENV && s) || this.streamServerIp || s
                    )
                  )
                let r = this._getStreamWSUrl(n, this.streamServerIp || s)
                r && e(r)
              })
      })
    }
    __updatePlayerWindow() {
      setTimeout(() => {
        this.playerList.forEach((e) => {
          e.updateAdapter(this.playerAdapter)
        })
      }, 24),
        this.__updateTopBarStyle(),
        this.__renderDefaultStats(),
        this.__showBottomBarMore()
    }
    __renderDefaultStats() {
      setTimeout(() => {
        this.playerList.forEach((e) => {
          e.renderDefaultStats()
        })
      }, 24)
    }
    __updateTopBarStyle() {
      setTimeout(() => {
        this.playerList.forEach((e) => {
          e.updateTopBarStyle()
        })
      }, 24)
    }
    __showBottomBarMore() {
      setTimeout(() => {
        this.$el.width() < ('real' === this.type ? 540 : 700)
          ? WSPlayerJQ.$('.ws-ctrl-ellipsis', this.$el).css({ display: 'unset' })
          : WSPlayerJQ.$('.ws-ctrl-ellipsis', this.$el).css({ display: 'none' })
      }, 24)
    }
    __startTalk(e) {
      this.procedure && this.procedure.startTalk(e)
    }
    talkByUrl(e) {
      this.playerList[e.selectIndex].talkByUrl(e)
    }
    stopUrlTalk(e) {
      this.playerList[e].stopTalk('url')
    }
    playRealVideo(e) {
      let { channelList: t = [], streamType: s = '2', windowIndex: i } = e
      this.procedure && this.procedure.playRealVideo(t, s, i)
    }
    realByUrl(e) {
      if (((this.selectIndex = e.selectIndex), !e.rtspURL && !e.wsURL))
        return console.error(v.$t('wsPlayer.error.106'))
      this.playReal({
        playType: 'url',
        rtspURL: e.rtspURL,
        wsURL: e.wsURL,
        channelId: e.channelId,
        streamType: e.streamType,
        streamServerIp: e.streamServerIp,
        playerAdapter: e.playerAdapter,
        selectIndex: e.selectIndex - 0,
        channelData: e.channelData || {}
      })
    }
    changeStreamType(e, t, s) {
      this.procedure && this.procedure.playRealVideo([e], t, s)
    }
    playRecordVideo(e) {
      this.procedure && this.procedure.getRecordList(e)
    }
    recordByUrl(e) {
      if ((!e.records && (e.records = []), !e.rtspURL && !e.wsURL))
        return console.error(v.$t('wsPlayer.error.106'))
      e.records.length || console.warn(v.$t('wsPlayer.error.107')),
        (e.records = e.records.sort((e, t) => e.startTime - t.startTime)),
        this.playRecord({
          playType: 'url',
          wsURL: e.wsURL,
          rtspURL: e.rtspURL,
          records: e.records || [],
          channelId: e.channelId,
          startTime: e.startTime || (e.records[0] && e.records[0].startTime),
          endTime: e.endTime || (e.records && [e.records.length - 1].endTime),
          playerAdapter: e.playerAdapter,
          selectIndex: e.selectIndex - 0,
          channelData: e.channelData || {},
          recordSource: e.recordSource,
          playRecordByTime: 'boolean' != typeof e.playRecordByTime || e.playRecordByTime,
          ssId: (e.records && e.records[0] && e.records[0].ssId) || ''
        })
    }
    clickRecordTimeLine(e) {
      let t = this.playerList[this.selectIndex]
      this.timeList.some((s) => {
        if (e >= s.startTime && e < s.endTime) {
          if (t.options.playRecordByTime && t.options.ssId === s.ssId)
            t.playSDK.SetSeekTime(e - t.options.startTime),
              this.sendMessage('setSeekTime', {
                timeStamp: e,
                channelInfo: t.options,
                channelData: t.options.channelData,
                hasVideoRecord: !0
              })
          else {
            if ('url' === t.playType)
              return (
                this.sendMessage('switchStartTime', {
                  timeStamp: e,
                  channelInfo: t.options,
                  channelData: t.options.channelData,
                  hasVideoRecord: !0
                }),
                !0
              )
            this.procedure && this.procedure.clickRecordTimeLine(e, s.ssId)
          }
          return !0
        }
      }) ||
        ('url' === t.playType &&
          this.sendMessage('switchStartTime', {
            timeStamp: e,
            channelInfo: t.options,
            channelData: t.options.channelData,
            hasVideoRecord: !1
          }),
        console.warn('所选时间点无录像'))
    }
    jumpPlayByTime(e, t) {
      if ('real' === this.type)
        return void this.sendErrorMessage(613, {
          method: 'jumpPlayByTime',
          arguments: { time: e, selectIndex: t }
        })
      let s,
        i = this.playerList[void 0 === t ? this.selectIndex : t]
      if (i) {
        if (['playing', 'pause'].includes(i.status)) {
          if ('object' == typeof e)
            return (s = e.getTime && e.getTime() / 1e3), void this.clickRecordTimeLine(s)
          if ((-1 === e.indexOf(':') && (e = Number(e)), 'string' == typeof e)) {
            const t = new Date(e)
            if (isNaN(t.getTime())) {
              const t = e.split(':')
              if (3 === t.length) {
                const e = parseInt(t[0], 10),
                  i = parseInt(t[1], 10),
                  r = parseInt(t[2], 10)
                s = new Date(1e3 * this.timeList[0].startTime).setHours(e, i, r, 0) / 1e3
              }
            } else s = t.getTime() / 1e3
          }
          'number' == typeof e &&
            (s =
              e <= 86400 && e / 1e3 === parseInt(e / 1e3)
                ? Number(this.timeList[0].startTime) + e
                : e <= 864e5
                  ? Number(this.timeList[0].startTime) + e / 1e3
                  : e.toString().length <= 10
                    ? e
                    : e / 1e3),
            this.clickRecordTimeLine(s)
        }
      } else
        this.sendErrorMessage(601, {
          method: 'jumpPlayByTime',
          arguments: { time: e, selectIndex: t }
        })
    }
    playNextRecord(e, t) {
      this.procedure && this.procedure.playNextRecord(e, t)
    }
    playNextRecordByFile(e, t) {
      this.procedure && this.procedure.playNextRecordByFile(e, t)
    }
    videoClosed(e, t, s, i) {
      this.sendMessage('closeVideo', {
        selectIndex: e,
        changeVideoFlag: t,
        channelData: s,
        customDomId: i
      }),
        this.procedure && this.procedure.videoClosed(e, t, s)
    }
    sendErrorMessage(e, s = {}) {
      let i = t.errorInfo[e]
      s.insert &&
        (s.insert.forEach((e, t) => {
          i = i.replace(`{${t}}`, e)
        }),
        delete s.insert),
        this.sendMessage('errorInfo', { errorCode: e, errorInfo: i, errorData: s })
    }
    dealRTSPError(e, t) {
      this.sendErrorMessage(e, { res: 'rtsp', arguments: { index: t } })
    }
    startLocalRecord(e, t, s, i) {
      let r = this.playerList[void 0 === e ? this.selectIndex : e]
      r
        ? 'playing' === r.status || 'pause' === r.status
          ? r.isRecording
            ? this.sendErrorMessage(602, {
                method: 'startLocalRecord',
                arguments: { selectIndex: e, name: t, size: s }
              })
            : ((r.isRecording = !0),
              r.startRecord(t, s, i),
              WSPlayerJQ.$('.record-icon', r.$el).addClass('recording'))
          : this.sendErrorMessage(603, {
              method: 'startLocalRecord',
              arguments: { selectIndex: e, name: t, size: s }
            })
        : this.sendErrorMessage(601, {
            method: 'startLocalRecord',
            arguments: { selectIndex: e, name: t, size: s }
          })
    }
    stopLocalRecord(e) {
      let t = this.playerList[void 0 === e ? this.selectIndex : e]
      t
        ? t.isRecording
          ? ((t.isRecording = !1),
            t.playSDK.StopRecord(),
            WSPlayerJQ.$('.record-icon', t.$el).removeClass('recording'))
          : this.sendErrorMessage(604, { method: 'stopLocalRecord', arguments: { selectIndex: e } })
        : this.sendErrorMessage(601, { method: 'stopLocalRecord', arguments: { selectIndex: e } })
    }
    setLoading(e, t, s) {
      let i = this.playerList[void 0 === e ? this.selectIndex : e]
      i
        ? i.setLoading(t, s)
        : this.sendErrorMessage(601, { method: 'setLoading', arguments: { selectIndex: e } })
    }
    setIvs({ showIvs: e, selectIndex: t, ivsType: s = [1, 2] }) {
      let i = this.playerList[void 0 === t ? this.selectIndex : t]
      i
        ? (i.playSDK.OpenIVS(),
          i.playSDK.SetIvsLanguageEnvi('en' === localStorage.getItem('language') ? 1 : 0),
          e
            ? (s.includes(1) || i.playSDK.SetIvsEnable(3, 0),
              s.includes(2) || (i.playSDK.SetIvsEnable(1, 0), i.playSDK.SetIvsEnable(14, 0)))
            : i.playSDK.CloseIVS())
        : this.sendErrorMessage(601, {
            method: 'setIvs',
            arguments: { showIvs: e, selectIndex: t, ivsType: s }
          })
    }
    initPanTilt(e) {
      this.panTilt = new E({ ...e, prefixUrl: this.prefixUrl, locale: v }, this)
    }
    setPtzChannel(e) {
      this.panTilt && this.panTilt.setChannel(e)
    }
    stopTalk(e) {
      this.procedure && this.procedure.stopTalk({ data: e })
    }
  }
  __publicField(de, 'version', ce),
    (e.WSPlayer = de),
    (e.default = de),
    Object.defineProperties(e, {
      __esModule: { value: !0 },
      [Symbol.toStringTag]: { value: 'Module' }
    })
})
