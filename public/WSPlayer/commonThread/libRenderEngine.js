var RenderEngine_Module = (function () {
  var _scriptDir =
    typeof document !== 'undefined' && document.currentScript
      ? document.currentScript.src
      : undefined
  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename
  return function (RenderEngine_Module) {
    RenderEngine_Module = RenderEngine_Module || {}

    var Module = typeof RenderEngine_Module !== 'undefined' ? RenderEngine_Module : {}
    var readyPromiseResolve, readyPromiseReject
    Module['ready'] = new Promise(function (resolve, reject) {
      readyPromiseResolve = resolve
      readyPromiseReject = reject
    })
    var moduleOverrides = {}
    var key
    for (key in Module) {
      if (Module.hasOwnProperty(key)) {
        moduleOverrides[key] = Module[key]
      }
    }
    var arguments_ = []
    var thisProgram = './this.program'
    var quit_ = function (status, toThrow) {
      throw toThrow
    }
    var ENVIRONMENT_IS_WEB = false
    var ENVIRONMENT_IS_WORKER = false
    var ENVIRONMENT_IS_NODE = false
    var ENVIRONMENT_IS_SHELL = false
    ENVIRONMENT_IS_WEB = typeof window === 'object'
    ENVIRONMENT_IS_WORKER = typeof importScripts === 'function'
    ENVIRONMENT_IS_NODE =
      typeof process === 'object' &&
      typeof process.versions === 'object' &&
      typeof process.versions.node === 'string'
    ENVIRONMENT_IS_SHELL = !ENVIRONMENT_IS_WEB && !ENVIRONMENT_IS_NODE && !ENVIRONMENT_IS_WORKER
    var scriptDirectory = ''
    function locateFile(path) {
      if (Module['locateFile']) {
        return Module['locateFile'](path, scriptDirectory)
      }
      return scriptDirectory + path
    }
    var read_, readAsync, readBinary, setWindowTitle
    var nodeFS
    var nodePath
    if (ENVIRONMENT_IS_NODE) {
      if (ENVIRONMENT_IS_WORKER) {
        scriptDirectory = require('path').dirname(scriptDirectory) + '/'
      } else {
        scriptDirectory = __dirname + '/'
      }
      read_ = function shell_read(filename, binary) {
        if (!nodeFS) nodeFS = require('fs')
        if (!nodePath) nodePath = require('path')
        filename = nodePath['normalize'](filename)
        return nodeFS['readFileSync'](filename, binary ? null : 'utf8')
      }
      readBinary = function readBinary(filename) {
        var ret = read_(filename, true)
        if (!ret.buffer) {
          ret = new Uint8Array(ret)
        }
        assert(ret.buffer)
        return ret
      }
      if (process['argv'].length > 1) {
        thisProgram = process['argv'][1].replace(/\\/g, '/')
      }
      arguments_ = process['argv'].slice(2)
      process['on']('uncaughtException', function (ex) {
        if (!(ex instanceof ExitStatus)) {
          throw ex
        }
      })
      process['on']('unhandledRejection', abort)
      quit_ = function (status) {
        process['exit'](status)
      }
      Module['inspect'] = function () {
        return '[Emscripten Module object]'
      }
    } else if (ENVIRONMENT_IS_SHELL) {
      if (typeof read != 'undefined') {
        read_ = function shell_read(f) {
          return read(f)
        }
      }
      readBinary = function readBinary(f) {
        var data
        if (typeof readbuffer === 'function') {
          return new Uint8Array(readbuffer(f))
        }
        data = read(f, 'binary')
        assert(typeof data === 'object')
        return data
      }
      if (typeof scriptArgs != 'undefined') {
        arguments_ = scriptArgs
      } else if (typeof arguments != 'undefined') {
        arguments_ = arguments
      }
      if (typeof quit === 'function') {
        quit_ = function (status) {
          quit(status)
        }
      }
      if (typeof print !== 'undefined') {
        if (typeof console === 'undefined') console = {}
        console.log = print
        console.warn = console.error = typeof printErr !== 'undefined' ? printErr : print
      }
    } else if (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER) {
      if (ENVIRONMENT_IS_WORKER) {
        scriptDirectory = self.location.href
      } else if (typeof document !== 'undefined' && document.currentScript) {
        scriptDirectory = document.currentScript.src
      }
      if (_scriptDir) {
        scriptDirectory = _scriptDir
      }
      if (scriptDirectory.indexOf('blob:') !== 0) {
        scriptDirectory = scriptDirectory.substr(0, scriptDirectory.lastIndexOf('/') + 1)
      } else {
        scriptDirectory = ''
      }
      {
        read_ = function (url) {
          var xhr = new XMLHttpRequest()
          xhr.open('GET', url, false)
          xhr.send(null)
          return xhr.responseText
        }
        if (ENVIRONMENT_IS_WORKER) {
          readBinary = function (url) {
            var xhr = new XMLHttpRequest()
            xhr.open('GET', url, false)
            xhr.responseType = 'arraybuffer'
            xhr.send(null)
            return new Uint8Array(xhr.response)
          }
        }
        readAsync = function (url, onload, onerror) {
          var xhr = new XMLHttpRequest()
          xhr.open('GET', url, true)
          xhr.responseType = 'arraybuffer'
          xhr.onload = function () {
            if (xhr.status == 200 || (xhr.status == 0 && xhr.response)) {
              onload(xhr.response)
              return
            }
            onerror()
          }
          xhr.onerror = onerror
          xhr.send(null)
        }
      }
      setWindowTitle = function (title) {
        document.title = title
      }
    } else {
    }
    var out = Module['print'] || console.log.bind(console)
    var err = Module['printErr'] || console.warn.bind(console)
    for (key in moduleOverrides) {
      if (moduleOverrides.hasOwnProperty(key)) {
        Module[key] = moduleOverrides[key]
      }
    }
    moduleOverrides = null
    if (Module['arguments']) arguments_ = Module['arguments']
    if (Module['thisProgram']) thisProgram = Module['thisProgram']
    if (Module['quit']) quit_ = Module['quit']
    var tempRet0 = 0
    var setTempRet0 = function (value) {
      tempRet0 = value
    }
    var getTempRet0 = function () {
      return tempRet0
    }
    var wasmBinary
    if (Module['wasmBinary']) wasmBinary = Module['wasmBinary']
    var noExitRuntime = Module['noExitRuntime'] || true
    if (typeof WebAssembly !== 'object') {
      abort('no native wasm support detected')
    }
    var wasmMemory
    var ABORT = false
    var EXITSTATUS
    function assert(condition, text) {
      if (!condition) {
        abort('Assertion failed: ' + text)
      }
    }
    var UTF8Decoder = typeof TextDecoder !== 'undefined' ? new TextDecoder('utf8') : undefined
    function UTF8ArrayToString(heap, idx, maxBytesToRead) {
      var endIdx = idx + maxBytesToRead
      var endPtr = idx
      while (heap[endPtr] && !(endPtr >= endIdx)) ++endPtr
      if (endPtr - idx > 16 && heap.subarray && UTF8Decoder) {
        return UTF8Decoder.decode(heap.subarray(idx, endPtr))
      } else {
        var str = ''
        while (idx < endPtr) {
          var u0 = heap[idx++]
          if (!(u0 & 128)) {
            str += String.fromCharCode(u0)
            continue
          }
          var u1 = heap[idx++] & 63
          if ((u0 & 224) == 192) {
            str += String.fromCharCode(((u0 & 31) << 6) | u1)
            continue
          }
          var u2 = heap[idx++] & 63
          if ((u0 & 240) == 224) {
            u0 = ((u0 & 15) << 12) | (u1 << 6) | u2
          } else {
            u0 = ((u0 & 7) << 18) | (u1 << 12) | (u2 << 6) | (heap[idx++] & 63)
          }
          if (u0 < 65536) {
            str += String.fromCharCode(u0)
          } else {
            var ch = u0 - 65536
            str += String.fromCharCode(55296 | (ch >> 10), 56320 | (ch & 1023))
          }
        }
      }
      return str
    }
    function UTF8ToString(ptr, maxBytesToRead) {
      return ptr ? UTF8ArrayToString(HEAPU8, ptr, maxBytesToRead) : ''
    }
    function stringToUTF8Array(str, heap, outIdx, maxBytesToWrite) {
      if (!(maxBytesToWrite > 0)) return 0
      var startIdx = outIdx
      var endIdx = outIdx + maxBytesToWrite - 1
      for (var i = 0; i < str.length; ++i) {
        var u = str.charCodeAt(i)
        if (u >= 55296 && u <= 57343) {
          var u1 = str.charCodeAt(++i)
          u = (65536 + ((u & 1023) << 10)) | (u1 & 1023)
        }
        if (u <= 127) {
          if (outIdx >= endIdx) break
          heap[outIdx++] = u
        } else if (u <= 2047) {
          if (outIdx + 1 >= endIdx) break
          heap[outIdx++] = 192 | (u >> 6)
          heap[outIdx++] = 128 | (u & 63)
        } else if (u <= 65535) {
          if (outIdx + 2 >= endIdx) break
          heap[outIdx++] = 224 | (u >> 12)
          heap[outIdx++] = 128 | ((u >> 6) & 63)
          heap[outIdx++] = 128 | (u & 63)
        } else {
          if (outIdx + 3 >= endIdx) break
          heap[outIdx++] = 240 | (u >> 18)
          heap[outIdx++] = 128 | ((u >> 12) & 63)
          heap[outIdx++] = 128 | ((u >> 6) & 63)
          heap[outIdx++] = 128 | (u & 63)
        }
      }
      heap[outIdx] = 0
      return outIdx - startIdx
    }
    function stringToUTF8(str, outPtr, maxBytesToWrite) {
      return stringToUTF8Array(str, HEAPU8, outPtr, maxBytesToWrite)
    }
    function lengthBytesUTF8(str) {
      var len = 0
      for (var i = 0; i < str.length; ++i) {
        var u = str.charCodeAt(i)
        if (u >= 55296 && u <= 57343)
          u = (65536 + ((u & 1023) << 10)) | (str.charCodeAt(++i) & 1023)
        if (u <= 127) ++len
        else if (u <= 2047) len += 2
        else if (u <= 65535) len += 3
        else len += 4
      }
      return len
    }
    function allocateUTF8(str) {
      var size = lengthBytesUTF8(str) + 1
      var ret = _malloc(size)
      if (ret) stringToUTF8Array(str, HEAP8, ret, size)
      return ret
    }
    function writeArrayToMemory(array, buffer) {
      HEAP8.set(array, buffer)
    }
    function alignUp(x, multiple) {
      if (x % multiple > 0) {
        x += multiple - (x % multiple)
      }
      return x
    }
    var buffer, HEAP8, HEAPU8, HEAP16, HEAPU16, HEAP32, HEAPU32, HEAPF32, HEAPF64
    function updateGlobalBufferAndViews(buf) {
      buffer = buf
      Module['HEAP8'] = HEAP8 = new Int8Array(buf)
      Module['HEAP16'] = HEAP16 = new Int16Array(buf)
      Module['HEAP32'] = HEAP32 = new Int32Array(buf)
      Module['HEAPU8'] = HEAPU8 = new Uint8Array(buf)
      Module['HEAPU16'] = HEAPU16 = new Uint16Array(buf)
      Module['HEAPU32'] = HEAPU32 = new Uint32Array(buf)
      Module['HEAPF32'] = HEAPF32 = new Float32Array(buf)
      Module['HEAPF64'] = HEAPF64 = new Float64Array(buf)
    }
    var INITIAL_MEMORY = Module['INITIAL_MEMORY'] || 16777216
    var wasmTable
    var __ATPRERUN__ = []
    var __ATINIT__ = []
    var __ATMAIN__ = []
    var __ATEXIT__ = []
    var __ATPOSTRUN__ = []
    var runtimeInitialized = false
    __ATINIT__.push({
      func: function () {
        ___wasm_call_ctors()
      }
    })
    function preRun() {
      if (Module['preRun']) {
        if (typeof Module['preRun'] == 'function') Module['preRun'] = [Module['preRun']]
        while (Module['preRun'].length) {
          addOnPreRun(Module['preRun'].shift())
        }
      }
      callRuntimeCallbacks(__ATPRERUN__)
    }
    function initRuntime() {
      runtimeInitialized = true
      callRuntimeCallbacks(__ATINIT__)
    }
    function preMain() {
      callRuntimeCallbacks(__ATMAIN__)
    }
    function postRun() {
      if (Module['postRun']) {
        if (typeof Module['postRun'] == 'function') Module['postRun'] = [Module['postRun']]
        while (Module['postRun'].length) {
          addOnPostRun(Module['postRun'].shift())
        }
      }
      callRuntimeCallbacks(__ATPOSTRUN__)
    }
    function addOnPreRun(cb) {
      __ATPRERUN__.unshift(cb)
    }
    function addOnPostRun(cb) {
      __ATPOSTRUN__.unshift(cb)
    }
    var runDependencies = 0
    var runDependencyWatcher = null
    var dependenciesFulfilled = null
    function addRunDependency(id) {
      runDependencies++
      if (Module['monitorRunDependencies']) {
        Module['monitorRunDependencies'](runDependencies)
      }
    }
    function removeRunDependency(id) {
      runDependencies--
      if (Module['monitorRunDependencies']) {
        Module['monitorRunDependencies'](runDependencies)
      }
      if (runDependencies == 0) {
        if (runDependencyWatcher !== null) {
          clearInterval(runDependencyWatcher)
          runDependencyWatcher = null
        }
        if (dependenciesFulfilled) {
          var callback = dependenciesFulfilled
          dependenciesFulfilled = null
          callback()
        }
      }
    }
    Module['preloadedImages'] = {}
    Module['preloadedAudios'] = {}
    function abort(what) {
      if (Module['onAbort']) {
        Module['onAbort'](what)
      }
      what += ''
      err(what)
      ABORT = true
      EXITSTATUS = 1
      what = 'abort(' + what + '). Build with -s ASSERTIONS=1 for more info.'
      var e = new WebAssembly.RuntimeError(what)
      readyPromiseReject(e)
      throw e
    }
    function hasPrefix(str, prefix) {
      return String.prototype.startsWith ? str.startsWith(prefix) : str.indexOf(prefix) === 0
    }
    var dataURIPrefix = 'data:application/octet-stream;base64,'
    function isDataURI(filename) {
      return hasPrefix(filename, dataURIPrefix)
    }
    var fileURIPrefix = 'file://'
    function isFileURI(filename) {
      return hasPrefix(filename, fileURIPrefix)
    }
    var wasmBinaryFile = 'libRenderEngine.wasm'
    if (!isDataURI(wasmBinaryFile)) {
      wasmBinaryFile = locateFile(wasmBinaryFile)
    }
    function getBinary(file) {
      try {
        if (file == wasmBinaryFile && wasmBinary) {
          return new Uint8Array(wasmBinary)
        }
        if (readBinary) {
          return readBinary(file)
        } else {
          throw 'both async and sync fetching of the wasm failed'
        }
      } catch (err) {
        abort(err)
      }
    }
    function getBinaryPromise() {
      if (!wasmBinary && (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER)) {
        if (typeof fetch === 'function' && !isFileURI(wasmBinaryFile)) {
          return fetch(wasmBinaryFile, { credentials: 'same-origin' })
            .then(function (response) {
              if (!response['ok']) {
                throw "failed to load wasm binary file at '" + wasmBinaryFile + "'"
              }
              return response['arrayBuffer']()
            })
            .catch(function () {
              return getBinary(wasmBinaryFile)
            })
        } else {
          if (readAsync) {
            return new Promise(function (resolve, reject) {
              readAsync(
                wasmBinaryFile,
                function (response) {
                  resolve(new Uint8Array(response))
                },
                reject
              )
            })
          }
        }
      }
      return Promise.resolve().then(function () {
        return getBinary(wasmBinaryFile)
      })
    }
    function createWasm() {
      var info = { a: asmLibraryArg }
      function receiveInstance(instance, module) {
        var exports = instance.exports
        Module['asm'] = exports
        wasmMemory = Module['asm']['Ya']
        updateGlobalBufferAndViews(wasmMemory.buffer)
        wasmTable = Module['asm']['Jb']
        removeRunDependency('wasm-instantiate')
      }
      addRunDependency('wasm-instantiate')
      function receiveInstantiatedSource(output) {
        receiveInstance(output['instance'])
      }
      function instantiateArrayBuffer(receiver) {
        return getBinaryPromise()
          .then(function (binary) {
            return WebAssembly.instantiate(binary, info)
          })
          .then(receiver, function (reason) {
            err('failed to asynchronously prepare wasm: ' + reason)
            abort(reason)
          })
      }
      function instantiateAsync() {
        if (
          !wasmBinary &&
          typeof WebAssembly.instantiateStreaming === 'function' &&
          !isDataURI(wasmBinaryFile) &&
          !isFileURI(wasmBinaryFile) &&
          typeof fetch === 'function'
        ) {
          return fetch(wasmBinaryFile, { credentials: 'same-origin' }).then(function (response) {
            var result = WebAssembly.instantiateStreaming(response, info)
            return result.then(receiveInstantiatedSource, function (reason) {
              err('wasm streaming compile failed: ' + reason)
              err('falling back to ArrayBuffer instantiation')
              return instantiateArrayBuffer(receiveInstantiatedSource)
            })
          })
        } else {
          return instantiateArrayBuffer(receiveInstantiatedSource)
        }
      }
      if (Module['instantiateWasm']) {
        try {
          var exports = Module['instantiateWasm'](info, receiveInstance)
          return exports
        } catch (e) {
          err('Module.instantiateWasm callback failed with error: ' + e)
          return false
        }
      }
      instantiateAsync().catch(readyPromiseReject)
      return {}
    }
    function callRuntimeCallbacks(callbacks) {
      while (callbacks.length > 0) {
        var callback = callbacks.shift()
        if (typeof callback == 'function') {
          callback(Module)
          continue
        }
        var func = callback.func
        if (typeof func === 'number') {
          if (callback.arg === undefined) {
            wasmTable.get(func)()
          } else {
            wasmTable.get(func)(callback.arg)
          }
        } else {
          func(callback.arg === undefined ? null : callback.arg)
        }
      }
    }
    function _JS_glTexImage2D(target, level, internalformat, format, type, data) {
      let frame = window.WebCodecsVideoFrameMap.get(data)
      if (!frame) {
        console.log('[Error] The frame is invalid in JS_glTexImage2D!')
        return
      }
      GLctx.texImage2D(target, level, internalformat, format, type, frame)
      frame.close()
      window.WebCodecsVideoFrameMap.delete(data)
    }
    function ___assert_fail(condition, filename, line, func) {
      abort(
        'Assertion failed: ' +
          UTF8ToString(condition) +
          ', at: ' +
          [
            filename ? UTF8ToString(filename) : 'unknown filename',
            line,
            func ? UTF8ToString(func) : 'unknown function'
          ]
      )
    }
    var ExceptionInfoAttrs = {
      DESTRUCTOR_OFFSET: 0,
      REFCOUNT_OFFSET: 4,
      TYPE_OFFSET: 8,
      CAUGHT_OFFSET: 12,
      RETHROWN_OFFSET: 13,
      SIZE: 16
    }
    function ___cxa_allocate_exception(size) {
      return _malloc(size + ExceptionInfoAttrs.SIZE) + ExceptionInfoAttrs.SIZE
    }
    function ExceptionInfo(excPtr) {
      this.excPtr = excPtr
      this.ptr = excPtr - ExceptionInfoAttrs.SIZE
      this.set_type = function (type) {
        HEAP32[(this.ptr + ExceptionInfoAttrs.TYPE_OFFSET) >> 2] = type
      }
      this.get_type = function () {
        return HEAP32[(this.ptr + ExceptionInfoAttrs.TYPE_OFFSET) >> 2]
      }
      this.set_destructor = function (destructor) {
        HEAP32[(this.ptr + ExceptionInfoAttrs.DESTRUCTOR_OFFSET) >> 2] = destructor
      }
      this.get_destructor = function () {
        return HEAP32[(this.ptr + ExceptionInfoAttrs.DESTRUCTOR_OFFSET) >> 2]
      }
      this.set_refcount = function (refcount) {
        HEAP32[(this.ptr + ExceptionInfoAttrs.REFCOUNT_OFFSET) >> 2] = refcount
      }
      this.set_caught = function (caught) {
        caught = caught ? 1 : 0
        HEAP8[(this.ptr + ExceptionInfoAttrs.CAUGHT_OFFSET) >> 0] = caught
      }
      this.get_caught = function () {
        return HEAP8[(this.ptr + ExceptionInfoAttrs.CAUGHT_OFFSET) >> 0] != 0
      }
      this.set_rethrown = function (rethrown) {
        rethrown = rethrown ? 1 : 0
        HEAP8[(this.ptr + ExceptionInfoAttrs.RETHROWN_OFFSET) >> 0] = rethrown
      }
      this.get_rethrown = function () {
        return HEAP8[(this.ptr + ExceptionInfoAttrs.RETHROWN_OFFSET) >> 0] != 0
      }
      this.init = function (type, destructor) {
        this.set_type(type)
        this.set_destructor(destructor)
        this.set_refcount(0)
        this.set_caught(false)
        this.set_rethrown(false)
      }
      this.add_ref = function () {
        var value = HEAP32[(this.ptr + ExceptionInfoAttrs.REFCOUNT_OFFSET) >> 2]
        HEAP32[(this.ptr + ExceptionInfoAttrs.REFCOUNT_OFFSET) >> 2] = value + 1
      }
      this.release_ref = function () {
        var prev = HEAP32[(this.ptr + ExceptionInfoAttrs.REFCOUNT_OFFSET) >> 2]
        HEAP32[(this.ptr + ExceptionInfoAttrs.REFCOUNT_OFFSET) >> 2] = prev - 1
        return prev === 1
      }
    }
    function CatchInfo(ptr) {
      this.free = function () {
        _free(this.ptr)
        this.ptr = 0
      }
      this.set_base_ptr = function (basePtr) {
        HEAP32[this.ptr >> 2] = basePtr
      }
      this.get_base_ptr = function () {
        return HEAP32[this.ptr >> 2]
      }
      this.set_adjusted_ptr = function (adjustedPtr) {
        var ptrSize = 4
        HEAP32[(this.ptr + ptrSize) >> 2] = adjustedPtr
      }
      this.get_adjusted_ptr = function () {
        var ptrSize = 4
        return HEAP32[(this.ptr + ptrSize) >> 2]
      }
      this.get_exception_ptr = function () {
        var isPointer = ___cxa_is_pointer_type(this.get_exception_info().get_type())
        if (isPointer) {
          return HEAP32[this.get_base_ptr() >> 2]
        }
        var adjusted = this.get_adjusted_ptr()
        if (adjusted !== 0) return adjusted
        return this.get_base_ptr()
      }
      this.get_exception_info = function () {
        return new ExceptionInfo(this.get_base_ptr())
      }
      if (ptr === undefined) {
        this.ptr = _malloc(8)
        this.set_adjusted_ptr(0)
      } else {
        this.ptr = ptr
      }
    }
    var exceptionCaught = []
    function exception_addRef(info) {
      info.add_ref()
    }
    var uncaughtExceptionCount = 0
    function ___cxa_begin_catch(ptr) {
      var catchInfo = new CatchInfo(ptr)
      var info = catchInfo.get_exception_info()
      if (!info.get_caught()) {
        info.set_caught(true)
        uncaughtExceptionCount--
      }
      info.set_rethrown(false)
      exceptionCaught.push(catchInfo)
      exception_addRef(info)
      return catchInfo.get_exception_ptr()
    }
    var exceptionLast = 0
    function ___cxa_free_exception(ptr) {
      return _free(new ExceptionInfo(ptr).ptr)
    }
    function exception_decRef(info) {
      if (info.release_ref() && !info.get_rethrown()) {
        var destructor = info.get_destructor()
        if (destructor) {
          wasmTable.get(destructor)(info.excPtr)
        }
        ___cxa_free_exception(info.excPtr)
      }
    }
    function ___cxa_end_catch() {
      _setThrew(0)
      var catchInfo = exceptionCaught.pop()
      exception_decRef(catchInfo.get_exception_info())
      catchInfo.free()
      exceptionLast = 0
    }
    function ___resumeException(catchInfoPtr) {
      var catchInfo = new CatchInfo(catchInfoPtr)
      var ptr = catchInfo.get_base_ptr()
      if (!exceptionLast) {
        exceptionLast = ptr
      }
      catchInfo.free()
      throw ptr
    }
    function ___cxa_find_matching_catch_2() {
      var thrown = exceptionLast
      if (!thrown) {
        setTempRet0(0 | 0)
        return 0 | 0
      }
      var info = new ExceptionInfo(thrown)
      var thrownType = info.get_type()
      var catchInfo = new CatchInfo()
      catchInfo.set_base_ptr(thrown)
      if (!thrownType) {
        setTempRet0(0 | 0)
        return catchInfo.ptr | 0
      }
      var typeArray = Array.prototype.slice.call(arguments)
      var stackTop = stackSave()
      var exceptionThrowBuf = stackAlloc(4)
      HEAP32[exceptionThrowBuf >> 2] = thrown
      for (var i = 0; i < typeArray.length; i++) {
        var caughtType = typeArray[i]
        if (caughtType === 0 || caughtType === thrownType) {
          break
        }
        if (___cxa_can_catch(caughtType, thrownType, exceptionThrowBuf)) {
          var adjusted = HEAP32[exceptionThrowBuf >> 2]
          if (thrown !== adjusted) {
            catchInfo.set_adjusted_ptr(adjusted)
          }
          setTempRet0(caughtType | 0)
          return catchInfo.ptr | 0
        }
      }
      stackRestore(stackTop)
      setTempRet0(thrownType | 0)
      return catchInfo.ptr | 0
    }
    function ___cxa_find_matching_catch_3() {
      var thrown = exceptionLast
      if (!thrown) {
        setTempRet0(0 | 0)
        return 0 | 0
      }
      var info = new ExceptionInfo(thrown)
      var thrownType = info.get_type()
      var catchInfo = new CatchInfo()
      catchInfo.set_base_ptr(thrown)
      if (!thrownType) {
        setTempRet0(0 | 0)
        return catchInfo.ptr | 0
      }
      var typeArray = Array.prototype.slice.call(arguments)
      var stackTop = stackSave()
      var exceptionThrowBuf = stackAlloc(4)
      HEAP32[exceptionThrowBuf >> 2] = thrown
      for (var i = 0; i < typeArray.length; i++) {
        var caughtType = typeArray[i]
        if (caughtType === 0 || caughtType === thrownType) {
          break
        }
        if (___cxa_can_catch(caughtType, thrownType, exceptionThrowBuf)) {
          var adjusted = HEAP32[exceptionThrowBuf >> 2]
          if (thrown !== adjusted) {
            catchInfo.set_adjusted_ptr(adjusted)
          }
          setTempRet0(caughtType | 0)
          return catchInfo.ptr | 0
        }
      }
      stackRestore(stackTop)
      setTempRet0(thrownType | 0)
      return catchInfo.ptr | 0
    }
    function ___cxa_find_matching_catch_4() {
      var thrown = exceptionLast
      if (!thrown) {
        setTempRet0(0 | 0)
        return 0 | 0
      }
      var info = new ExceptionInfo(thrown)
      var thrownType = info.get_type()
      var catchInfo = new CatchInfo()
      catchInfo.set_base_ptr(thrown)
      if (!thrownType) {
        setTempRet0(0 | 0)
        return catchInfo.ptr | 0
      }
      var typeArray = Array.prototype.slice.call(arguments)
      var stackTop = stackSave()
      var exceptionThrowBuf = stackAlloc(4)
      HEAP32[exceptionThrowBuf >> 2] = thrown
      for (var i = 0; i < typeArray.length; i++) {
        var caughtType = typeArray[i]
        if (caughtType === 0 || caughtType === thrownType) {
          break
        }
        if (___cxa_can_catch(caughtType, thrownType, exceptionThrowBuf)) {
          var adjusted = HEAP32[exceptionThrowBuf >> 2]
          if (thrown !== adjusted) {
            catchInfo.set_adjusted_ptr(adjusted)
          }
          setTempRet0(caughtType | 0)
          return catchInfo.ptr | 0
        }
      }
      stackRestore(stackTop)
      setTempRet0(thrownType | 0)
      return catchInfo.ptr | 0
    }
    function ___cxa_rethrow() {
      var catchInfo = exceptionCaught.pop()
      if (!catchInfo) {
        abort('no exception to throw')
      }
      var info = catchInfo.get_exception_info()
      var ptr = catchInfo.get_base_ptr()
      if (!info.get_rethrown()) {
        exceptionCaught.push(catchInfo)
        info.set_rethrown(true)
        info.set_caught(false)
        uncaughtExceptionCount++
      } else {
        catchInfo.free()
      }
      exceptionLast = ptr
      throw ptr
    }
    function ___cxa_throw(ptr, type, destructor) {
      var info = new ExceptionInfo(ptr)
      info.init(type, destructor)
      exceptionLast = ptr
      uncaughtExceptionCount++
      throw ptr
    }
    function _abort() {
      abort()
    }
    function _emscripten_memcpy_big(dest, src, num) {
      HEAPU8.copyWithin(dest, src, src + num)
    }
    function _emscripten_get_heap_size() {
      return HEAPU8.length
    }
    function emscripten_realloc_buffer(size) {
      try {
        wasmMemory.grow((size - buffer.byteLength + 65535) >>> 16)
        updateGlobalBufferAndViews(wasmMemory.buffer)
        return 1
      } catch (e) {}
    }
    function _emscripten_resize_heap(requestedSize) {
      var oldSize = _emscripten_get_heap_size()
      var maxHeapSize = 2147483648
      if (requestedSize > maxHeapSize) {
        return false
      }
      for (var cutDown = 1; cutDown <= 4; cutDown *= 2) {
        var overGrownHeapSize = oldSize * (1 + 0.2 / cutDown)
        overGrownHeapSize = Math.min(overGrownHeapSize, requestedSize + 100663296)
        var newSize = Math.min(
          maxHeapSize,
          alignUp(Math.max(requestedSize, overGrownHeapSize), 65536)
        )
        var replacement = emscripten_realloc_buffer(newSize)
        if (replacement) {
          return true
        }
      }
      return false
    }
    function __webgl_enable_ANGLE_instanced_arrays(ctx) {
      var ext = ctx.getExtension('ANGLE_instanced_arrays')
      if (ext) {
        ctx['vertexAttribDivisor'] = function (index, divisor) {
          ext['vertexAttribDivisorANGLE'](index, divisor)
        }
        ctx['drawArraysInstanced'] = function (mode, first, count, primcount) {
          ext['drawArraysInstancedANGLE'](mode, first, count, primcount)
        }
        ctx['drawElementsInstanced'] = function (mode, count, type, indices, primcount) {
          ext['drawElementsInstancedANGLE'](mode, count, type, indices, primcount)
        }
        return 1
      }
    }
    function __webgl_enable_OES_vertex_array_object(ctx) {
      var ext = ctx.getExtension('OES_vertex_array_object')
      if (ext) {
        ctx['createVertexArray'] = function () {
          return ext['createVertexArrayOES']()
        }
        ctx['deleteVertexArray'] = function (vao) {
          ext['deleteVertexArrayOES'](vao)
        }
        ctx['bindVertexArray'] = function (vao) {
          ext['bindVertexArrayOES'](vao)
        }
        ctx['isVertexArray'] = function (vao) {
          return ext['isVertexArrayOES'](vao)
        }
        return 1
      }
    }
    function __webgl_enable_WEBGL_draw_buffers(ctx) {
      var ext = ctx.getExtension('WEBGL_draw_buffers')
      if (ext) {
        ctx['drawBuffers'] = function (n, bufs) {
          ext['drawBuffersWEBGL'](n, bufs)
        }
        return 1
      }
    }
    function __webgl_enable_WEBGL_draw_instanced_base_vertex_base_instance(ctx) {
      return !!(ctx.dibvbi = ctx.getExtension('WEBGL_draw_instanced_base_vertex_base_instance'))
    }
    function __webgl_enable_WEBGL_multi_draw_instanced_base_vertex_base_instance(ctx) {
      return !!(ctx.mdibvbi = ctx.getExtension(
        'WEBGL_multi_draw_instanced_base_vertex_base_instance'
      ))
    }
    function __webgl_enable_WEBGL_multi_draw(ctx) {
      return !!(ctx.multiDrawWebgl = ctx.getExtension('WEBGL_multi_draw'))
    }
    var GL = {
      counter: 1,
      buffers: [],
      programs: [],
      framebuffers: [],
      renderbuffers: [],
      textures: [],
      uniforms: [],
      shaders: [],
      vaos: [],
      contexts: [],
      offscreenCanvases: {},
      timerQueriesEXT: [],
      queries: [],
      samplers: [],
      transformFeedbacks: [],
      syncs: [],
      programInfos: {},
      stringCache: {},
      stringiCache: {},
      unpackAlignment: 4,
      recordError: function recordError(errorCode) {
        if (!GL.lastError) {
          GL.lastError = errorCode
        }
      },
      getNewId: function (table) {
        var ret = GL.counter++
        for (var i = table.length; i < ret; i++) {
          table[i] = null
        }
        return ret
      },
      getSource: function (shader, count, string, length) {
        var source = ''
        for (var i = 0; i < count; ++i) {
          var len = length ? HEAP32[(length + i * 4) >> 2] : -1
          source += UTF8ToString(HEAP32[(string + i * 4) >> 2], len < 0 ? undefined : len)
        }
        return source
      },
      createContext: function (canvas, webGLContextAttributes) {
        var ctx =
          webGLContextAttributes.majorVersion > 1
            ? canvas.getContext('webgl2', webGLContextAttributes)
            : canvas.getContext('webgl', webGLContextAttributes)
        if (!ctx) return 0
        var handle = GL.registerContext(ctx, webGLContextAttributes)
        return handle
      },
      registerContext: function (ctx, webGLContextAttributes) {
        var handle = GL.getNewId(GL.contexts)
        var context = {
          handle: handle,
          attributes: webGLContextAttributes,
          version: webGLContextAttributes.majorVersion,
          GLctx: ctx
        }
        if (ctx.canvas) ctx.canvas.GLctxObject = context
        GL.contexts[handle] = context
        if (
          typeof webGLContextAttributes.enableExtensionsByDefault === 'undefined' ||
          webGLContextAttributes.enableExtensionsByDefault
        ) {
          GL.initExtensions(context)
        }
        return handle
      },
      makeContextCurrent: function (contextHandle) {
        GL.currentContext = GL.contexts[contextHandle]
        Module.ctx = GLctx = GL.currentContext && GL.currentContext.GLctx
        return !(contextHandle && !GLctx)
      },
      getContext: function (contextHandle) {
        return GL.contexts[contextHandle]
      },
      deleteContext: function (contextHandle) {
        if (GL.currentContext === GL.contexts[contextHandle]) GL.currentContext = null
        if (typeof JSEvents === 'object')
          JSEvents.removeAllHandlersOnTarget(GL.contexts[contextHandle].GLctx.canvas)
        if (GL.contexts[contextHandle] && GL.contexts[contextHandle].GLctx.canvas)
          GL.contexts[contextHandle].GLctx.canvas.GLctxObject = undefined
        GL.contexts[contextHandle] = null
      },
      initExtensions: function (context) {
        if (!context) context = GL.currentContext
        if (context.initExtensionsDone) return
        context.initExtensionsDone = true
        var GLctx = context.GLctx
        __webgl_enable_ANGLE_instanced_arrays(GLctx)
        __webgl_enable_OES_vertex_array_object(GLctx)
        __webgl_enable_WEBGL_draw_buffers(GLctx)
        __webgl_enable_WEBGL_draw_instanced_base_vertex_base_instance(GLctx)
        __webgl_enable_WEBGL_multi_draw_instanced_base_vertex_base_instance(GLctx)
        GLctx.disjointTimerQueryExt = GLctx.getExtension('EXT_disjoint_timer_query')
        __webgl_enable_WEBGL_multi_draw(GLctx)
        var exts = GLctx.getSupportedExtensions() || []
        exts.forEach(function (ext) {
          if (ext.indexOf('lose_context') < 0 && ext.indexOf('debug') < 0) {
            GLctx.getExtension(ext)
          }
        })
      },
      populateUniformTable: function (program) {
        var p = GL.programs[program]
        var ptable = (GL.programInfos[program] = {
          uniforms: {},
          maxUniformLength: 0,
          maxAttributeLength: -1,
          maxUniformBlockNameLength: -1
        })
        var utable = ptable.uniforms
        var numUniforms = GLctx.getProgramParameter(p, 35718)
        for (var i = 0; i < numUniforms; ++i) {
          var u = GLctx.getActiveUniform(p, i)
          var name = u.name
          ptable.maxUniformLength = Math.max(ptable.maxUniformLength, name.length + 1)
          if (name.slice(-1) == ']') {
            name = name.slice(0, name.lastIndexOf('['))
          }
          var loc = GLctx.getUniformLocation(p, name)
          if (loc) {
            var id = GL.getNewId(GL.uniforms)
            utable[name] = [u.size, id]
            GL.uniforms[id] = loc
            for (var j = 1; j < u.size; ++j) {
              var n = name + '[' + j + ']'
              loc = GLctx.getUniformLocation(p, n)
              id = GL.getNewId(GL.uniforms)
              GL.uniforms[id] = loc
            }
          }
        }
      }
    }
    var JSEvents = {
      inEventHandler: 0,
      removeAllEventListeners: function () {
        for (var i = JSEvents.eventHandlers.length - 1; i >= 0; --i) {
          JSEvents._removeHandler(i)
        }
        JSEvents.eventHandlers = []
        JSEvents.deferredCalls = []
      },
      registerRemoveEventListeners: function () {
        if (!JSEvents.removeEventListenersRegistered) {
          __ATEXIT__.push(JSEvents.removeAllEventListeners)
          JSEvents.removeEventListenersRegistered = true
        }
      },
      deferredCalls: [],
      deferCall: function (targetFunction, precedence, argsList) {
        function arraysHaveEqualContent(arrA, arrB) {
          if (arrA.length != arrB.length) return false
          for (var i in arrA) {
            if (arrA[i] != arrB[i]) return false
          }
          return true
        }
        for (var i in JSEvents.deferredCalls) {
          var call = JSEvents.deferredCalls[i]
          if (
            call.targetFunction == targetFunction &&
            arraysHaveEqualContent(call.argsList, argsList)
          ) {
            return
          }
        }
        JSEvents.deferredCalls.push({
          targetFunction: targetFunction,
          precedence: precedence,
          argsList: argsList
        })
        JSEvents.deferredCalls.sort(function (x, y) {
          return x.precedence < y.precedence
        })
      },
      removeDeferredCalls: function (targetFunction) {
        for (var i = 0; i < JSEvents.deferredCalls.length; ++i) {
          if (JSEvents.deferredCalls[i].targetFunction == targetFunction) {
            JSEvents.deferredCalls.splice(i, 1)
            --i
          }
        }
      },
      canPerformEventHandlerRequests: function () {
        return JSEvents.inEventHandler && JSEvents.currentEventHandler.allowsDeferredCalls
      },
      runDeferredCalls: function () {
        if (!JSEvents.canPerformEventHandlerRequests()) {
          return
        }
        for (var i = 0; i < JSEvents.deferredCalls.length; ++i) {
          var call = JSEvents.deferredCalls[i]
          JSEvents.deferredCalls.splice(i, 1)
          --i
          call.targetFunction.apply(null, call.argsList)
        }
      },
      eventHandlers: [],
      removeAllHandlersOnTarget: function (target, eventTypeString) {
        for (var i = 0; i < JSEvents.eventHandlers.length; ++i) {
          if (
            JSEvents.eventHandlers[i].target == target &&
            (!eventTypeString || eventTypeString == JSEvents.eventHandlers[i].eventTypeString)
          ) {
            JSEvents._removeHandler(i--)
          }
        }
      },
      _removeHandler: function (i) {
        var h = JSEvents.eventHandlers[i]
        h.target.removeEventListener(h.eventTypeString, h.eventListenerFunc, h.useCapture)
        JSEvents.eventHandlers.splice(i, 1)
      },
      registerOrRemoveHandler: function (eventHandler) {
        var jsEventHandler = function jsEventHandler(event) {
          ++JSEvents.inEventHandler
          JSEvents.currentEventHandler = eventHandler
          JSEvents.runDeferredCalls()
          eventHandler.handlerFunc(event)
          JSEvents.runDeferredCalls()
          --JSEvents.inEventHandler
        }
        if (eventHandler.callbackfunc) {
          eventHandler.eventListenerFunc = jsEventHandler
          eventHandler.target.addEventListener(
            eventHandler.eventTypeString,
            jsEventHandler,
            eventHandler.useCapture
          )
          JSEvents.eventHandlers.push(eventHandler)
          JSEvents.registerRemoveEventListeners()
        } else {
          for (var i = 0; i < JSEvents.eventHandlers.length; ++i) {
            if (
              JSEvents.eventHandlers[i].target == eventHandler.target &&
              JSEvents.eventHandlers[i].eventTypeString == eventHandler.eventTypeString
            ) {
              JSEvents._removeHandler(i--)
            }
          }
        }
      },
      getNodeNameForTarget: function (target) {
        if (!target) return ''
        if (target == window) return '#window'
        if (target == screen) return '#screen'
        return target && target.nodeName ? target.nodeName : ''
      },
      fullscreenEnabled: function () {
        return document.fullscreenEnabled || document.webkitFullscreenEnabled
      }
    }
    var __emscripten_webgl_power_preferences = ['default', 'low-power', 'high-performance']
    function maybeCStringToJsString(cString) {
      return cString > 2 ? UTF8ToString(cString) : cString
    }
    var specialHTMLTargets = [
      0,
      typeof document !== 'undefined' ? document : 0,
      typeof window !== 'undefined' ? window : 0
    ]
    function findEventTarget(target) {
      target = maybeCStringToJsString(target)
      var domElement =
        specialHTMLTargets[target] ||
        (typeof document !== 'undefined' ? document.querySelector(target) : undefined)
      return domElement
    }
    function findCanvasEventTarget(target) {
      return findEventTarget(target)
    }
    function _emscripten_webgl_do_create_context(target, attributes) {
      var a = attributes >> 2
      var powerPreference = HEAP32[a + (24 >> 2)]
      var contextAttributes = {
        alpha: !!HEAP32[a + (0 >> 2)],
        depth: !!HEAP32[a + (4 >> 2)],
        stencil: !!HEAP32[a + (8 >> 2)],
        antialias: !!HEAP32[a + (12 >> 2)],
        premultipliedAlpha: !!HEAP32[a + (16 >> 2)],
        preserveDrawingBuffer: !!HEAP32[a + (20 >> 2)],
        powerPreference: __emscripten_webgl_power_preferences[powerPreference],
        failIfMajorPerformanceCaveat: !!HEAP32[a + (28 >> 2)],
        majorVersion: HEAP32[a + (32 >> 2)],
        minorVersion: HEAP32[a + (36 >> 2)],
        enableExtensionsByDefault: HEAP32[a + (40 >> 2)],
        explicitSwapControl: HEAP32[a + (44 >> 2)],
        proxyContextToMainThread: HEAP32[a + (48 >> 2)],
        renderViaOffscreenBackBuffer: HEAP32[a + (52 >> 2)]
      }
      var canvas = findCanvasEventTarget(target)
      if (!canvas) {
        return 0
      }
      if (contextAttributes.explicitSwapControl) {
        return 0
      }
      var contextHandle = GL.createContext(canvas, contextAttributes)
      return contextHandle
    }
    function _emscripten_webgl_create_context(a0, a1) {
      return _emscripten_webgl_do_create_context(a0, a1)
    }
    function _emscripten_webgl_do_get_current_context() {
      return GL.currentContext ? GL.currentContext.handle : 0
    }
    function _emscripten_webgl_get_current_context() {
      return _emscripten_webgl_do_get_current_context()
    }
    Module['_emscripten_webgl_get_current_context'] = _emscripten_webgl_get_current_context
    function _emscripten_webgl_make_context_current(contextHandle) {
      var success = GL.makeContextCurrent(contextHandle)
      return success ? 0 : -5
    }
    Module['_emscripten_webgl_make_context_current'] = _emscripten_webgl_make_context_current
    function _emscripten_webgl_destroy_context(contextHandle) {
      if (GL.currentContext == contextHandle) GL.currentContext = 0
      GL.deleteContext(contextHandle)
    }
    function _emscripten_webgl_get_drawing_buffer_size(contextHandle, width, height) {
      var GLContext = GL.getContext(contextHandle)
      if (!GLContext || !GLContext.GLctx || !width || !height) {
        return -5
      }
      HEAP32[width >> 2] = GLContext.GLctx.drawingBufferWidth
      HEAP32[height >> 2] = GLContext.GLctx.drawingBufferHeight
      return 0
    }
    function _emscripten_webgl_init_context_attributes(attributes) {
      var a = attributes >> 2
      for (var i = 0; i < 56 >> 2; ++i) {
        HEAP32[a + i] = 0
      }
      HEAP32[a + (0 >> 2)] =
        HEAP32[a + (4 >> 2)] =
        HEAP32[a + (12 >> 2)] =
        HEAP32[a + (16 >> 2)] =
        HEAP32[a + (32 >> 2)] =
        HEAP32[a + (40 >> 2)] =
          1
    }
    var SYSCALLS = {
      mappings: {},
      buffers: [null, [], []],
      printChar: function (stream, curr) {
        var buffer = SYSCALLS.buffers[stream]
        if (curr === 0 || curr === 10) {
          ;(stream === 1 ? out : err)(UTF8ArrayToString(buffer, 0))
          buffer.length = 0
        } else {
          buffer.push(curr)
        }
      },
      varargs: undefined,
      get: function () {
        SYSCALLS.varargs += 4
        var ret = HEAP32[(SYSCALLS.varargs - 4) >> 2]
        return ret
      },
      getStr: function (ptr) {
        var ret = UTF8ToString(ptr)
        return ret
      },
      get64: function (low, high) {
        return low
      }
    }
    function _fd_close(fd) {
      return 0
    }
    function _fd_write(fd, iov, iovcnt, pnum) {
      var num = 0
      for (var i = 0; i < iovcnt; i++) {
        var ptr = HEAP32[(iov + i * 8) >> 2]
        var len = HEAP32[(iov + (i * 8 + 4)) >> 2]
        for (var j = 0; j < len; j++) {
          SYSCALLS.printChar(fd, HEAPU8[ptr + j])
        }
        num += len
      }
      HEAP32[pnum >> 2] = num
      return 0
    }
    function _getTempRet0() {
      return getTempRet0() | 0
    }
    function _gettimeofday(ptr) {
      var now = Date.now()
      HEAP32[ptr >> 2] = (now / 1e3) | 0
      HEAP32[(ptr + 4) >> 2] = ((now % 1e3) * 1e3) | 0
      return 0
    }
    function _glActiveTexture(x0) {
      GLctx['activeTexture'](x0)
    }
    function _glAttachShader(program, shader) {
      GLctx.attachShader(GL.programs[program], GL.shaders[shader])
    }
    function _glBindBuffer(target, buffer) {
      if (target == 35051) {
        GLctx.currentPixelPackBufferBinding = buffer
      } else if (target == 35052) {
        GLctx.currentPixelUnpackBufferBinding = buffer
      }
      GLctx.bindBuffer(target, GL.buffers[buffer])
    }
    function _glBindFramebuffer(target, framebuffer) {
      GLctx.bindFramebuffer(target, GL.framebuffers[framebuffer])
    }
    function _glBindTexture(target, texture) {
      GLctx.bindTexture(target, GL.textures[texture])
    }
    function _glBlendFunc(x0, x1) {
      GLctx['blendFunc'](x0, x1)
    }
    function _glBlitFramebuffer(x0, x1, x2, x3, x4, x5, x6, x7, x8, x9) {
      GLctx['blitFramebuffer'](x0, x1, x2, x3, x4, x5, x6, x7, x8, x9)
    }
    function _glBufferData(target, size, data, usage) {
      if (GL.currentContext.version >= 2) {
        if (data) {
          GLctx.bufferData(target, HEAPU8, usage, data, size)
        } else {
          GLctx.bufferData(target, size, usage)
        }
      } else {
        GLctx.bufferData(target, data ? HEAPU8.subarray(data, data + size) : size, usage)
      }
    }
    function _glCheckFramebufferStatus(x0) {
      return GLctx['checkFramebufferStatus'](x0)
    }
    function _glClear(x0) {
      GLctx['clear'](x0)
    }
    function _glClearColor(x0, x1, x2, x3) {
      GLctx['clearColor'](x0, x1, x2, x3)
    }
    function _glCompileShader(shader) {
      GLctx.compileShader(GL.shaders[shader])
    }
    function _glCreateProgram() {
      var id = GL.getNewId(GL.programs)
      var program = GLctx.createProgram()
      program.name = id
      GL.programs[id] = program
      return id
    }
    function _glCreateShader(shaderType) {
      var id = GL.getNewId(GL.shaders)
      GL.shaders[id] = GLctx.createShader(shaderType)
      return id
    }
    function _glDeleteBuffers(n, buffers) {
      for (var i = 0; i < n; i++) {
        var id = HEAP32[(buffers + i * 4) >> 2]
        var buffer = GL.buffers[id]
        if (!buffer) continue
        GLctx.deleteBuffer(buffer)
        buffer.name = 0
        GL.buffers[id] = null
        if (id == GLctx.currentPixelPackBufferBinding) GLctx.currentPixelPackBufferBinding = 0
        if (id == GLctx.currentPixelUnpackBufferBinding) GLctx.currentPixelUnpackBufferBinding = 0
      }
    }
    function _glDeleteFramebuffers(n, framebuffers) {
      for (var i = 0; i < n; ++i) {
        var id = HEAP32[(framebuffers + i * 4) >> 2]
        var framebuffer = GL.framebuffers[id]
        if (!framebuffer) continue
        GLctx.deleteFramebuffer(framebuffer)
        framebuffer.name = 0
        GL.framebuffers[id] = null
      }
    }
    function _glDeleteProgram(id) {
      if (!id) return
      var program = GL.programs[id]
      if (!program) {
        GL.recordError(1281)
        return
      }
      GLctx.deleteProgram(program)
      program.name = 0
      GL.programs[id] = null
      GL.programInfos[id] = null
    }
    function _glDeleteRenderbuffers(n, renderbuffers) {
      for (var i = 0; i < n; i++) {
        var id = HEAP32[(renderbuffers + i * 4) >> 2]
        var renderbuffer = GL.renderbuffers[id]
        if (!renderbuffer) continue
        GLctx.deleteRenderbuffer(renderbuffer)
        renderbuffer.name = 0
        GL.renderbuffers[id] = null
      }
    }
    function _glDeleteShader(id) {
      if (!id) return
      var shader = GL.shaders[id]
      if (!shader) {
        GL.recordError(1281)
        return
      }
      GLctx.deleteShader(shader)
      GL.shaders[id] = null
    }
    function _glDeleteTextures(n, textures) {
      for (var i = 0; i < n; i++) {
        var id = HEAP32[(textures + i * 4) >> 2]
        var texture = GL.textures[id]
        if (!texture) continue
        GLctx.deleteTexture(texture)
        texture.name = 0
        GL.textures[id] = null
      }
    }
    function _glDisable(x0) {
      GLctx['disable'](x0)
    }
    function _glDisableVertexAttribArray(index) {
      GLctx.disableVertexAttribArray(index)
    }
    function _glDrawArrays(mode, first, count) {
      GLctx.drawArrays(mode, first, count)
    }
    function _glDrawElements(mode, count, type, indices) {
      GLctx.drawElements(mode, count, type, indices)
    }
    function _glEnable(x0) {
      GLctx['enable'](x0)
    }
    function _glEnableVertexAttribArray(index) {
      GLctx.enableVertexAttribArray(index)
    }
    function _glFramebufferTexture2D(target, attachment, textarget, texture, level) {
      GLctx.framebufferTexture2D(target, attachment, textarget, GL.textures[texture], level)
    }
    function __glGenObject(n, buffers, createFunction, objectTable) {
      for (var i = 0; i < n; i++) {
        var buffer = GLctx[createFunction]()
        var id = buffer && GL.getNewId(objectTable)
        if (buffer) {
          buffer.name = id
          objectTable[id] = buffer
        } else {
          GL.recordError(1282)
        }
        HEAP32[(buffers + i * 4) >> 2] = id
      }
    }
    function _glGenBuffers(n, buffers) {
      __glGenObject(n, buffers, 'createBuffer', GL.buffers)
    }
    function _glGenFramebuffers(n, ids) {
      __glGenObject(n, ids, 'createFramebuffer', GL.framebuffers)
    }
    function _glGenRenderbuffers(n, renderbuffers) {
      __glGenObject(n, renderbuffers, 'createRenderbuffer', GL.renderbuffers)
    }
    function _glGenTextures(n, textures) {
      __glGenObject(n, textures, 'createTexture', GL.textures)
    }
    function _glGenerateMipmap(x0) {
      GLctx['generateMipmap'](x0)
    }
    function _glGetAttribLocation(program, name) {
      return GLctx.getAttribLocation(GL.programs[program], UTF8ToString(name))
    }
    function _glGetError() {
      var error = GLctx.getError() || GL.lastError
      GL.lastError = 0
      return error
    }
    function writeI53ToI64(ptr, num) {
      HEAPU32[ptr >> 2] = num
      HEAPU32[(ptr + 4) >> 2] = (num - HEAPU32[ptr >> 2]) / 4294967296
    }
    function emscriptenWebGLGet(name_, p, type) {
      if (!p) {
        GL.recordError(1281)
        return
      }
      var ret = undefined
      switch (name_) {
        case 36346:
          ret = 1
          break
        case 36344:
          if (type != 0 && type != 1) {
            GL.recordError(1280)
          }
          return
        case 34814:
        case 36345:
          ret = 0
          break
        case 34466:
          var formats = GLctx.getParameter(34467)
          ret = formats ? formats.length : 0
          break
        case 33309:
          if (GL.currentContext.version < 2) {
            GL.recordError(1282)
            return
          }
          var exts = GLctx.getSupportedExtensions() || []
          ret = 2 * exts.length
          break
        case 33307:
        case 33308:
          if (GL.currentContext.version < 2) {
            GL.recordError(1280)
            return
          }
          ret = name_ == 33307 ? 3 : 0
          break
      }
      if (ret === undefined) {
        var result = GLctx.getParameter(name_)
        switch (typeof result) {
          case 'number':
            ret = result
            break
          case 'boolean':
            ret = result ? 1 : 0
            break
          case 'string':
            GL.recordError(1280)
            return
          case 'object':
            if (result === null) {
              switch (name_) {
                case 34964:
                case 35725:
                case 34965:
                case 36006:
                case 36007:
                case 32873:
                case 34229:
                case 36662:
                case 36663:
                case 35053:
                case 35055:
                case 36010:
                case 35097:
                case 35869:
                case 32874:
                case 36389:
                case 35983:
                case 35368:
                case 34068: {
                  ret = 0
                  break
                }
                default: {
                  GL.recordError(1280)
                  return
                }
              }
            } else if (
              result instanceof Float32Array ||
              result instanceof Uint32Array ||
              result instanceof Int32Array ||
              result instanceof Array
            ) {
              for (var i = 0; i < result.length; ++i) {
                switch (type) {
                  case 0:
                    HEAP32[(p + i * 4) >> 2] = result[i]
                    break
                  case 2:
                    HEAPF32[(p + i * 4) >> 2] = result[i]
                    break
                  case 4:
                    HEAP8[(p + i) >> 0] = result[i] ? 1 : 0
                    break
                }
              }
              return
            } else {
              try {
                ret = result.name | 0
              } catch (e) {
                GL.recordError(1280)
                err(
                  'GL_INVALID_ENUM in glGet' +
                    type +
                    'v: Unknown object returned from WebGL getParameter(' +
                    name_ +
                    ')! (error: ' +
                    e +
                    ')'
                )
                return
              }
            }
            break
          default:
            GL.recordError(1280)
            err(
              'GL_INVALID_ENUM in glGet' +
                type +
                'v: Native code calling glGet' +
                type +
                'v(' +
                name_ +
                ') and it returns ' +
                result +
                ' of type ' +
                typeof result +
                '!'
            )
            return
        }
      }
      switch (type) {
        case 1:
          writeI53ToI64(p, ret)
          break
        case 0:
          HEAP32[p >> 2] = ret
          break
        case 2:
          HEAPF32[p >> 2] = ret
          break
        case 4:
          HEAP8[p >> 0] = ret ? 1 : 0
          break
      }
    }
    function _glGetIntegerv(name_, p) {
      emscriptenWebGLGet(name_, p, 0)
    }
    function _glGetProgramiv(program, pname, p) {
      if (!p) {
        GL.recordError(1281)
        return
      }
      if (program >= GL.counter) {
        GL.recordError(1281)
        return
      }
      var ptable = GL.programInfos[program]
      if (!ptable) {
        GL.recordError(1282)
        return
      }
      if (pname == 35716) {
        var log = GLctx.getProgramInfoLog(GL.programs[program])
        if (log === null) log = '(unknown error)'
        HEAP32[p >> 2] = log.length + 1
      } else if (pname == 35719) {
        HEAP32[p >> 2] = ptable.maxUniformLength
      } else if (pname == 35722) {
        if (ptable.maxAttributeLength == -1) {
          program = GL.programs[program]
          var numAttribs = GLctx.getProgramParameter(program, 35721)
          ptable.maxAttributeLength = 0
          for (var i = 0; i < numAttribs; ++i) {
            var activeAttrib = GLctx.getActiveAttrib(program, i)
            ptable.maxAttributeLength = Math.max(
              ptable.maxAttributeLength,
              activeAttrib.name.length + 1
            )
          }
        }
        HEAP32[p >> 2] = ptable.maxAttributeLength
      } else if (pname == 35381) {
        if (ptable.maxUniformBlockNameLength == -1) {
          program = GL.programs[program]
          var numBlocks = GLctx.getProgramParameter(program, 35382)
          ptable.maxUniformBlockNameLength = 0
          for (var i = 0; i < numBlocks; ++i) {
            var activeBlockName = GLctx.getActiveUniformBlockName(program, i)
            ptable.maxUniformBlockNameLength = Math.max(
              ptable.maxUniformBlockNameLength,
              activeBlockName.length + 1
            )
          }
        }
        HEAP32[p >> 2] = ptable.maxUniformBlockNameLength
      } else {
        HEAP32[p >> 2] = GLctx.getProgramParameter(GL.programs[program], pname)
      }
    }
    function _glGetShaderInfoLog(shader, maxLength, length, infoLog) {
      var log = GLctx.getShaderInfoLog(GL.shaders[shader])
      if (log === null) log = '(unknown error)'
      var numBytesWrittenExclNull =
        maxLength > 0 && infoLog ? stringToUTF8(log, infoLog, maxLength) : 0
      if (length) HEAP32[length >> 2] = numBytesWrittenExclNull
    }
    function _glGetShaderiv(shader, pname, p) {
      if (!p) {
        GL.recordError(1281)
        return
      }
      if (pname == 35716) {
        var log = GLctx.getShaderInfoLog(GL.shaders[shader])
        if (log === null) log = '(unknown error)'
        var logLength = log ? log.length + 1 : 0
        HEAP32[p >> 2] = logLength
      } else if (pname == 35720) {
        var source = GLctx.getShaderSource(GL.shaders[shader])
        var sourceLength = source ? source.length + 1 : 0
        HEAP32[p >> 2] = sourceLength
      } else {
        HEAP32[p >> 2] = GLctx.getShaderParameter(GL.shaders[shader], pname)
      }
    }
    function stringToNewUTF8(jsString) {
      var length = lengthBytesUTF8(jsString) + 1
      var cString = _malloc(length)
      stringToUTF8(jsString, cString, length)
      return cString
    }
    function _glGetString(name_) {
      if (GL.stringCache[name_]) return GL.stringCache[name_]
      var ret
      switch (name_) {
        case 7939:
          var exts = GLctx.getSupportedExtensions() || []
          exts = exts.concat(
            exts.map(function (e) {
              return 'GL_' + e
            })
          )
          ret = stringToNewUTF8(exts.join(' '))
          break
        case 7936:
        case 7937:
        case 37445:
        case 37446:
          var s = GLctx.getParameter(name_)
          if (!s) {
            GL.recordError(1280)
          }
          ret = stringToNewUTF8(s)
          break
        case 7938:
          var glVersion = GLctx.getParameter(7938)
          if (GL.currentContext.version >= 2) glVersion = 'OpenGL ES 3.0 (' + glVersion + ')'
          else {
            glVersion = 'OpenGL ES 2.0 (' + glVersion + ')'
          }
          ret = stringToNewUTF8(glVersion)
          break
        case 35724:
          var glslVersion = GLctx.getParameter(35724)
          var ver_re = /^WebGL GLSL ES ([0-9]\.[0-9][0-9]?)(?:$| .*)/
          var ver_num = glslVersion.match(ver_re)
          if (ver_num !== null) {
            if (ver_num[1].length == 3) ver_num[1] = ver_num[1] + '0'
            glslVersion = 'OpenGL ES GLSL ES ' + ver_num[1] + ' (' + glslVersion + ')'
          }
          ret = stringToNewUTF8(glslVersion)
          break
        default:
          GL.recordError(1280)
          return 0
      }
      GL.stringCache[name_] = ret
      return ret
    }
    function jstoi_q(str) {
      return parseInt(str)
    }
    function _glGetUniformLocation(program, name) {
      name = UTF8ToString(name)
      var arrayIndex = 0
      if (name[name.length - 1] == ']') {
        var leftBrace = name.lastIndexOf('[')
        arrayIndex = name[leftBrace + 1] != ']' ? jstoi_q(name.slice(leftBrace + 1)) : 0
        name = name.slice(0, leftBrace)
      }
      var uniformInfo = GL.programInfos[program] && GL.programInfos[program].uniforms[name]
      if (uniformInfo && arrayIndex >= 0 && arrayIndex < uniformInfo[0]) {
        return uniformInfo[1] + arrayIndex
      } else {
        return -1
      }
    }
    function _glLineWidth(x0) {
      GLctx['lineWidth'](x0)
    }
    function _glLinkProgram(program) {
      GLctx.linkProgram(GL.programs[program])
      GL.populateUniformTable(program)
    }
    function _glPixelStorei(pname, param) {
      if (pname == 3317) {
        GL.unpackAlignment = param
      }
      GLctx.pixelStorei(pname, param)
    }
    function _glScissor(x0, x1, x2, x3) {
      GLctx['scissor'](x0, x1, x2, x3)
    }
    function _glShaderSource(shader, count, string, length) {
      var source = GL.getSource(shader, count, string, length)
      GLctx.shaderSource(GL.shaders[shader], source)
    }
    function computeUnpackAlignedImageSize(width, height, sizePerPixel, alignment) {
      function roundedToNextMultipleOf(x, y) {
        return (x + y - 1) & -y
      }
      var plainRowSize = width * sizePerPixel
      var alignedRowSize = roundedToNextMultipleOf(plainRowSize, alignment)
      return height * alignedRowSize
    }
    function __colorChannelsInGlTextureFormat(format) {
      var colorChannels = {
        5: 3,
        6: 4,
        8: 2,
        29502: 3,
        29504: 4,
        26917: 2,
        26918: 2,
        29846: 3,
        29847: 4
      }
      return colorChannels[format - 6402] || 1
    }
    function heapObjectForWebGLType(type) {
      type -= 5120
      if (type == 0) return HEAP8
      if (type == 1) return HEAPU8
      if (type == 2) return HEAP16
      if (type == 4) return HEAP32
      if (type == 6) return HEAPF32
      if (type == 5 || type == 28922 || type == 28520 || type == 30779 || type == 30782)
        return HEAPU32
      return HEAPU16
    }
    function heapAccessShiftForWebGLHeap(heap) {
      return 31 - Math.clz32(heap.BYTES_PER_ELEMENT)
    }
    function emscriptenWebGLGetTexPixelData(type, format, width, height, pixels, internalFormat) {
      var heap = heapObjectForWebGLType(type)
      var shift = heapAccessShiftForWebGLHeap(heap)
      var byteSize = 1 << shift
      var sizePerPixel = __colorChannelsInGlTextureFormat(format) * byteSize
      var bytes = computeUnpackAlignedImageSize(width, height, sizePerPixel, GL.unpackAlignment)
      return heap.subarray(pixels >> shift, (pixels + bytes) >> shift)
    }
    function _glTexImage2D(
      target,
      level,
      internalFormat,
      width,
      height,
      border,
      format,
      type,
      pixels
    ) {
      if (GL.currentContext.version >= 2) {
        if (GLctx.currentPixelUnpackBufferBinding) {
          GLctx.texImage2D(
            target,
            level,
            internalFormat,
            width,
            height,
            border,
            format,
            type,
            pixels
          )
        } else if (pixels) {
          var heap = heapObjectForWebGLType(type)
          GLctx.texImage2D(
            target,
            level,
            internalFormat,
            width,
            height,
            border,
            format,
            type,
            heap,
            pixels >> heapAccessShiftForWebGLHeap(heap)
          )
        } else {
          GLctx.texImage2D(target, level, internalFormat, width, height, border, format, type, null)
        }
        return
      }
      GLctx.texImage2D(
        target,
        level,
        internalFormat,
        width,
        height,
        border,
        format,
        type,
        pixels
          ? emscriptenWebGLGetTexPixelData(type, format, width, height, pixels, internalFormat)
          : null
      )
    }
    function _glTexParameterf(x0, x1, x2) {
      GLctx['texParameterf'](x0, x1, x2)
    }
    function _glTexParameteri(x0, x1, x2) {
      GLctx['texParameteri'](x0, x1, x2)
    }
    function _glUniform1f(location, v0) {
      GLctx.uniform1f(GL.uniforms[location], v0)
    }
    function _glUniform1i(location, v0) {
      GLctx.uniform1i(GL.uniforms[location], v0)
    }
    var miniTempWebGLFloatBuffers = []
    function _glUniformMatrix4fv(location, count, transpose, value) {
      if (GL.currentContext.version >= 2) {
        GLctx.uniformMatrix4fv(GL.uniforms[location], !!transpose, HEAPF32, value >> 2, count * 16)
        return
      }
      if (count <= 18) {
        var view = miniTempWebGLFloatBuffers[16 * count - 1]
        var heap = HEAPF32
        value >>= 2
        for (var i = 0; i < 16 * count; i += 16) {
          var dst = value + i
          view[i] = heap[dst]
          view[i + 1] = heap[dst + 1]
          view[i + 2] = heap[dst + 2]
          view[i + 3] = heap[dst + 3]
          view[i + 4] = heap[dst + 4]
          view[i + 5] = heap[dst + 5]
          view[i + 6] = heap[dst + 6]
          view[i + 7] = heap[dst + 7]
          view[i + 8] = heap[dst + 8]
          view[i + 9] = heap[dst + 9]
          view[i + 10] = heap[dst + 10]
          view[i + 11] = heap[dst + 11]
          view[i + 12] = heap[dst + 12]
          view[i + 13] = heap[dst + 13]
          view[i + 14] = heap[dst + 14]
          view[i + 15] = heap[dst + 15]
        }
      } else {
        var view = HEAPF32.subarray(value >> 2, (value + count * 64) >> 2)
      }
      GLctx.uniformMatrix4fv(GL.uniforms[location], !!transpose, view)
    }
    function _glUseProgram(program) {
      GLctx.useProgram(GL.programs[program])
    }
    function _glVertexAttribPointer(index, size, type, normalized, stride, ptr) {
      GLctx.vertexAttribPointer(index, size, type, !!normalized, stride, ptr)
    }
    function _glViewport(x0, x1, x2, x3) {
      GLctx['viewport'](x0, x1, x2, x3)
    }
    function _llvm_eh_typeid_for(type) {
      return type
    }
    function _tzset() {
      if (_tzset.called) return
      _tzset.called = true
      var currentYear = new Date().getFullYear()
      var winter = new Date(currentYear, 0, 1)
      var summer = new Date(currentYear, 6, 1)
      var winterOffset = winter.getTimezoneOffset()
      var summerOffset = summer.getTimezoneOffset()
      var stdTimezoneOffset = Math.max(winterOffset, summerOffset)
      HEAP32[__get_timezone() >> 2] = stdTimezoneOffset * 60
      HEAP32[__get_daylight() >> 2] = Number(winterOffset != summerOffset)
      function extractZone(date) {
        var match = date.toTimeString().match(/\(([A-Za-z ]+)\)$/)
        return match ? match[1] : 'GMT'
      }
      var winterName = extractZone(winter)
      var summerName = extractZone(summer)
      var winterNamePtr = allocateUTF8(winterName)
      var summerNamePtr = allocateUTF8(summerName)
      if (summerOffset < winterOffset) {
        HEAP32[__get_tzname() >> 2] = winterNamePtr
        HEAP32[(__get_tzname() + 4) >> 2] = summerNamePtr
      } else {
        HEAP32[__get_tzname() >> 2] = summerNamePtr
        HEAP32[(__get_tzname() + 4) >> 2] = winterNamePtr
      }
    }
    function _localtime_r(time, tmPtr) {
      _tzset()
      var date = new Date(HEAP32[time >> 2] * 1e3)
      HEAP32[tmPtr >> 2] = date.getSeconds()
      HEAP32[(tmPtr + 4) >> 2] = date.getMinutes()
      HEAP32[(tmPtr + 8) >> 2] = date.getHours()
      HEAP32[(tmPtr + 12) >> 2] = date.getDate()
      HEAP32[(tmPtr + 16) >> 2] = date.getMonth()
      HEAP32[(tmPtr + 20) >> 2] = date.getFullYear() - 1900
      HEAP32[(tmPtr + 24) >> 2] = date.getDay()
      var start = new Date(date.getFullYear(), 0, 1)
      var yday = ((date.getTime() - start.getTime()) / (1e3 * 60 * 60 * 24)) | 0
      HEAP32[(tmPtr + 28) >> 2] = yday
      HEAP32[(tmPtr + 36) >> 2] = -(date.getTimezoneOffset() * 60)
      var summerOffset = new Date(date.getFullYear(), 6, 1).getTimezoneOffset()
      var winterOffset = start.getTimezoneOffset()
      var dst =
        (summerOffset != winterOffset &&
          date.getTimezoneOffset() == Math.min(winterOffset, summerOffset)) | 0
      HEAP32[(tmPtr + 32) >> 2] = dst
      var zonePtr = HEAP32[(__get_tzname() + (dst ? 4 : 0)) >> 2]
      HEAP32[(tmPtr + 40) >> 2] = zonePtr
      return tmPtr
    }
    var GLctx
    var miniTempWebGLFloatBuffersStorage = new Float32Array(288)
    for (var i = 0; i < 288; ++i) {
      miniTempWebGLFloatBuffers[i] = miniTempWebGLFloatBuffersStorage.subarray(0, i + 1)
    }
    var asmLibraryArg = {
      fa: _JS_glTexImage2D,
      C: ___assert_fail,
      o: ___cxa_allocate_exception,
      n: ___cxa_begin_catch,
      x: ___cxa_end_catch,
      b: ___cxa_find_matching_catch_2,
      f: ___cxa_find_matching_catch_3,
      p: ___cxa_find_matching_catch_4,
      q: ___cxa_free_exception,
      Ua: ___cxa_rethrow,
      z: ___cxa_throw,
      d: ___resumeException,
      Va: _abort,
      Xa: _emscripten_memcpy_big,
      Ia: _emscripten_resize_heap,
      Pa: _emscripten_webgl_create_context,
      Oa: _emscripten_webgl_destroy_context,
      Na: _emscripten_webgl_get_drawing_buffer_size,
      Qa: _emscripten_webgl_init_context_attributes,
      $: _emscripten_webgl_make_context_current,
      Ta: _fd_close,
      aa: _fd_write,
      a: _getTempRet0,
      Sa: _gettimeofday,
      y: _glActiveTexture,
      Aa: _glAttachShader,
      N: _glBindBuffer,
      D: _glBindFramebuffer,
      s: _glBindTexture,
      pa: _glBlendFunc,
      oa: _glBlitFramebuffer,
      Y: _glBufferData,
      Q: _glCheckFramebufferStatus,
      T: _glClear,
      U: _glClearColor,
      Da: _glCompileShader,
      Ga: _glCreateProgram,
      Fa: _glCreateShader,
      O: _glDeleteBuffers,
      ia: _glDeleteFramebuffers,
      ra: _glDeleteProgram,
      ja: _glDeleteRenderbuffers,
      sa: _glDeleteShader,
      W: _glDeleteTextures,
      ma: _glDisable,
      Ha: _glDisableVertexAttribArray,
      Ja: _glDrawArrays,
      Ka: _glDrawElements,
      V: _glEnable,
      va: _glEnableVertexAttribArray,
      R: _glFramebufferTexture2D,
      Z: _glGenBuffers,
      ka: _glGenFramebuffers,
      la: _glGenRenderbuffers,
      ha: _glGenTextures,
      ea: _glGenerateMipmap,
      _: _glGetAttribLocation,
      t: _glGetError,
      ga: _glGetIntegerv,
      ya: _glGetProgramiv,
      Ba: _glGetShaderInfoLog,
      Ca: _glGetShaderiv,
      qa: _glGetString,
      X: _glGetUniformLocation,
      Ma: _glLineWidth,
      za: _glLinkProgram,
      u: _glPixelStorei,
      na: _glScissor,
      Ea: _glShaderSource,
      v: _glTexImage2D,
      B: _glTexParameterf,
      H: _glTexParameteri,
      wa: _glUniform1f,
      ta: _glUniform1i,
      xa: _glUniformMatrix4fv,
      M: _glUseProgram,
      ua: _glVertexAttribPointer,
      S: _glViewport,
      A: invoke_i,
      ca: invoke_iffiiii,
      g: invoke_ii,
      Wa: invoke_iif,
      P: invoke_iifff,
      ba: invoke_iiffii,
      c: invoke_iii,
      h: invoke_iiii,
      J: invoke_iiiiff,
      l: invoke_iiiii,
      e: invoke_iiiiii,
      I: invoke_iiiiiii,
      G: invoke_iiiiiiii,
      k: invoke_v,
      La: invoke_vf,
      r: invoke_vi,
      F: invoke_vif,
      da: invoke_viffff,
      i: invoke_vii,
      K: invoke_viifi,
      j: invoke_viii,
      m: invoke_viiii,
      E: invoke_viiiii,
      L: invoke_viiiiii,
      w: _llvm_eh_typeid_for,
      Ra: _localtime_r
    }
    var asm = createWasm()
    var ___wasm_call_ctors = (Module['___wasm_call_ctors'] = function () {
      return (___wasm_call_ctors = Module['___wasm_call_ctors'] = Module['asm']['Za']).apply(
        null,
        arguments
      )
    })
    var _RENDER_Create = (Module['_RENDER_Create'] = function () {
      return (_RENDER_Create = Module['_RENDER_Create'] = Module['asm']['_a']).apply(
        null,
        arguments
      )
    })
    var _RENDER_DrawImage = (Module['_RENDER_DrawImage'] = function () {
      return (_RENDER_DrawImage = Module['_RENDER_DrawImage'] = Module['asm']['$a']).apply(
        null,
        arguments
      )
    })
    var _RENDER_Present = (Module['_RENDER_Present'] = function () {
      return (_RENDER_Present = Module['_RENDER_Present'] = Module['asm']['ab']).apply(
        null,
        arguments
      )
    })
    var _RENDER_ClearScreen = (Module['_RENDER_ClearScreen'] = function () {
      return (_RENDER_ClearScreen = Module['_RENDER_ClearScreen'] = Module['asm']['bb']).apply(
        null,
        arguments
      )
    })
    var _RENDER_Destroy = (Module['_RENDER_Destroy'] = function () {
      return (_RENDER_Destroy = Module['_RENDER_Destroy'] = Module['asm']['cb']).apply(
        null,
        arguments
      )
    })
    var _RENDER_ColorAdjust = (Module['_RENDER_ColorAdjust'] = function () {
      return (_RENDER_ColorAdjust = Module['_RENDER_ColorAdjust'] = Module['asm']['db']).apply(
        null,
        arguments
      )
    })
    var _RENDER_AlgoCommand = (Module['_RENDER_AlgoCommand'] = function () {
      return (_RENDER_AlgoCommand = Module['_RENDER_AlgoCommand'] = Module['asm']['eb']).apply(
        null,
        arguments
      )
    })
    var _RENDER_SetAlgoParams = (Module['_RENDER_SetAlgoParams'] = function () {
      return (_RENDER_SetAlgoParams = Module['_RENDER_SetAlgoParams'] = Module['asm']['fb']).apply(
        null,
        arguments
      )
    })
    var _RENDER_GetPanoARCoord2DTrans = (Module['_RENDER_GetPanoARCoord2DTrans'] = function () {
      return (_RENDER_GetPanoARCoord2DTrans = Module['_RENDER_GetPanoARCoord2DTrans'] =
        Module['asm']['gb']).apply(null, arguments)
    })
    var _RENDER_GetPanoARCoord3DTrans = (Module['_RENDER_GetPanoARCoord3DTrans'] = function () {
      return (_RENDER_GetPanoARCoord3DTrans = Module['_RENDER_GetPanoARCoord3DTrans'] =
        Module['asm']['hb']).apply(null, arguments)
    })
    var _RENDER_3DSetViewPoint = (Module['_RENDER_3DSetViewPoint'] = function () {
      return (_RENDER_3DSetViewPoint = Module['_RENDER_3DSetViewPoint'] =
        Module['asm']['ib']).apply(null, arguments)
    })
    var _RENDER_3DSetViewMode = (Module['_RENDER_3DSetViewMode'] = function () {
      return (_RENDER_3DSetViewMode = Module['_RENDER_3DSetViewMode'] = Module['asm']['jb']).apply(
        null,
        arguments
      )
    })
    var _RENDER_3DGetViewPoint = (Module['_RENDER_3DGetViewPoint'] = function () {
      return (_RENDER_3DGetViewPoint = Module['_RENDER_3DGetViewPoint'] =
        Module['asm']['kb']).apply(null, arguments)
    })
    var _RENDER_3DSetViewMoveDistance = (Module['_RENDER_3DSetViewMoveDistance'] = function () {
      return (_RENDER_3DSetViewMoveDistance = Module['_RENDER_3DSetViewMoveDistance'] =
        Module['asm']['lb']).apply(null, arguments)
    })
    var _RENDER_3DGetViewMoveDistance = (Module['_RENDER_3DGetViewMoveDistance'] = function () {
      return (_RENDER_3DGetViewMoveDistance = Module['_RENDER_3DGetViewMoveDistance'] =
        Module['asm']['mb']).apply(null, arguments)
    })
    var _RENDER_3DSetPerspectiveFovy = (Module['_RENDER_3DSetPerspectiveFovy'] = function () {
      return (_RENDER_3DSetPerspectiveFovy = Module['_RENDER_3DSetPerspectiveFovy'] =
        Module['asm']['nb']).apply(null, arguments)
    })
    var _RENDER_3DGetPerspectiveFovy = (Module['_RENDER_3DGetPerspectiveFovy'] = function () {
      return (_RENDER_3DGetPerspectiveFovy = Module['_RENDER_3DGetPerspectiveFovy'] =
        Module['asm']['ob']).apply(null, arguments)
    })
    var _RENDER_3DSetModelRotate = (Module['_RENDER_3DSetModelRotate'] = function () {
      return (_RENDER_3DSetModelRotate = Module['_RENDER_3DSetModelRotate'] =
        Module['asm']['pb']).apply(null, arguments)
    })
    var _RENDER_3DGetModelRotate = (Module['_RENDER_3DGetModelRotate'] = function () {
      return (_RENDER_3DGetModelRotate = Module['_RENDER_3DGetModelRotate'] =
        Module['asm']['qb']).apply(null, arguments)
    })
    var _RENDER_3DSetModelScale = (Module['_RENDER_3DSetModelScale'] = function () {
      return (_RENDER_3DSetModelScale = Module['_RENDER_3DSetModelScale'] =
        Module['asm']['rb']).apply(null, arguments)
    })
    var _RENDER_3DGetModelScale = (Module['_RENDER_3DGetModelScale'] = function () {
      return (_RENDER_3DGetModelScale = Module['_RENDER_3DGetModelScale'] =
        Module['asm']['sb']).apply(null, arguments)
    })
    var _RENDER_3DSetModelTranslate = (Module['_RENDER_3DSetModelTranslate'] = function () {
      return (_RENDER_3DSetModelTranslate = Module['_RENDER_3DSetModelTranslate'] =
        Module['asm']['tb']).apply(null, arguments)
    })
    var _RENDER_3DGetModelTranslate = (Module['_RENDER_3DGetModelTranslate'] = function () {
      return (_RENDER_3DGetModelTranslate = Module['_RENDER_3DGetModelTranslate'] =
        Module['asm']['ub']).apply(null, arguments)
    })
    var _RENDER_3DResetModel = (Module['_RENDER_3DResetModel'] = function () {
      return (_RENDER_3DResetModel = Module['_RENDER_3DResetModel'] = Module['asm']['vb']).apply(
        null,
        arguments
      )
    })
    var _RENDER_GetVersion = (Module['_RENDER_GetVersion'] = function () {
      return (_RENDER_GetVersion = Module['_RENDER_GetVersion'] = Module['asm']['wb']).apply(
        null,
        arguments
      )
    })
    var _RENDER_SetPrintLogLevel = (Module['_RENDER_SetPrintLogLevel'] = function () {
      return (_RENDER_SetPrintLogLevel = Module['_RENDER_SetPrintLogLevel'] =
        Module['asm']['xb']).apply(null, arguments)
    })
    var __get_tzname = (Module['__get_tzname'] = function () {
      return (__get_tzname = Module['__get_tzname'] = Module['asm']['yb']).apply(null, arguments)
    })
    var __get_daylight = (Module['__get_daylight'] = function () {
      return (__get_daylight = Module['__get_daylight'] = Module['asm']['zb']).apply(
        null,
        arguments
      )
    })
    var __get_timezone = (Module['__get_timezone'] = function () {
      return (__get_timezone = Module['__get_timezone'] = Module['asm']['Ab']).apply(
        null,
        arguments
      )
    })
    var stackSave = (Module['stackSave'] = function () {
      return (stackSave = Module['stackSave'] = Module['asm']['Bb']).apply(null, arguments)
    })
    var stackRestore = (Module['stackRestore'] = function () {
      return (stackRestore = Module['stackRestore'] = Module['asm']['Cb']).apply(null, arguments)
    })
    var stackAlloc = (Module['stackAlloc'] = function () {
      return (stackAlloc = Module['stackAlloc'] = Module['asm']['Db']).apply(null, arguments)
    })
    var _setThrew = (Module['_setThrew'] = function () {
      return (_setThrew = Module['_setThrew'] = Module['asm']['Eb']).apply(null, arguments)
    })
    var ___cxa_can_catch = (Module['___cxa_can_catch'] = function () {
      return (___cxa_can_catch = Module['___cxa_can_catch'] = Module['asm']['Fb']).apply(
        null,
        arguments
      )
    })
    var ___cxa_is_pointer_type = (Module['___cxa_is_pointer_type'] = function () {
      return (___cxa_is_pointer_type = Module['___cxa_is_pointer_type'] =
        Module['asm']['Gb']).apply(null, arguments)
    })
    var _malloc = (Module['_malloc'] = function () {
      return (_malloc = Module['_malloc'] = Module['asm']['Hb']).apply(null, arguments)
    })
    var _free = (Module['_free'] = function () {
      return (_free = Module['_free'] = Module['asm']['Ib']).apply(null, arguments)
    })
    function invoke_iii(index, a1, a2) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_i(index) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)()
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_v(index) {
      var sp = stackSave()
      try {
        wasmTable.get(index)()
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_vii(index, a1, a2) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_ii(index, a1) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiiii(index, a1, a2, a3, a4, a5) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiii(index, a1, a2, a3) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_viiii(index, a1, a2, a3, a4) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2, a3, a4)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiii(index, a1, a2, a3, a4) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_vi(index, a1) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_viii(index, a1, a2, a3) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2, a3)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_vf(index, a1) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiiiiii(index, a1, a2, a3, a4, a5, a6, a7) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_vif(index, a1, a2) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_viiiii(index, a1, a2, a3, a4, a5) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2, a3, a4, a5)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_viiiiii(index, a1, a2, a3, a4, a5, a6) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2, a3, a4, a5, a6)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiiiii(index, a1, a2, a3, a4, a5, a6) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5, a6)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_viifi(index, a1, a2, a3, a4) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2, a3, a4)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_viffff(index, a1, a2, a3, a4, a5) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2, a3, a4, a5)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iffiiii(index, a1, a2, a3, a4, a5, a6) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5, a6)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiffii(index, a1, a2, a3, a4, a5) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiiff(index, a1, a2, a3, a4, a5) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iif(index, a1, a2) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iifff(index, a1, a2, a3, a4) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    Module['writeArrayToMemory'] = writeArrayToMemory
    Module['allocateUTF8'] = allocateUTF8
    var calledRun
    function ExitStatus(status) {
      this.name = 'ExitStatus'
      this.message = 'Program terminated with exit(' + status + ')'
      this.status = status
    }
    dependenciesFulfilled = function runCaller() {
      if (!calledRun) run()
      if (!calledRun) dependenciesFulfilled = runCaller
    }
    function run(args) {
      args = args || arguments_
      if (runDependencies > 0) {
        return
      }
      preRun()
      if (runDependencies > 0) {
        return
      }
      function doRun() {
        if (calledRun) return
        calledRun = true
        Module['calledRun'] = true
        if (ABORT) return
        initRuntime()
        preMain()
        readyPromiseResolve(Module)
        if (Module['onRuntimeInitialized']) Module['onRuntimeInitialized']()
        postRun()
      }
      if (Module['setStatus']) {
        Module['setStatus']('Running...')
        setTimeout(function () {
          setTimeout(function () {
            Module['setStatus']('')
          }, 1)
          doRun()
        }, 1)
      } else {
        doRun()
      }
    }
    Module['run'] = run
    if (Module['preInit']) {
      if (typeof Module['preInit'] == 'function') Module['preInit'] = [Module['preInit']]
      while (Module['preInit'].length > 0) {
        Module['preInit'].pop()()
      }
    }
    run()

    return RenderEngine_Module.ready
  }
})()
if (typeof exports === 'object' && typeof module === 'object') module.exports = RenderEngine_Module
else if (typeof define === 'function' && define['amd'])
  define([], function () {
    return RenderEngine_Module
  })
else if (typeof exports === 'object') exports['RenderEngine_Module'] = RenderEngine_Module
