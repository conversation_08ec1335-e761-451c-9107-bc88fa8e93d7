var IVSDrawer_Module = (function () {
  var _scriptDir =
    typeof document !== 'undefined' && document.currentScript
      ? document.currentScript.src
      : undefined
  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename
  return function (IVSDrawer_Module) {
    IVSDrawer_Module = IVSDrawer_Module || {}

    var Module = typeof IVSDrawer_Module !== 'undefined' ? IVSDrawer_Module : {}
    var readyPromiseResolve, readyPromiseReject
    Module['ready'] = new Promise(function (resolve, reject) {
      readyPromiseResolve = resolve
      readyPromiseReject = reject
    })
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_Open')) {
      Object.defineProperty(Module['ready'], '_DRAW_Open', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_Open on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_Open', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_Open on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_Clean')) {
      Object.defineProperty(Module['ready'], '_DRAW_Clean', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_Clean on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_Clean', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_Clean on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_Close')) {
      Object.defineProperty(Module['ready'], '_DRAW_Close', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_Close on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_Close', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_Close on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_SetPen')) {
      Object.defineProperty(Module['ready'], '_DRAW_SetPen', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_SetPen on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_SetPen', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_SetPen on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_SetDrawSurfaceParam')) {
      Object.defineProperty(Module['ready'], '_DRAW_SetDrawSurfaceParam', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_SetDrawSurfaceParam on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_SetDrawSurfaceParam', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_SetDrawSurfaceParam on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_DrawByRenderHandle')) {
      Object.defineProperty(Module['ready'], '_DRAW_DrawByRenderHandle', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_DrawByRenderHandle on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_DrawByRenderHandle', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_DrawByRenderHandle on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_InputJsonData')) {
      Object.defineProperty(Module['ready'], '_DRAW_InputJsonData', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_InputJsonData on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_InputJsonData', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_InputJsonData on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_InputIVSData')) {
      Object.defineProperty(Module['ready'], '_DRAW_InputIVSData', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_InputIVSData on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_InputIVSData', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_InputIVSData on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_InputRuleData')) {
      Object.defineProperty(Module['ready'], '_DRAW_InputRuleData', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_InputRuleData on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_InputRuleData', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_InputRuleData on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_InputRuleDataEx')) {
      Object.defineProperty(Module['ready'], '_DRAW_InputRuleDataEx', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_InputRuleDataEx on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_InputRuleDataEx', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_InputRuleDataEx on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_InputTrackData')) {
      Object.defineProperty(Module['ready'], '_DRAW_InputTrackData', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_InputTrackData on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_InputTrackData', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_InputTrackData on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_InputTrackDataEx2')) {
      Object.defineProperty(Module['ready'], '_DRAW_InputTrackDataEx2', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_InputTrackDataEx2 on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_InputTrackDataEx2', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_InputTrackDataEx2 on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_SetLifeCount')) {
      Object.defineProperty(Module['ready'], '_DRAW_SetLifeCount', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_SetLifeCount on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_SetLifeCount', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_SetLifeCount on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_SetWebCanvas')) {
      Object.defineProperty(Module['ready'], '_DRAW_SetWebCanvas', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_SetWebCanvas on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_SetWebCanvas', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_SetWebCanvas on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_SetEnable')) {
      Object.defineProperty(Module['ready'], '_DRAW_SetEnable', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_SetEnable on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_SetEnable', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_SetEnable on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_CleanScreen')) {
      Object.defineProperty(Module['ready'], '_DRAW_CleanScreen', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_CleanScreen on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_CleanScreen', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_CleanScreen on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_SetPrintLogLevel')) {
      Object.defineProperty(Module['ready'], '_DRAW_SetPrintLogLevel', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_SetPrintLogLevel on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_SetPrintLogLevel', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_SetPrintLogLevel on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_SetLanguageEnvi')) {
      Object.defineProperty(Module['ready'], '_DRAW_SetLanguageEnvi', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_SetLanguageEnvi on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_SetLanguageEnvi', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_SetLanguageEnvi on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_DRAW_SetTranslateString')) {
      Object.defineProperty(Module['ready'], '_DRAW_SetTranslateString', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _DRAW_SetTranslateString on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_DRAW_SetTranslateString', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _DRAW_SetTranslateString on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_emscripten_stack_get_end')) {
      Object.defineProperty(Module['ready'], '_emscripten_stack_get_end', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _emscripten_stack_get_end on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_emscripten_stack_get_end', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _emscripten_stack_get_end on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_emscripten_stack_get_free')) {
      Object.defineProperty(Module['ready'], '_emscripten_stack_get_free', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _emscripten_stack_get_free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_emscripten_stack_get_free', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _emscripten_stack_get_free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_emscripten_stack_init')) {
      Object.defineProperty(Module['ready'], '_emscripten_stack_init', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _emscripten_stack_init on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_emscripten_stack_init', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _emscripten_stack_init on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_stackSave')) {
      Object.defineProperty(Module['ready'], '_stackSave', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _stackSave on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_stackSave', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _stackSave on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_stackRestore')) {
      Object.defineProperty(Module['ready'], '_stackRestore', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _stackRestore on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_stackRestore', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _stackRestore on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_stackAlloc')) {
      Object.defineProperty(Module['ready'], '_stackAlloc', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _stackAlloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_stackAlloc', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _stackAlloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '___wasm_call_ctors')) {
      Object.defineProperty(Module['ready'], '___wasm_call_ctors', {
        configurable: true,
        get: function () {
          abort(
            'You are getting ___wasm_call_ctors on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '___wasm_call_ctors', {
        configurable: true,
        set: function () {
          abort(
            'You are setting ___wasm_call_ctors on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_fflush')) {
      Object.defineProperty(Module['ready'], '_fflush', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _fflush on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_fflush', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _fflush on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '___errno_location')) {
      Object.defineProperty(Module['ready'], '___errno_location', {
        configurable: true,
        get: function () {
          abort(
            'You are getting ___errno_location on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '___errno_location', {
        configurable: true,
        set: function () {
          abort(
            'You are setting ___errno_location on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_malloc')) {
      Object.defineProperty(Module['ready'], '_malloc', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _malloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_malloc', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _malloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_free')) {
      Object.defineProperty(Module['ready'], '_free', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_free', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '___cxa_is_pointer_type')) {
      Object.defineProperty(Module['ready'], '___cxa_is_pointer_type', {
        configurable: true,
        get: function () {
          abort(
            'You are getting ___cxa_is_pointer_type on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '___cxa_is_pointer_type', {
        configurable: true,
        set: function () {
          abort(
            'You are setting ___cxa_is_pointer_type on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '___cxa_can_catch')) {
      Object.defineProperty(Module['ready'], '___cxa_can_catch', {
        configurable: true,
        get: function () {
          abort(
            'You are getting ___cxa_can_catch on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '___cxa_can_catch', {
        configurable: true,
        set: function () {
          abort(
            'You are setting ___cxa_can_catch on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '_setThrew')) {
      Object.defineProperty(Module['ready'], '_setThrew', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _setThrew on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_setThrew', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _setThrew on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '__get_tzname')) {
      Object.defineProperty(Module['ready'], '__get_tzname', {
        configurable: true,
        get: function () {
          abort(
            'You are getting __get_tzname on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '__get_tzname', {
        configurable: true,
        set: function () {
          abort(
            'You are setting __get_tzname on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '__get_daylight')) {
      Object.defineProperty(Module['ready'], '__get_daylight', {
        configurable: true,
        get: function () {
          abort(
            'You are getting __get_daylight on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '__get_daylight', {
        configurable: true,
        set: function () {
          abort(
            'You are setting __get_daylight on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], '__get_timezone')) {
      Object.defineProperty(Module['ready'], '__get_timezone', {
        configurable: true,
        get: function () {
          abort(
            'You are getting __get_timezone on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '__get_timezone', {
        configurable: true,
        set: function () {
          abort(
            'You are setting __get_timezone on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (
      !Object.getOwnPropertyDescriptor(
        Module['ready'],
        '_emscripten_main_thread_process_queued_calls'
      )
    ) {
      Object.defineProperty(Module['ready'], '_emscripten_main_thread_process_queued_calls', {
        configurable: true,
        get: function () {
          abort(
            'You are getting _emscripten_main_thread_process_queued_calls on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], '_emscripten_main_thread_process_queued_calls', {
        configurable: true,
        set: function () {
          abort(
            'You are setting _emscripten_main_thread_process_queued_calls on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module['ready'], 'onRuntimeInitialized')) {
      Object.defineProperty(Module['ready'], 'onRuntimeInitialized', {
        configurable: true,
        get: function () {
          abort(
            'You are getting onRuntimeInitialized on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
      Object.defineProperty(Module['ready'], 'onRuntimeInitialized', {
        configurable: true,
        set: function () {
          abort(
            'You are setting onRuntimeInitialized on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js'
          )
        }
      })
    }
    var moduleOverrides = {}
    var key
    for (key in Module) {
      if (Module.hasOwnProperty(key)) {
        moduleOverrides[key] = Module[key]
      }
    }
    var arguments_ = []
    var thisProgram = './this.program'
    var quit_ = function (status, toThrow) {
      throw toThrow
    }
    var ENVIRONMENT_IS_WEB = false
    var ENVIRONMENT_IS_WORKER = false
    var ENVIRONMENT_IS_NODE = false
    var ENVIRONMENT_IS_SHELL = false
    ENVIRONMENT_IS_WEB = typeof window === 'object'
    ENVIRONMENT_IS_WORKER = typeof importScripts === 'function'
    ENVIRONMENT_IS_NODE =
      typeof process === 'object' &&
      typeof process.versions === 'object' &&
      typeof process.versions.node === 'string'
    ENVIRONMENT_IS_SHELL = !ENVIRONMENT_IS_WEB && !ENVIRONMENT_IS_NODE && !ENVIRONMENT_IS_WORKER
    if (Module['ENVIRONMENT']) {
      throw new Error(
        'Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -s ENVIRONMENT=web or -s ENVIRONMENT=node)'
      )
    }
    var scriptDirectory = ''
    function locateFile(path) {
      if (Module['locateFile']) {
        return Module['locateFile'](path, scriptDirectory)
      }
      return scriptDirectory + path
    }
    var read_, readAsync, readBinary, setWindowTitle
    var nodeFS
    var nodePath
    if (ENVIRONMENT_IS_NODE) {
      if (ENVIRONMENT_IS_WORKER) {
        scriptDirectory = require('path').dirname(scriptDirectory) + '/'
      } else {
        scriptDirectory = __dirname + '/'
      }
      read_ = function shell_read(filename, binary) {
        if (!nodeFS) nodeFS = require('fs')
        if (!nodePath) nodePath = require('path')
        filename = nodePath['normalize'](filename)
        return nodeFS['readFileSync'](filename, binary ? null : 'utf8')
      }
      readBinary = function readBinary(filename) {
        var ret = read_(filename, true)
        if (!ret.buffer) {
          ret = new Uint8Array(ret)
        }
        assert(ret.buffer)
        return ret
      }
      if (process['argv'].length > 1) {
        thisProgram = process['argv'][1].replace(/\\/g, '/')
      }
      arguments_ = process['argv'].slice(2)
      process['on']('uncaughtException', function (ex) {
        if (!(ex instanceof ExitStatus)) {
          throw ex
        }
      })
      process['on']('unhandledRejection', abort)
      quit_ = function (status) {
        process['exit'](status)
      }
      Module['inspect'] = function () {
        return '[Emscripten Module object]'
      }
    } else if (ENVIRONMENT_IS_SHELL) {
      if (typeof read != 'undefined') {
        read_ = function shell_read(f) {
          return read(f)
        }
      }
      readBinary = function readBinary(f) {
        var data
        if (typeof readbuffer === 'function') {
          return new Uint8Array(readbuffer(f))
        }
        data = read(f, 'binary')
        assert(typeof data === 'object')
        return data
      }
      if (typeof scriptArgs != 'undefined') {
        arguments_ = scriptArgs
      } else if (typeof arguments != 'undefined') {
        arguments_ = arguments
      }
      if (typeof quit === 'function') {
        quit_ = function (status) {
          quit(status)
        }
      }
      if (typeof print !== 'undefined') {
        if (typeof console === 'undefined') console = {}
        console.log = print
        console.warn = console.error = typeof printErr !== 'undefined' ? printErr : print
      }
    } else if (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER) {
      if (ENVIRONMENT_IS_WORKER) {
        scriptDirectory = self.location.href
      } else if (typeof document !== 'undefined' && document.currentScript) {
        scriptDirectory = document.currentScript.src
      }
      if (_scriptDir) {
        scriptDirectory = _scriptDir
      }
      if (scriptDirectory.indexOf('blob:') !== 0) {
        scriptDirectory = scriptDirectory.substr(0, scriptDirectory.lastIndexOf('/') + 1)
      } else {
        scriptDirectory = ''
      }
      {
        read_ = function (url) {
          var xhr = new XMLHttpRequest()
          xhr.open('GET', url, false)
          xhr.send(null)
          return xhr.responseText
        }
        if (ENVIRONMENT_IS_WORKER) {
          readBinary = function (url) {
            var xhr = new XMLHttpRequest()
            xhr.open('GET', url, false)
            xhr.responseType = 'arraybuffer'
            xhr.send(null)
            return new Uint8Array(xhr.response)
          }
        }
        readAsync = function (url, onload, onerror) {
          var xhr = new XMLHttpRequest()
          xhr.open('GET', url, true)
          xhr.responseType = 'arraybuffer'
          xhr.onload = function () {
            if (xhr.status == 200 || (xhr.status == 0 && xhr.response)) {
              onload(xhr.response)
              return
            }
            onerror()
          }
          xhr.onerror = onerror
          xhr.send(null)
        }
      }
      setWindowTitle = function (title) {
        document.title = title
      }
    } else {
      throw new Error('environment detection error')
    }
    var out = Module['print'] || console.log.bind(console)
    var err = Module['printErr'] || console.warn.bind(console)
    for (key in moduleOverrides) {
      if (moduleOverrides.hasOwnProperty(key)) {
        Module[key] = moduleOverrides[key]
      }
    }
    moduleOverrides = null
    if (Module['arguments']) arguments_ = Module['arguments']
    if (!Object.getOwnPropertyDescriptor(Module, 'arguments')) {
      Object.defineProperty(Module, 'arguments', {
        configurable: true,
        get: function () {
          abort(
            'Module.arguments has been replaced with plain arguments_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)'
          )
        }
      })
    }
    if (Module['thisProgram']) thisProgram = Module['thisProgram']
    if (!Object.getOwnPropertyDescriptor(Module, 'thisProgram')) {
      Object.defineProperty(Module, 'thisProgram', {
        configurable: true,
        get: function () {
          abort(
            'Module.thisProgram has been replaced with plain thisProgram (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)'
          )
        }
      })
    }
    if (Module['quit']) quit_ = Module['quit']
    if (!Object.getOwnPropertyDescriptor(Module, 'quit')) {
      Object.defineProperty(Module, 'quit', {
        configurable: true,
        get: function () {
          abort(
            'Module.quit has been replaced with plain quit_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)'
          )
        }
      })
    }
    assert(
      typeof Module['memoryInitializerPrefixURL'] === 'undefined',
      'Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead'
    )
    assert(
      typeof Module['pthreadMainPrefixURL'] === 'undefined',
      'Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead'
    )
    assert(
      typeof Module['cdInitializerPrefixURL'] === 'undefined',
      'Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead'
    )
    assert(
      typeof Module['filePackagePrefixURL'] === 'undefined',
      'Module.filePackagePrefixURL option was removed, use Module.locateFile instead'
    )
    assert(
      typeof Module['read'] === 'undefined',
      'Module.read option was removed (modify read_ in JS)'
    )
    assert(
      typeof Module['readAsync'] === 'undefined',
      'Module.readAsync option was removed (modify readAsync in JS)'
    )
    assert(
      typeof Module['readBinary'] === 'undefined',
      'Module.readBinary option was removed (modify readBinary in JS)'
    )
    assert(
      typeof Module['setWindowTitle'] === 'undefined',
      'Module.setWindowTitle option was removed (modify setWindowTitle in JS)'
    )
    assert(
      typeof Module['TOTAL_MEMORY'] === 'undefined',
      'Module.TOTAL_MEMORY has been renamed Module.INITIAL_MEMORY'
    )
    if (!Object.getOwnPropertyDescriptor(Module, 'read')) {
      Object.defineProperty(Module, 'read', {
        configurable: true,
        get: function () {
          abort(
            'Module.read has been replaced with plain read_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module, 'readAsync')) {
      Object.defineProperty(Module, 'readAsync', {
        configurable: true,
        get: function () {
          abort(
            'Module.readAsync has been replaced with plain readAsync (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module, 'readBinary')) {
      Object.defineProperty(Module, 'readBinary', {
        configurable: true,
        get: function () {
          abort(
            'Module.readBinary has been replaced with plain readBinary (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)'
          )
        }
      })
    }
    if (!Object.getOwnPropertyDescriptor(Module, 'setWindowTitle')) {
      Object.defineProperty(Module, 'setWindowTitle', {
        configurable: true,
        get: function () {
          abort(
            'Module.setWindowTitle has been replaced with plain setWindowTitle (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)'
          )
        }
      })
    }
    var STACK_ALIGN = 16
    function warnOnce(text) {
      if (!warnOnce.shown) warnOnce.shown = {}
      if (!warnOnce.shown[text]) {
        warnOnce.shown[text] = 1
        err(text)
      }
    }
    function convertJsFunctionToWasm(func, sig) {
      if (typeof WebAssembly.Function === 'function') {
        var typeNames = { i: 'i32', j: 'i64', f: 'f32', d: 'f64' }
        var type = { parameters: [], results: sig[0] == 'v' ? [] : [typeNames[sig[0]]] }
        for (var i = 1; i < sig.length; ++i) {
          type.parameters.push(typeNames[sig[i]])
        }
        return new WebAssembly.Function(type, func)
      }
      var typeSection = [1, 0, 1, 96]
      var sigRet = sig.slice(0, 1)
      var sigParam = sig.slice(1)
      var typeCodes = { i: 127, j: 126, f: 125, d: 124 }
      typeSection.push(sigParam.length)
      for (var i = 0; i < sigParam.length; ++i) {
        typeSection.push(typeCodes[sigParam[i]])
      }
      if (sigRet == 'v') {
        typeSection.push(0)
      } else {
        typeSection = typeSection.concat([1, typeCodes[sigRet]])
      }
      typeSection[1] = typeSection.length - 2
      var bytes = new Uint8Array(
        [0, 97, 115, 109, 1, 0, 0, 0].concat(
          typeSection,
          [2, 7, 1, 1, 101, 1, 102, 0, 0, 7, 5, 1, 1, 102, 0, 0]
        )
      )
      var module = new WebAssembly.Module(bytes)
      var instance = new WebAssembly.Instance(module, { e: { f: func } })
      var wrappedFunc = instance.exports['f']
      return wrappedFunc
    }
    var freeTableIndexes = []
    var functionsInTableMap
    function getEmptyTableSlot() {
      if (freeTableIndexes.length) {
        return freeTableIndexes.pop()
      }
      try {
        wasmTable.grow(1)
      } catch (err) {
        if (!(err instanceof RangeError)) {
          throw err
        }
        throw 'Unable to grow wasm table. Set ALLOW_TABLE_GROWTH.'
      }
      return wasmTable.length - 1
    }
    function addFunctionWasm(func, sig) {
      if (!functionsInTableMap) {
        functionsInTableMap = new WeakMap()
        for (var i = 0; i < wasmTable.length; i++) {
          var item = wasmTable.get(i)
          if (item) {
            functionsInTableMap.set(item, i)
          }
        }
      }
      if (functionsInTableMap.has(func)) {
        return functionsInTableMap.get(func)
      }
      var ret = getEmptyTableSlot()
      try {
        wasmTable.set(ret, func)
      } catch (err) {
        if (!(err instanceof TypeError)) {
          throw err
        }
        assert(typeof sig !== 'undefined', 'Missing signature argument to addFunction: ' + func)
        var wrapped = convertJsFunctionToWasm(func, sig)
        wasmTable.set(ret, wrapped)
      }
      functionsInTableMap.set(func, ret)
      return ret
    }
    var tempRet0 = 0
    var setTempRet0 = function (value) {
      tempRet0 = value
    }
    var getTempRet0 = function () {
      return tempRet0
    }
    var wasmBinary
    if (Module['wasmBinary']) wasmBinary = Module['wasmBinary']
    if (!Object.getOwnPropertyDescriptor(Module, 'wasmBinary')) {
      Object.defineProperty(Module, 'wasmBinary', {
        configurable: true,
        get: function () {
          abort(
            'Module.wasmBinary has been replaced with plain wasmBinary (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)'
          )
        }
      })
    }
    var noExitRuntime = Module['noExitRuntime'] || true
    if (!Object.getOwnPropertyDescriptor(Module, 'noExitRuntime')) {
      Object.defineProperty(Module, 'noExitRuntime', {
        configurable: true,
        get: function () {
          abort(
            'Module.noExitRuntime has been replaced with plain noExitRuntime (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)'
          )
        }
      })
    }
    if (typeof WebAssembly !== 'object') {
      abort('no native wasm support detected')
    }
    var wasmMemory
    var ABORT = false
    var EXITSTATUS
    function assert(condition, text) {
      if (!condition) {
        abort('Assertion failed: ' + text)
      }
    }
    function getCFunc(ident) {
      var func = Module['_' + ident]
      assert(func, 'Cannot call unknown function ' + ident + ', make sure it is exported')
      return func
    }
    function ccall(ident, returnType, argTypes, args, opts) {
      var toC = {
        string: function (str) {
          var ret = 0
          if (str !== null && str !== undefined && str !== 0) {
            var len = (str.length << 2) + 1
            ret = stackAlloc(len)
            stringToUTF8(str, ret, len)
          }
          return ret
        },
        array: function (arr) {
          var ret = stackAlloc(arr.length)
          writeArrayToMemory(arr, ret)
          return ret
        }
      }
      function convertReturnValue(ret) {
        if (returnType === 'string') return UTF8ToString(ret)
        if (returnType === 'boolean') return Boolean(ret)
        return ret
      }
      var func = getCFunc(ident)
      var cArgs = []
      var stack = 0
      assert(returnType !== 'array', 'Return type should not be "array".')
      if (args) {
        for (var i = 0; i < args.length; i++) {
          var converter = toC[argTypes[i]]
          if (converter) {
            if (stack === 0) stack = stackSave()
            cArgs[i] = converter(args[i])
          } else {
            cArgs[i] = args[i]
          }
        }
      }
      var ret = func.apply(null, cArgs)
      ret = convertReturnValue(ret)
      if (stack !== 0) stackRestore(stack)
      return ret
    }
    var ALLOC_STACK = 1
    var UTF8Decoder = typeof TextDecoder !== 'undefined' ? new TextDecoder('utf8') : undefined
    function UTF8ArrayToString(heap, idx, maxBytesToRead) {
      var endIdx = idx + maxBytesToRead
      var endPtr = idx
      while (heap[endPtr] && !(endPtr >= endIdx)) ++endPtr
      if (endPtr - idx > 16 && heap.subarray && UTF8Decoder) {
        return UTF8Decoder.decode(heap.subarray(idx, endPtr))
      } else {
        var str = ''
        while (idx < endPtr) {
          var u0 = heap[idx++]
          if (!(u0 & 128)) {
            str += String.fromCharCode(u0)
            continue
          }
          var u1 = heap[idx++] & 63
          if ((u0 & 224) == 192) {
            str += String.fromCharCode(((u0 & 31) << 6) | u1)
            continue
          }
          var u2 = heap[idx++] & 63
          if ((u0 & 240) == 224) {
            u0 = ((u0 & 15) << 12) | (u1 << 6) | u2
          } else {
            if ((u0 & 248) != 240)
              warnOnce(
                'Invalid UTF-8 leading byte 0x' +
                  u0.toString(16) +
                  ' encountered when deserializing a UTF-8 string in wasm memory to a JS string!'
              )
            u0 = ((u0 & 7) << 18) | (u1 << 12) | (u2 << 6) | (heap[idx++] & 63)
          }
          if (u0 < 65536) {
            str += String.fromCharCode(u0)
          } else {
            var ch = u0 - 65536
            str += String.fromCharCode(55296 | (ch >> 10), 56320 | (ch & 1023))
          }
        }
      }
      return str
    }
    function UTF8ToString(ptr, maxBytesToRead) {
      return ptr ? UTF8ArrayToString(HEAPU8, ptr, maxBytesToRead) : ''
    }
    function stringToUTF8Array(str, heap, outIdx, maxBytesToWrite) {
      if (!(maxBytesToWrite > 0)) return 0
      var startIdx = outIdx
      var endIdx = outIdx + maxBytesToWrite - 1
      for (var i = 0; i < str.length; ++i) {
        var u = str.charCodeAt(i)
        if (u >= 55296 && u <= 57343) {
          var u1 = str.charCodeAt(++i)
          u = (65536 + ((u & 1023) << 10)) | (u1 & 1023)
        }
        if (u <= 127) {
          if (outIdx >= endIdx) break
          heap[outIdx++] = u
        } else if (u <= 2047) {
          if (outIdx + 1 >= endIdx) break
          heap[outIdx++] = 192 | (u >> 6)
          heap[outIdx++] = 128 | (u & 63)
        } else if (u <= 65535) {
          if (outIdx + 2 >= endIdx) break
          heap[outIdx++] = 224 | (u >> 12)
          heap[outIdx++] = 128 | ((u >> 6) & 63)
          heap[outIdx++] = 128 | (u & 63)
        } else {
          if (outIdx + 3 >= endIdx) break
          if (u >= 2097152)
            warnOnce(
              'Invalid Unicode code point 0x' +
                u.toString(16) +
                ' encountered when serializing a JS string to a UTF-8 string in wasm memory! (Valid unicode code points should be in range 0-0x1FFFFF).'
            )
          heap[outIdx++] = 240 | (u >> 18)
          heap[outIdx++] = 128 | ((u >> 12) & 63)
          heap[outIdx++] = 128 | ((u >> 6) & 63)
          heap[outIdx++] = 128 | (u & 63)
        }
      }
      heap[outIdx] = 0
      return outIdx - startIdx
    }
    function stringToUTF8(str, outPtr, maxBytesToWrite) {
      assert(
        typeof maxBytesToWrite == 'number',
        'stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!'
      )
      return stringToUTF8Array(str, HEAPU8, outPtr, maxBytesToWrite)
    }
    function lengthBytesUTF8(str) {
      var len = 0
      for (var i = 0; i < str.length; ++i) {
        var u = str.charCodeAt(i)
        if (u >= 55296 && u <= 57343)
          u = (65536 + ((u & 1023) << 10)) | (str.charCodeAt(++i) & 1023)
        if (u <= 127) ++len
        else if (u <= 2047) len += 2
        else if (u <= 65535) len += 3
        else len += 4
      }
      return len
    }
    var UTF16Decoder = typeof TextDecoder !== 'undefined' ? new TextDecoder('utf-16le') : undefined
    function allocateUTF8(str) {
      var size = lengthBytesUTF8(str) + 1
      var ret = _malloc(size)
      if (ret) stringToUTF8Array(str, HEAP8, ret, size)
      return ret
    }
    function writeArrayToMemory(array, buffer) {
      assert(
        array.length >= 0,
        'writeArrayToMemory array must have a length (should be an array or typed array)'
      )
      HEAP8.set(array, buffer)
    }
    function writeAsciiToMemory(str, buffer, dontAddNull) {
      for (var i = 0; i < str.length; ++i) {
        assert((str.charCodeAt(i) === str.charCodeAt(i)) & 255)
        HEAP8[buffer++ >> 0] = str.charCodeAt(i)
      }
      if (!dontAddNull) HEAP8[buffer >> 0] = 0
    }
    function alignUp(x, multiple) {
      if (x % multiple > 0) {
        x += multiple - (x % multiple)
      }
      return x
    }
    var buffer, HEAP8, HEAPU8, HEAP16, HEAPU16, HEAP32, HEAPU32, HEAPF32, HEAPF64
    function updateGlobalBufferAndViews(buf) {
      buffer = buf
      Module['HEAP8'] = HEAP8 = new Int8Array(buf)
      Module['HEAP16'] = HEAP16 = new Int16Array(buf)
      Module['HEAP32'] = HEAP32 = new Int32Array(buf)
      Module['HEAPU8'] = HEAPU8 = new Uint8Array(buf)
      Module['HEAPU16'] = HEAPU16 = new Uint16Array(buf)
      Module['HEAPU32'] = HEAPU32 = new Uint32Array(buf)
      Module['HEAPF32'] = HEAPF32 = new Float32Array(buf)
      Module['HEAPF64'] = HEAPF64 = new Float64Array(buf)
    }
    var TOTAL_STACK = 5242880
    if (Module['TOTAL_STACK'])
      assert(
        TOTAL_STACK === Module['TOTAL_STACK'],
        'the stack size can no longer be determined at runtime'
      )
    var INITIAL_MEMORY = Module['INITIAL_MEMORY'] || 33554432
    if (!Object.getOwnPropertyDescriptor(Module, 'INITIAL_MEMORY')) {
      Object.defineProperty(Module, 'INITIAL_MEMORY', {
        configurable: true,
        get: function () {
          abort(
            'Module.INITIAL_MEMORY has been replaced with plain INITIAL_MEMORY (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)'
          )
        }
      })
    }
    assert(
      INITIAL_MEMORY >= TOTAL_STACK,
      'INITIAL_MEMORY should be larger than TOTAL_STACK, was ' +
        INITIAL_MEMORY +
        '! (TOTAL_STACK=' +
        TOTAL_STACK +
        ')'
    )
    assert(
      typeof Int32Array !== 'undefined' &&
        typeof Float64Array !== 'undefined' &&
        Int32Array.prototype.subarray !== undefined &&
        Int32Array.prototype.set !== undefined,
      'JS engine does not provide full typed array support'
    )
    assert(
      !Module['wasmMemory'],
      'Use of `wasmMemory` detected.  Use -s IMPORTED_MEMORY to define wasmMemory externally'
    )
    assert(
      INITIAL_MEMORY == 33554432,
      'Detected runtime INITIAL_MEMORY setting.  Use -s IMPORTED_MEMORY to define wasmMemory dynamically'
    )
    var wasmTable
    function writeStackCookie() {
      var max = _emscripten_stack_get_end()
      assert((max & 3) == 0)
      HEAPU32[(max >> 2) + 1] = 34821223
      HEAPU32[(max >> 2) + 2] = 2310721022
      HEAP32[0] = 1668509029
    }
    function checkStackCookie() {
      if (ABORT) return
      var max = _emscripten_stack_get_end()
      var cookie1 = HEAPU32[(max >> 2) + 1]
      var cookie2 = HEAPU32[(max >> 2) + 2]
      if (cookie1 != 34821223 || cookie2 != 2310721022) {
        abort(
          'Stack overflow! Stack cookie has been overwritten, expected hex dwords 0x89BACDFE and 0x2135467, but received 0x' +
            cookie2.toString(16) +
            ' ' +
            cookie1.toString(16)
        )
      }
      if (HEAP32[0] !== 1668509029)
        abort('Runtime error: The application has corrupted its heap memory area (address zero)!')
    }
    ;(function () {
      var h16 = new Int16Array(1)
      var h8 = new Int8Array(h16.buffer)
      h16[0] = 25459
      if (h8[0] !== 115 || h8[1] !== 99)
        throw 'Runtime error: expected the system to be little-endian!'
    })()
    var __ATPRERUN__ = []
    var __ATINIT__ = []
    var __ATMAIN__ = []
    var __ATPOSTRUN__ = []
    var runtimeInitialized = false
    var runtimeExited = false
    __ATINIT__.push({
      func: function () {
        ___wasm_call_ctors()
      }
    })
    function preRun() {
      if (Module['preRun']) {
        if (typeof Module['preRun'] == 'function') Module['preRun'] = [Module['preRun']]
        while (Module['preRun'].length) {
          addOnPreRun(Module['preRun'].shift())
        }
      }
      callRuntimeCallbacks(__ATPRERUN__)
    }
    function initRuntime() {
      checkStackCookie()
      assert(!runtimeInitialized)
      runtimeInitialized = true
      callRuntimeCallbacks(__ATINIT__)
    }
    function preMain() {
      checkStackCookie()
      callRuntimeCallbacks(__ATMAIN__)
    }
    function exitRuntime() {
      checkStackCookie()
      runtimeExited = true
    }
    function postRun() {
      checkStackCookie()
      if (Module['postRun']) {
        if (typeof Module['postRun'] == 'function') Module['postRun'] = [Module['postRun']]
        while (Module['postRun'].length) {
          addOnPostRun(Module['postRun'].shift())
        }
      }
      callRuntimeCallbacks(__ATPOSTRUN__)
    }
    function addOnPreRun(cb) {
      __ATPRERUN__.unshift(cb)
    }
    function addOnPostRun(cb) {
      __ATPOSTRUN__.unshift(cb)
    }
    assert(
      Math.imul,
      'This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill'
    )
    assert(
      Math.fround,
      'This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill'
    )
    assert(
      Math.clz32,
      'This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill'
    )
    assert(
      Math.trunc,
      'This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill'
    )
    var runDependencies = 0
    var runDependencyWatcher = null
    var dependenciesFulfilled = null
    var runDependencyTracking = {}
    function addRunDependency(id) {
      runDependencies++
      if (Module['monitorRunDependencies']) {
        Module['monitorRunDependencies'](runDependencies)
      }
      if (id) {
        assert(!runDependencyTracking[id])
        runDependencyTracking[id] = 1
        if (runDependencyWatcher === null && typeof setInterval !== 'undefined') {
          runDependencyWatcher = setInterval(function () {
            if (ABORT) {
              clearInterval(runDependencyWatcher)
              runDependencyWatcher = null
              return
            }
            var shown = false
            for (var dep in runDependencyTracking) {
              if (!shown) {
                shown = true
                err('still waiting on run dependencies:')
              }
              err('dependency: ' + dep)
            }
            if (shown) {
              err('(end of list)')
            }
          }, 1e4)
        }
      } else {
        err('warning: run dependency added without ID')
      }
    }
    function removeRunDependency(id) {
      runDependencies--
      if (Module['monitorRunDependencies']) {
        Module['monitorRunDependencies'](runDependencies)
      }
      if (id) {
        assert(runDependencyTracking[id])
        delete runDependencyTracking[id]
      } else {
        err('warning: run dependency removed without ID')
      }
      if (runDependencies == 0) {
        if (runDependencyWatcher !== null) {
          clearInterval(runDependencyWatcher)
          runDependencyWatcher = null
        }
        if (dependenciesFulfilled) {
          var callback = dependenciesFulfilled
          dependenciesFulfilled = null
          callback()
        }
      }
    }
    Module['preloadedImages'] = {}
    Module['preloadedAudios'] = {}
    function abort(what) {
      if (Module['onAbort']) {
        Module['onAbort'](what)
      }
      what += ''
      err(what)
      ABORT = true
      EXITSTATUS = 1
      var output = 'abort(' + what + ') at ' + stackTrace()
      what = output
      var e = new WebAssembly.RuntimeError(what)
      readyPromiseReject(e)
      throw e
    }
    var FS = {
      error: function () {
        abort(
          'Filesystem support (FS) was not included. The problem is that you are using files from JS, but files were not used from C/C++, so filesystem support was not auto-included. You can force-include filesystem support with  -s FORCE_FILESYSTEM=1'
        )
      },
      init: function () {
        FS.error()
      },
      createDataFile: function () {
        FS.error()
      },
      createPreloadedFile: function () {
        FS.error()
      },
      createLazyFile: function () {
        FS.error()
      },
      open: function () {
        FS.error()
      },
      mkdev: function () {
        FS.error()
      },
      registerDevice: function () {
        FS.error()
      },
      analyzePath: function () {
        FS.error()
      },
      loadFilesFromDB: function () {
        FS.error()
      },
      ErrnoError: function ErrnoError() {
        FS.error()
      }
    }
    Module['FS_createDataFile'] = FS.createDataFile
    Module['FS_createPreloadedFile'] = FS.createPreloadedFile
    function hasPrefix(str, prefix) {
      return String.prototype.startsWith ? str.startsWith(prefix) : str.indexOf(prefix) === 0
    }
    var dataURIPrefix = 'data:application/octet-stream;base64,'
    function isDataURI(filename) {
      return hasPrefix(filename, dataURIPrefix)
    }
    var fileURIPrefix = 'file://'
    function isFileURI(filename) {
      return hasPrefix(filename, fileURIPrefix)
    }
    function createExportWrapper(name, fixedasm) {
      return function () {
        var displayName = name
        var asm = fixedasm
        if (!fixedasm) {
          asm = Module['asm']
        }
        assert(
          runtimeInitialized,
          'native function `' + displayName + '` called before runtime initialization'
        )
        assert(
          !runtimeExited,
          'native function `' +
            displayName +
            '` called after runtime exit (use NO_EXIT_RUNTIME to keep it alive after main() exits)'
        )
        if (!asm[name]) {
          assert(asm[name], 'exported native function `' + displayName + '` not found')
        }
        return asm[name].apply(null, arguments)
      }
    }
    var wasmBinaryFile = 'libIVSDrawer.wasm'
    if (!isDataURI(wasmBinaryFile)) {
      wasmBinaryFile = locateFile(wasmBinaryFile)
    }
    function getBinary(file) {
      try {
        if (file == wasmBinaryFile && wasmBinary) {
          return new Uint8Array(wasmBinary)
        }
        if (readBinary) {
          return readBinary(file)
        } else {
          throw 'both async and sync fetching of the wasm failed'
        }
      } catch (err) {
        abort(err)
      }
    }
    function getBinaryPromise() {
      if (!wasmBinary && (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER)) {
        if (typeof fetch === 'function' && !isFileURI(wasmBinaryFile)) {
          return fetch(wasmBinaryFile, { credentials: 'same-origin' })
            .then(function (response) {
              if (!response['ok']) {
                throw "failed to load wasm binary file at '" + wasmBinaryFile + "'"
              }
              return response['arrayBuffer']()
            })
            .catch(function () {
              return getBinary(wasmBinaryFile)
            })
        } else {
          if (readAsync) {
            return new Promise(function (resolve, reject) {
              readAsync(
                wasmBinaryFile,
                function (response) {
                  resolve(new Uint8Array(response))
                },
                reject
              )
            })
          }
        }
      }
      return Promise.resolve().then(function () {
        return getBinary(wasmBinaryFile)
      })
    }
    function createWasm() {
      var info = { env: asmLibraryArg, wasi_snapshot_preview1: asmLibraryArg }
      function receiveInstance(instance, module) {
        var exports = instance.exports
        Module['asm'] = exports
        wasmMemory = Module['asm']['memory']
        assert(wasmMemory, 'memory not found in wasm exports')
        updateGlobalBufferAndViews(wasmMemory.buffer)
        wasmTable = Module['asm']['__indirect_function_table']
        assert(wasmTable, 'table not found in wasm exports')
        removeRunDependency('wasm-instantiate')
      }
      addRunDependency('wasm-instantiate')
      var trueModule = Module
      function receiveInstantiatedSource(output) {
        assert(
          Module === trueModule,
          'the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?'
        )
        trueModule = null
        receiveInstance(output['instance'])
      }
      function instantiateArrayBuffer(receiver) {
        return getBinaryPromise()
          .then(function (binary) {
            return WebAssembly.instantiate(binary, info)
          })
          .then(receiver, function (reason) {
            err('failed to asynchronously prepare wasm: ' + reason)
            if (isFileURI(wasmBinaryFile)) {
              err(
                'warning: Loading from a file URI (' +
                  wasmBinaryFile +
                  ') is not supported in most browsers. See https://emscripten.org/docs/getting_started/FAQ.html#how-do-i-run-a-local-webserver-for-testing-why-does-my-program-stall-in-downloading-or-preparing'
              )
            }
            abort(reason)
          })
      }
      function instantiateAsync() {
        if (
          !wasmBinary &&
          typeof WebAssembly.instantiateStreaming === 'function' &&
          !isDataURI(wasmBinaryFile) &&
          !isFileURI(wasmBinaryFile) &&
          typeof fetch === 'function'
        ) {
          return fetch(wasmBinaryFile, { credentials: 'same-origin' }).then(function (response) {
            var result = WebAssembly.instantiateStreaming(response, info)
            return result.then(receiveInstantiatedSource, function (reason) {
              err('wasm streaming compile failed: ' + reason)
              err('falling back to ArrayBuffer instantiation')
              return instantiateArrayBuffer(receiveInstantiatedSource)
            })
          })
        } else {
          return instantiateArrayBuffer(receiveInstantiatedSource)
        }
      }
      if (Module['instantiateWasm']) {
        try {
          var exports = Module['instantiateWasm'](info, receiveInstance)
          return exports
        } catch (e) {
          err('Module.instantiateWasm callback failed with error: ' + e)
          return false
        }
      }
      instantiateAsync().catch(readyPromiseReject)
      return {}
    }
    var tempDouble
    var tempI64
    function callRuntimeCallbacks(callbacks) {
      while (callbacks.length > 0) {
        var callback = callbacks.shift()
        if (typeof callback == 'function') {
          callback(Module)
          continue
        }
        var func = callback.func
        if (typeof func === 'number') {
          if (callback.arg === undefined) {
            wasmTable.get(func)()
          } else {
            wasmTable.get(func)(callback.arg)
          }
        } else {
          func(callback.arg === undefined ? null : callback.arg)
        }
      }
    }
    function demangle(func) {
      warnOnce('warning: build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling')
      return func
    }
    function demangleAll(text) {
      var regex = /\b_Z[\w\d_]+/g
      return text.replace(regex, function (x) {
        var y = demangle(x)
        return x === y ? x : y + ' [' + x + ']'
      })
    }
    function jsStackTrace() {
      var error = new Error()
      if (!error.stack) {
        try {
          throw new Error()
        } catch (e) {
          error = e
        }
        if (!error.stack) {
          return '(no stack trace available)'
        }
      }
      return error.stack.toString()
    }
    function stackTrace() {
      var js = jsStackTrace()
      if (Module['extraStackTrace']) js += '\n' + Module['extraStackTrace']()
      return demangleAll(js)
    }
    function ___assert_fail(condition, filename, line, func) {
      abort(
        'Assertion failed: ' +
          UTF8ToString(condition) +
          ', at: ' +
          [
            filename ? UTF8ToString(filename) : 'unknown filename',
            line,
            func ? UTF8ToString(func) : 'unknown function'
          ]
      )
    }
    var ExceptionInfoAttrs = {
      DESTRUCTOR_OFFSET: 0,
      REFCOUNT_OFFSET: 4,
      TYPE_OFFSET: 8,
      CAUGHT_OFFSET: 12,
      RETHROWN_OFFSET: 13,
      SIZE: 16
    }
    function ___cxa_allocate_exception(size) {
      return _malloc(size + ExceptionInfoAttrs.SIZE) + ExceptionInfoAttrs.SIZE
    }
    function _atexit(func, arg) {}
    function ___cxa_atexit(a0, a1) {
      return _atexit(a0, a1)
    }
    function ExceptionInfo(excPtr) {
      this.excPtr = excPtr
      this.ptr = excPtr - ExceptionInfoAttrs.SIZE
      this.set_type = function (type) {
        HEAP32[(this.ptr + ExceptionInfoAttrs.TYPE_OFFSET) >> 2] = type
      }
      this.get_type = function () {
        return HEAP32[(this.ptr + ExceptionInfoAttrs.TYPE_OFFSET) >> 2]
      }
      this.set_destructor = function (destructor) {
        HEAP32[(this.ptr + ExceptionInfoAttrs.DESTRUCTOR_OFFSET) >> 2] = destructor
      }
      this.get_destructor = function () {
        return HEAP32[(this.ptr + ExceptionInfoAttrs.DESTRUCTOR_OFFSET) >> 2]
      }
      this.set_refcount = function (refcount) {
        HEAP32[(this.ptr + ExceptionInfoAttrs.REFCOUNT_OFFSET) >> 2] = refcount
      }
      this.set_caught = function (caught) {
        caught = caught ? 1 : 0
        HEAP8[(this.ptr + ExceptionInfoAttrs.CAUGHT_OFFSET) >> 0] = caught
      }
      this.get_caught = function () {
        return HEAP8[(this.ptr + ExceptionInfoAttrs.CAUGHT_OFFSET) >> 0] != 0
      }
      this.set_rethrown = function (rethrown) {
        rethrown = rethrown ? 1 : 0
        HEAP8[(this.ptr + ExceptionInfoAttrs.RETHROWN_OFFSET) >> 0] = rethrown
      }
      this.get_rethrown = function () {
        return HEAP8[(this.ptr + ExceptionInfoAttrs.RETHROWN_OFFSET) >> 0] != 0
      }
      this.init = function (type, destructor) {
        this.set_type(type)
        this.set_destructor(destructor)
        this.set_refcount(0)
        this.set_caught(false)
        this.set_rethrown(false)
      }
      this.add_ref = function () {
        var value = HEAP32[(this.ptr + ExceptionInfoAttrs.REFCOUNT_OFFSET) >> 2]
        HEAP32[(this.ptr + ExceptionInfoAttrs.REFCOUNT_OFFSET) >> 2] = value + 1
      }
      this.release_ref = function () {
        var prev = HEAP32[(this.ptr + ExceptionInfoAttrs.REFCOUNT_OFFSET) >> 2]
        HEAP32[(this.ptr + ExceptionInfoAttrs.REFCOUNT_OFFSET) >> 2] = prev - 1
        assert(prev > 0)
        return prev === 1
      }
    }
    function CatchInfo(ptr) {
      this.free = function () {
        _free(this.ptr)
        this.ptr = 0
      }
      this.set_base_ptr = function (basePtr) {
        HEAP32[this.ptr >> 2] = basePtr
      }
      this.get_base_ptr = function () {
        return HEAP32[this.ptr >> 2]
      }
      this.set_adjusted_ptr = function (adjustedPtr) {
        var ptrSize = 4
        HEAP32[(this.ptr + ptrSize) >> 2] = adjustedPtr
      }
      this.get_adjusted_ptr = function () {
        var ptrSize = 4
        return HEAP32[(this.ptr + ptrSize) >> 2]
      }
      this.get_exception_ptr = function () {
        var isPointer = ___cxa_is_pointer_type(this.get_exception_info().get_type())
        if (isPointer) {
          return HEAP32[this.get_base_ptr() >> 2]
        }
        var adjusted = this.get_adjusted_ptr()
        if (adjusted !== 0) return adjusted
        return this.get_base_ptr()
      }
      this.get_exception_info = function () {
        return new ExceptionInfo(this.get_base_ptr())
      }
      if (ptr === undefined) {
        this.ptr = _malloc(8)
        this.set_adjusted_ptr(0)
      } else {
        this.ptr = ptr
      }
    }
    var exceptionCaught = []
    function exception_addRef(info) {
      info.add_ref()
    }
    var uncaughtExceptionCount = 0
    function ___cxa_begin_catch(ptr) {
      var catchInfo = new CatchInfo(ptr)
      var info = catchInfo.get_exception_info()
      if (!info.get_caught()) {
        info.set_caught(true)
        uncaughtExceptionCount--
      }
      info.set_rethrown(false)
      exceptionCaught.push(catchInfo)
      exception_addRef(info)
      return catchInfo.get_exception_ptr()
    }
    var exceptionLast = 0
    function ___cxa_free_exception(ptr) {
      try {
        return _free(new ExceptionInfo(ptr).ptr)
      } catch (e) {
        err('exception during cxa_free_exception: ' + e)
      }
    }
    function exception_decRef(info) {
      if (info.release_ref() && !info.get_rethrown()) {
        var destructor = info.get_destructor()
        if (destructor) {
          wasmTable.get(destructor)(info.excPtr)
        }
        ___cxa_free_exception(info.excPtr)
      }
    }
    function ___cxa_end_catch() {
      _setThrew(0)
      assert(exceptionCaught.length > 0)
      var catchInfo = exceptionCaught.pop()
      exception_decRef(catchInfo.get_exception_info())
      catchInfo.free()
      exceptionLast = 0
    }
    function ___resumeException(catchInfoPtr) {
      var catchInfo = new CatchInfo(catchInfoPtr)
      var ptr = catchInfo.get_base_ptr()
      if (!exceptionLast) {
        exceptionLast = ptr
      }
      catchInfo.free()
      throw ptr
    }
    function ___cxa_find_matching_catch_2() {
      var thrown = exceptionLast
      if (!thrown) {
        setTempRet0(0 | 0)
        return 0 | 0
      }
      var info = new ExceptionInfo(thrown)
      var thrownType = info.get_type()
      var catchInfo = new CatchInfo()
      catchInfo.set_base_ptr(thrown)
      if (!thrownType) {
        setTempRet0(0 | 0)
        return catchInfo.ptr | 0
      }
      var typeArray = Array.prototype.slice.call(arguments)
      var stackTop = stackSave()
      var exceptionThrowBuf = stackAlloc(4)
      HEAP32[exceptionThrowBuf >> 2] = thrown
      for (var i = 0; i < typeArray.length; i++) {
        var caughtType = typeArray[i]
        if (caughtType === 0 || caughtType === thrownType) {
          break
        }
        if (___cxa_can_catch(caughtType, thrownType, exceptionThrowBuf)) {
          var adjusted = HEAP32[exceptionThrowBuf >> 2]
          if (thrown !== adjusted) {
            catchInfo.set_adjusted_ptr(adjusted)
          }
          setTempRet0(caughtType | 0)
          return catchInfo.ptr | 0
        }
      }
      stackRestore(stackTop)
      setTempRet0(thrownType | 0)
      return catchInfo.ptr | 0
    }
    function ___cxa_find_matching_catch_3() {
      var thrown = exceptionLast
      if (!thrown) {
        setTempRet0(0 | 0)
        return 0 | 0
      }
      var info = new ExceptionInfo(thrown)
      var thrownType = info.get_type()
      var catchInfo = new CatchInfo()
      catchInfo.set_base_ptr(thrown)
      if (!thrownType) {
        setTempRet0(0 | 0)
        return catchInfo.ptr | 0
      }
      var typeArray = Array.prototype.slice.call(arguments)
      var stackTop = stackSave()
      var exceptionThrowBuf = stackAlloc(4)
      HEAP32[exceptionThrowBuf >> 2] = thrown
      for (var i = 0; i < typeArray.length; i++) {
        var caughtType = typeArray[i]
        if (caughtType === 0 || caughtType === thrownType) {
          break
        }
        if (___cxa_can_catch(caughtType, thrownType, exceptionThrowBuf)) {
          var adjusted = HEAP32[exceptionThrowBuf >> 2]
          if (thrown !== adjusted) {
            catchInfo.set_adjusted_ptr(adjusted)
          }
          setTempRet0(caughtType | 0)
          return catchInfo.ptr | 0
        }
      }
      stackRestore(stackTop)
      setTempRet0(thrownType | 0)
      return catchInfo.ptr | 0
    }
    function ___cxa_find_matching_catch_4() {
      var thrown = exceptionLast
      if (!thrown) {
        setTempRet0(0 | 0)
        return 0 | 0
      }
      var info = new ExceptionInfo(thrown)
      var thrownType = info.get_type()
      var catchInfo = new CatchInfo()
      catchInfo.set_base_ptr(thrown)
      if (!thrownType) {
        setTempRet0(0 | 0)
        return catchInfo.ptr | 0
      }
      var typeArray = Array.prototype.slice.call(arguments)
      var stackTop = stackSave()
      var exceptionThrowBuf = stackAlloc(4)
      HEAP32[exceptionThrowBuf >> 2] = thrown
      for (var i = 0; i < typeArray.length; i++) {
        var caughtType = typeArray[i]
        if (caughtType === 0 || caughtType === thrownType) {
          break
        }
        if (___cxa_can_catch(caughtType, thrownType, exceptionThrowBuf)) {
          var adjusted = HEAP32[exceptionThrowBuf >> 2]
          if (thrown !== adjusted) {
            catchInfo.set_adjusted_ptr(adjusted)
          }
          setTempRet0(caughtType | 0)
          return catchInfo.ptr | 0
        }
      }
      stackRestore(stackTop)
      setTempRet0(thrownType | 0)
      return catchInfo.ptr | 0
    }
    function ___cxa_rethrow() {
      var catchInfo = exceptionCaught.pop()
      if (!catchInfo) {
        abort('no exception to throw')
      }
      var info = catchInfo.get_exception_info()
      var ptr = catchInfo.get_base_ptr()
      if (!info.get_rethrown()) {
        exceptionCaught.push(catchInfo)
        info.set_rethrown(true)
        info.set_caught(false)
        uncaughtExceptionCount++
      } else {
        catchInfo.free()
      }
      exceptionLast = ptr
      throw ptr
    }
    function ___cxa_throw(ptr, type, destructor) {
      var info = new ExceptionInfo(ptr)
      info.init(type, destructor)
      exceptionLast = ptr
      uncaughtExceptionCount++
      throw ptr
    }
    function ___cxa_uncaught_exceptions() {
      return uncaughtExceptionCount
    }
    function _tzset() {
      if (_tzset.called) return
      _tzset.called = true
      var currentYear = new Date().getFullYear()
      var winter = new Date(currentYear, 0, 1)
      var summer = new Date(currentYear, 6, 1)
      var winterOffset = winter.getTimezoneOffset()
      var summerOffset = summer.getTimezoneOffset()
      var stdTimezoneOffset = Math.max(winterOffset, summerOffset)
      HEAP32[__get_timezone() >> 2] = stdTimezoneOffset * 60
      HEAP32[__get_daylight() >> 2] = Number(winterOffset != summerOffset)
      function extractZone(date) {
        var match = date.toTimeString().match(/\(([A-Za-z ]+)\)$/)
        return match ? match[1] : 'GMT'
      }
      var winterName = extractZone(winter)
      var summerName = extractZone(summer)
      var winterNamePtr = allocateUTF8(winterName)
      var summerNamePtr = allocateUTF8(summerName)
      if (summerOffset < winterOffset) {
        HEAP32[__get_tzname() >> 2] = winterNamePtr
        HEAP32[(__get_tzname() + 4) >> 2] = summerNamePtr
      } else {
        HEAP32[__get_tzname() >> 2] = summerNamePtr
        HEAP32[(__get_tzname() + 4) >> 2] = winterNamePtr
      }
    }
    function _localtime_r(time, tmPtr) {
      _tzset()
      var date = new Date(HEAP32[time >> 2] * 1e3)
      HEAP32[tmPtr >> 2] = date.getSeconds()
      HEAP32[(tmPtr + 4) >> 2] = date.getMinutes()
      HEAP32[(tmPtr + 8) >> 2] = date.getHours()
      HEAP32[(tmPtr + 12) >> 2] = date.getDate()
      HEAP32[(tmPtr + 16) >> 2] = date.getMonth()
      HEAP32[(tmPtr + 20) >> 2] = date.getFullYear() - 1900
      HEAP32[(tmPtr + 24) >> 2] = date.getDay()
      var start = new Date(date.getFullYear(), 0, 1)
      var yday = ((date.getTime() - start.getTime()) / (1e3 * 60 * 60 * 24)) | 0
      HEAP32[(tmPtr + 28) >> 2] = yday
      HEAP32[(tmPtr + 36) >> 2] = -(date.getTimezoneOffset() * 60)
      var summerOffset = new Date(date.getFullYear(), 6, 1).getTimezoneOffset()
      var winterOffset = start.getTimezoneOffset()
      var dst =
        (summerOffset != winterOffset &&
          date.getTimezoneOffset() == Math.min(winterOffset, summerOffset)) | 0
      HEAP32[(tmPtr + 32) >> 2] = dst
      var zonePtr = HEAP32[(__get_tzname() + (dst ? 4 : 0)) >> 2]
      HEAP32[(tmPtr + 40) >> 2] = zonePtr
      return tmPtr
    }
    function ___localtime_r(a0, a1) {
      return _localtime_r(a0, a1)
    }
    function _abort() {
      abort()
    }
    var _emscripten_get_now
    if (ENVIRONMENT_IS_NODE) {
      _emscripten_get_now = function () {
        var t = process['hrtime']()
        return t[0] * 1e3 + t[1] / 1e6
      }
    } else if (typeof dateNow !== 'undefined') {
      _emscripten_get_now = dateNow
    } else
      _emscripten_get_now = function () {
        return performance.now()
      }
    var _emscripten_get_now_is_monotonic = true
    function setErrNo(value) {
      HEAP32[___errno_location() >> 2] = value
      return value
    }
    function _clock_gettime(clk_id, tp) {
      var now
      if (clk_id === 0) {
        now = Date.now()
      } else if ((clk_id === 1 || clk_id === 4) && _emscripten_get_now_is_monotonic) {
        now = _emscripten_get_now()
      } else {
        setErrNo(28)
        return -1
      }
      HEAP32[tp >> 2] = (now / 1e3) | 0
      HEAP32[(tp + 4) >> 2] = ((now % 1e3) * 1e3 * 1e3) | 0
      return 0
    }
    function _longjmp(env, value) {
      _setThrew(env, value || 1)
      throw 'longjmp'
    }
    function _emscripten_longjmp(a0, a1) {
      return _longjmp(a0, a1)
    }
    function _emscripten_memcpy_big(dest, src, num) {
      HEAPU8.copyWithin(dest, src, src + num)
    }
    function _emscripten_get_heap_size() {
      return HEAPU8.length
    }
    function emscripten_realloc_buffer(size) {
      try {
        wasmMemory.grow((size - buffer.byteLength + 65535) >>> 16)
        updateGlobalBufferAndViews(wasmMemory.buffer)
        return 1
      } catch (e) {
        console.error(
          'emscripten_realloc_buffer: Attempted to grow heap from ' +
            buffer.byteLength +
            ' bytes to ' +
            size +
            ' bytes, but got error: ' +
            e
        )
      }
    }
    function _emscripten_resize_heap(requestedSize) {
      var oldSize = _emscripten_get_heap_size()
      assert(requestedSize > oldSize)
      var maxHeapSize = 2147483648
      if (requestedSize > maxHeapSize) {
        err(
          'Cannot enlarge memory, asked to go up to ' +
            requestedSize +
            ' bytes, but the limit is ' +
            maxHeapSize +
            ' bytes!'
        )
        return false
      }
      for (var cutDown = 1; cutDown <= 4; cutDown *= 2) {
        var overGrownHeapSize = oldSize * (1 + 0.2 / cutDown)
        overGrownHeapSize = Math.min(overGrownHeapSize, requestedSize + 100663296)
        var newSize = Math.min(
          maxHeapSize,
          alignUp(Math.max(requestedSize, overGrownHeapSize), 65536)
        )
        var replacement = emscripten_realloc_buffer(newSize)
        if (replacement) {
          return true
        }
      }
      err(
        'Failed to grow the heap from ' +
          oldSize +
          ' bytes to ' +
          newSize +
          ' bytes, not enough memory!'
      )
      return false
    }
    var ENV = {}
    function getExecutableName() {
      return thisProgram || './this.program'
    }
    function getEnvStrings() {
      if (!getEnvStrings.strings) {
        var lang =
          (
            (typeof navigator === 'object' && navigator.languages && navigator.languages[0]) ||
            'C'
          ).replace('-', '_') + '.UTF-8'
        var env = {
          USER: 'web_user',
          LOGNAME: 'web_user',
          PATH: '/',
          PWD: '/',
          HOME: '/home/<USER>',
          LANG: lang,
          _: getExecutableName()
        }
        for (var x in ENV) {
          env[x] = ENV[x]
        }
        var strings = []
        for (var x in env) {
          strings.push(x + '=' + env[x])
        }
        getEnvStrings.strings = strings
      }
      return getEnvStrings.strings
    }
    var SYSCALLS = {
      mappings: {},
      buffers: [null, [], []],
      printChar: function (stream, curr) {
        var buffer = SYSCALLS.buffers[stream]
        assert(buffer)
        if (curr === 0 || curr === 10) {
          ;(stream === 1 ? out : err)(UTF8ArrayToString(buffer, 0))
          buffer.length = 0
        } else {
          buffer.push(curr)
        }
      },
      varargs: undefined,
      get: function () {
        assert(SYSCALLS.varargs != undefined)
        SYSCALLS.varargs += 4
        var ret = HEAP32[(SYSCALLS.varargs - 4) >> 2]
        return ret
      },
      getStr: function (ptr) {
        var ret = UTF8ToString(ptr)
        return ret
      },
      get64: function (low, high) {
        if (low >= 0) assert(high === 0)
        else assert(high === -1)
        return low
      }
    }
    function _environ_get(__environ, environ_buf) {
      var bufSize = 0
      getEnvStrings().forEach(function (string, i) {
        var ptr = environ_buf + bufSize
        HEAP32[(__environ + i * 4) >> 2] = ptr
        writeAsciiToMemory(string, ptr)
        bufSize += string.length + 1
      })
      return 0
    }
    function _environ_sizes_get(penviron_count, penviron_buf_size) {
      var strings = getEnvStrings()
      HEAP32[penviron_count >> 2] = strings.length
      var bufSize = 0
      strings.forEach(function (string) {
        bufSize += string.length + 1
      })
      HEAP32[penviron_buf_size >> 2] = bufSize
      return 0
    }
    function _fd_close(fd) {
      abort('it should not be possible to operate on streams when !SYSCALLS_REQUIRE_FILESYSTEM')
      return 0
    }
    function flush_NO_FILESYSTEM() {
      if (typeof _fflush !== 'undefined') _fflush(0)
      var buffers = SYSCALLS.buffers
      if (buffers[1].length) SYSCALLS.printChar(1, 10)
      if (buffers[2].length) SYSCALLS.printChar(2, 10)
    }
    function _fd_write(fd, iov, iovcnt, pnum) {
      var num = 0
      for (var i = 0; i < iovcnt; i++) {
        var ptr = HEAP32[(iov + i * 8) >> 2]
        var len = HEAP32[(iov + (i * 8 + 4)) >> 2]
        for (var j = 0; j < len; j++) {
          SYSCALLS.printChar(fd, HEAPU8[ptr + j])
        }
        num += len
      }
      HEAP32[pnum >> 2] = num
      return 0
    }
    function _getTempRet0() {
      return getTempRet0() | 0
    }
    function _gettimeofday(ptr) {
      var now = Date.now()
      HEAP32[ptr >> 2] = (now / 1e3) | 0
      HEAP32[(ptr + 4) >> 2] = ((now % 1e3) * 1e3) | 0
      return 0
    }
    function _js_clearscreen(canvasID, Color) {
      let canvas = document.getElementById(UTF8ToString(canvasID))
      if (!canvas) {
        return false
      }
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        return false
      }
      ctx.beginPath()
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      ctx.fillStyle = UTF8ToString(Color)
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      ctx.closePath()
      return true
    }
    function _js_draw_characters(
      canvasID,
      FontSize,
      FontColor,
      CharactersText,
      LocationX,
      LocationY,
      Angle
    ) {
      let canvas = document.getElementById(UTF8ToString(canvasID))
      if (!canvas) {
        return false
      }
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        return false
      }
      ctx.font = UTF8ToString(FontSize)
      ctx.fillStyle = UTF8ToString(FontColor)
      if (Angle < 0.001 && Angle > -0.001) {
        ctx.fillText(UTF8ToString(CharactersText), LocationX, LocationY)
        return true
      } else {
        ctx.translate(LocationX, LocationY)
        let AngleRad = (-Angle * Math.PI) / 180
        ctx.rotate(AngleRad)
        ctx.fillText(UTF8ToString(CharactersText), 0, 0)
        ctx.rotate(-AngleRad)
        ctx.translate(-LocationX, -LocationY)
        return true
      }
    }
    function _js_draw_ellipse(
      canvasID,
      DashType1,
      DashType2,
      LineWidth,
      LineColor,
      FillingColor,
      CenterX,
      CenterY,
      RadiusX,
      RadiusY
    ) {
      let canvas = document.getElementById(UTF8ToString(canvasID))
      if (!canvas) {
        return false
      }
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        return false
      }
      ctx.beginPath()
      ctx.lineWidth = LineWidth
      ctx.strokeStyle = UTF8ToString(LineColor)
      ctx.setLineDash([DashType1, DashType2])
      ctx.ellipse(CenterX, CenterY, RadiusX, RadiusY, 0, 0, 2 * Math.PI)
      ctx.stroke()
      if (0 !== FillingColor) {
        ctx.fillStyle = UTF8ToString(FillingColor)
        ctx.fill()
      }
      ctx.setLineDash([10, 0])
      ctx.closePath()
      return true
    }
    function _js_draw_init_2d(canvasID) {
      let canvas = document.getElementById(UTF8ToString(canvasID))
      if (!canvas) {
        return false
      }
      return true
    }
    function _js_draw_lines(
      canvasID,
      DashType1,
      DashType2,
      LineWidth,
      LineColor,
      LineBeginX,
      LineBeginY,
      LineEndX,
      LineEndY
    ) {
      let canvas = document.getElementById(UTF8ToString(canvasID))
      if (!canvas) {
        return false
      }
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        return false
      }
      ctx.beginPath()
      ctx.moveTo(LineBeginX, LineBeginY)
      ctx.lineTo(LineEndX, LineEndY)
      ctx.lineWidth = LineWidth
      ctx.strokeStyle = UTF8ToString(LineColor)
      ctx.setLineDash([DashType1, DashType2])
      ctx.stroke()
      ctx.setLineDash([10, 0])
      ctx.closePath()
      return true
    }
    function _js_get_win_height(canvasID) {
      let canvas = document.getElementById(UTF8ToString(canvasID))
      if (canvas) {
        return canvas.height
      } else {
        return 0
      }
    }
    function _js_get_win_width(canvasID) {
      let canvas = document.getElementById(UTF8ToString(canvasID))
      if (canvas) {
        return canvas.width
      } else {
        return 0
      }
    }
    function _llvm_eh_typeid_for(type) {
      return type
    }
    function _setTempRet0($i) {
      setTempRet0($i | 0)
    }
    function __isLeapYear(year) {
      return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0)
    }
    function __arraySum(array, index) {
      var sum = 0
      for (var i = 0; i <= index; sum += array[i++]) {}
      return sum
    }
    var __MONTH_DAYS_LEAP = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
    var __MONTH_DAYS_REGULAR = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
    function __addDays(date, days) {
      var newDate = new Date(date.getTime())
      while (days > 0) {
        var leap = __isLeapYear(newDate.getFullYear())
        var currentMonth = newDate.getMonth()
        var daysInCurrentMonth = (leap ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR)[currentMonth]
        if (days > daysInCurrentMonth - newDate.getDate()) {
          days -= daysInCurrentMonth - newDate.getDate() + 1
          newDate.setDate(1)
          if (currentMonth < 11) {
            newDate.setMonth(currentMonth + 1)
          } else {
            newDate.setMonth(0)
            newDate.setFullYear(newDate.getFullYear() + 1)
          }
        } else {
          newDate.setDate(newDate.getDate() + days)
          return newDate
        }
      }
      return newDate
    }
    function _strftime(s, maxsize, format, tm) {
      var tm_zone = HEAP32[(tm + 40) >> 2]
      var date = {
        tm_sec: HEAP32[tm >> 2],
        tm_min: HEAP32[(tm + 4) >> 2],
        tm_hour: HEAP32[(tm + 8) >> 2],
        tm_mday: HEAP32[(tm + 12) >> 2],
        tm_mon: HEAP32[(tm + 16) >> 2],
        tm_year: HEAP32[(tm + 20) >> 2],
        tm_wday: HEAP32[(tm + 24) >> 2],
        tm_yday: HEAP32[(tm + 28) >> 2],
        tm_isdst: HEAP32[(tm + 32) >> 2],
        tm_gmtoff: HEAP32[(tm + 36) >> 2],
        tm_zone: tm_zone ? UTF8ToString(tm_zone) : ''
      }
      var pattern = UTF8ToString(format)
      var EXPANSION_RULES_1 = {
        '%c': '%a %b %d %H:%M:%S %Y',
        '%D': '%m/%d/%y',
        '%F': '%Y-%m-%d',
        '%h': '%b',
        '%r': '%I:%M:%S %p',
        '%R': '%H:%M',
        '%T': '%H:%M:%S',
        '%x': '%m/%d/%y',
        '%X': '%H:%M:%S',
        '%Ec': '%c',
        '%EC': '%C',
        '%Ex': '%m/%d/%y',
        '%EX': '%H:%M:%S',
        '%Ey': '%y',
        '%EY': '%Y',
        '%Od': '%d',
        '%Oe': '%e',
        '%OH': '%H',
        '%OI': '%I',
        '%Om': '%m',
        '%OM': '%M',
        '%OS': '%S',
        '%Ou': '%u',
        '%OU': '%U',
        '%OV': '%V',
        '%Ow': '%w',
        '%OW': '%W',
        '%Oy': '%y'
      }
      for (var rule in EXPANSION_RULES_1) {
        pattern = pattern.replace(new RegExp(rule, 'g'), EXPANSION_RULES_1[rule])
      }
      var WEEKDAYS = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
      var MONTHS = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'
      ]
      function leadingSomething(value, digits, character) {
        var str = typeof value === 'number' ? value.toString() : value || ''
        while (str.length < digits) {
          str = character[0] + str
        }
        return str
      }
      function leadingNulls(value, digits) {
        return leadingSomething(value, digits, '0')
      }
      function compareByDay(date1, date2) {
        function sgn(value) {
          return value < 0 ? -1 : value > 0 ? 1 : 0
        }
        var compare
        if ((compare = sgn(date1.getFullYear() - date2.getFullYear())) === 0) {
          if ((compare = sgn(date1.getMonth() - date2.getMonth())) === 0) {
            compare = sgn(date1.getDate() - date2.getDate())
          }
        }
        return compare
      }
      function getFirstWeekStartDate(janFourth) {
        switch (janFourth.getDay()) {
          case 0:
            return new Date(janFourth.getFullYear() - 1, 11, 29)
          case 1:
            return janFourth
          case 2:
            return new Date(janFourth.getFullYear(), 0, 3)
          case 3:
            return new Date(janFourth.getFullYear(), 0, 2)
          case 4:
            return new Date(janFourth.getFullYear(), 0, 1)
          case 5:
            return new Date(janFourth.getFullYear() - 1, 11, 31)
          case 6:
            return new Date(janFourth.getFullYear() - 1, 11, 30)
        }
      }
      function getWeekBasedYear(date) {
        var thisDate = __addDays(new Date(date.tm_year + 1900, 0, 1), date.tm_yday)
        var janFourthThisYear = new Date(thisDate.getFullYear(), 0, 4)
        var janFourthNextYear = new Date(thisDate.getFullYear() + 1, 0, 4)
        var firstWeekStartThisYear = getFirstWeekStartDate(janFourthThisYear)
        var firstWeekStartNextYear = getFirstWeekStartDate(janFourthNextYear)
        if (compareByDay(firstWeekStartThisYear, thisDate) <= 0) {
          if (compareByDay(firstWeekStartNextYear, thisDate) <= 0) {
            return thisDate.getFullYear() + 1
          } else {
            return thisDate.getFullYear()
          }
        } else {
          return thisDate.getFullYear() - 1
        }
      }
      var EXPANSION_RULES_2 = {
        '%a': function (date) {
          return WEEKDAYS[date.tm_wday].substring(0, 3)
        },
        '%A': function (date) {
          return WEEKDAYS[date.tm_wday]
        },
        '%b': function (date) {
          return MONTHS[date.tm_mon].substring(0, 3)
        },
        '%B': function (date) {
          return MONTHS[date.tm_mon]
        },
        '%C': function (date) {
          var year = date.tm_year + 1900
          return leadingNulls((year / 100) | 0, 2)
        },
        '%d': function (date) {
          return leadingNulls(date.tm_mday, 2)
        },
        '%e': function (date) {
          return leadingSomething(date.tm_mday, 2, ' ')
        },
        '%g': function (date) {
          return getWeekBasedYear(date).toString().substring(2)
        },
        '%G': function (date) {
          return getWeekBasedYear(date)
        },
        '%H': function (date) {
          return leadingNulls(date.tm_hour, 2)
        },
        '%I': function (date) {
          var twelveHour = date.tm_hour
          if (twelveHour == 0) twelveHour = 12
          else if (twelveHour > 12) twelveHour -= 12
          return leadingNulls(twelveHour, 2)
        },
        '%j': function (date) {
          return leadingNulls(
            date.tm_mday +
              __arraySum(
                __isLeapYear(date.tm_year + 1900) ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR,
                date.tm_mon - 1
              ),
            3
          )
        },
        '%m': function (date) {
          return leadingNulls(date.tm_mon + 1, 2)
        },
        '%M': function (date) {
          return leadingNulls(date.tm_min, 2)
        },
        '%n': function () {
          return '\n'
        },
        '%p': function (date) {
          if (date.tm_hour >= 0 && date.tm_hour < 12) {
            return 'AM'
          } else {
            return 'PM'
          }
        },
        '%S': function (date) {
          return leadingNulls(date.tm_sec, 2)
        },
        '%t': function () {
          return '\t'
        },
        '%u': function (date) {
          return date.tm_wday || 7
        },
        '%U': function (date) {
          var janFirst = new Date(date.tm_year + 1900, 0, 1)
          var firstSunday =
            janFirst.getDay() === 0 ? janFirst : __addDays(janFirst, 7 - janFirst.getDay())
          var endDate = new Date(date.tm_year + 1900, date.tm_mon, date.tm_mday)
          if (compareByDay(firstSunday, endDate) < 0) {
            var februaryFirstUntilEndMonth =
              __arraySum(
                __isLeapYear(endDate.getFullYear()) ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR,
                endDate.getMonth() - 1
              ) - 31
            var firstSundayUntilEndJanuary = 31 - firstSunday.getDate()
            var days = firstSundayUntilEndJanuary + februaryFirstUntilEndMonth + endDate.getDate()
            return leadingNulls(Math.ceil(days / 7), 2)
          }
          return compareByDay(firstSunday, janFirst) === 0 ? '01' : '00'
        },
        '%V': function (date) {
          var janFourthThisYear = new Date(date.tm_year + 1900, 0, 4)
          var janFourthNextYear = new Date(date.tm_year + 1901, 0, 4)
          var firstWeekStartThisYear = getFirstWeekStartDate(janFourthThisYear)
          var firstWeekStartNextYear = getFirstWeekStartDate(janFourthNextYear)
          var endDate = __addDays(new Date(date.tm_year + 1900, 0, 1), date.tm_yday)
          if (compareByDay(endDate, firstWeekStartThisYear) < 0) {
            return '53'
          }
          if (compareByDay(firstWeekStartNextYear, endDate) <= 0) {
            return '01'
          }
          var daysDifference
          if (firstWeekStartThisYear.getFullYear() < date.tm_year + 1900) {
            daysDifference = date.tm_yday + 32 - firstWeekStartThisYear.getDate()
          } else {
            daysDifference = date.tm_yday + 1 - firstWeekStartThisYear.getDate()
          }
          return leadingNulls(Math.ceil(daysDifference / 7), 2)
        },
        '%w': function (date) {
          return date.tm_wday
        },
        '%W': function (date) {
          var janFirst = new Date(date.tm_year, 0, 1)
          var firstMonday =
            janFirst.getDay() === 1
              ? janFirst
              : __addDays(janFirst, janFirst.getDay() === 0 ? 1 : 7 - janFirst.getDay() + 1)
          var endDate = new Date(date.tm_year + 1900, date.tm_mon, date.tm_mday)
          if (compareByDay(firstMonday, endDate) < 0) {
            var februaryFirstUntilEndMonth =
              __arraySum(
                __isLeapYear(endDate.getFullYear()) ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR,
                endDate.getMonth() - 1
              ) - 31
            var firstMondayUntilEndJanuary = 31 - firstMonday.getDate()
            var days = firstMondayUntilEndJanuary + februaryFirstUntilEndMonth + endDate.getDate()
            return leadingNulls(Math.ceil(days / 7), 2)
          }
          return compareByDay(firstMonday, janFirst) === 0 ? '01' : '00'
        },
        '%y': function (date) {
          return (date.tm_year + 1900).toString().substring(2)
        },
        '%Y': function (date) {
          return date.tm_year + 1900
        },
        '%z': function (date) {
          var off = date.tm_gmtoff
          var ahead = off >= 0
          off = Math.abs(off) / 60
          off = (off / 60) * 100 + (off % 60)
          return (ahead ? '+' : '-') + String('0000' + off).slice(-4)
        },
        '%Z': function (date) {
          return date.tm_zone
        },
        '%%': function () {
          return '%'
        }
      }
      for (var rule in EXPANSION_RULES_2) {
        if (pattern.indexOf(rule) >= 0) {
          pattern = pattern.replace(new RegExp(rule, 'g'), EXPANSION_RULES_2[rule](date))
        }
      }
      var bytes = intArrayFromString(pattern, false)
      if (bytes.length > maxsize) {
        return 0
      }
      writeArrayToMemory(bytes, s)
      return bytes.length - 1
    }
    function _strftime_l(s, maxsize, format, tm) {
      return _strftime(s, maxsize, format, tm)
    }
    var ASSERTIONS = true
    function intArrayFromString(stringy, dontAddNull, length) {
      var len = length > 0 ? length : lengthBytesUTF8(stringy) + 1
      var u8array = new Array(len)
      var numBytesWritten = stringToUTF8Array(stringy, u8array, 0, u8array.length)
      if (dontAddNull) u8array.length = numBytesWritten
      return u8array
    }
    var asmLibraryArg = {
      __assert_fail: ___assert_fail,
      __cxa_allocate_exception: ___cxa_allocate_exception,
      __cxa_atexit: ___cxa_atexit,
      __cxa_begin_catch: ___cxa_begin_catch,
      __cxa_end_catch: ___cxa_end_catch,
      __cxa_find_matching_catch_2: ___cxa_find_matching_catch_2,
      __cxa_find_matching_catch_3: ___cxa_find_matching_catch_3,
      __cxa_find_matching_catch_4: ___cxa_find_matching_catch_4,
      __cxa_free_exception: ___cxa_free_exception,
      __cxa_rethrow: ___cxa_rethrow,
      __cxa_throw: ___cxa_throw,
      __cxa_uncaught_exceptions: ___cxa_uncaught_exceptions,
      __localtime_r: ___localtime_r,
      __resumeException: ___resumeException,
      abort: _abort,
      clock_gettime: _clock_gettime,
      emscripten_longjmp: _emscripten_longjmp,
      emscripten_memcpy_big: _emscripten_memcpy_big,
      emscripten_resize_heap: _emscripten_resize_heap,
      environ_get: _environ_get,
      environ_sizes_get: _environ_sizes_get,
      fd_close: _fd_close,
      fd_write: _fd_write,
      getTempRet0: _getTempRet0,
      gettimeofday: _gettimeofday,
      invoke_di: invoke_di,
      invoke_diii: invoke_diii,
      invoke_fiii: invoke_fiii,
      invoke_i: invoke_i,
      invoke_ii: invoke_ii,
      invoke_iif: invoke_iif,
      invoke_iii: invoke_iii,
      invoke_iiii: invoke_iiii,
      invoke_iiiif: invoke_iiiif,
      invoke_iiiii: invoke_iiiii,
      invoke_iiiiid: invoke_iiiiid,
      invoke_iiiiif: invoke_iiiiif,
      invoke_iiiiifi: invoke_iiiiifi,
      invoke_iiiiii: invoke_iiiiii,
      invoke_iiiiiii: invoke_iiiiiii,
      invoke_iiiiiiif: invoke_iiiiiiif,
      invoke_iiiiiiii: invoke_iiiiiiii,
      invoke_iiiiiiiiii: invoke_iiiiiiiiii,
      invoke_iiiiiiiiiii: invoke_iiiiiiiiiii,
      invoke_iiiiiiiiiiii: invoke_iiiiiiiiiiii,
      invoke_iiiiiiiiiiiii: invoke_iiiiiiiiiiiii,
      invoke_jiiii: invoke_jiiii,
      invoke_v: invoke_v,
      invoke_vi: invoke_vi,
      invoke_vii: invoke_vii,
      invoke_viii: invoke_viii,
      invoke_viiii: invoke_viiii,
      invoke_viiiii: invoke_viiiii,
      invoke_viiiiii: invoke_viiiiii,
      invoke_viiiiiii: invoke_viiiiiii,
      invoke_viiiiiiiii: invoke_viiiiiiiii,
      invoke_viiiiiiiiii: invoke_viiiiiiiiii,
      invoke_viiiiiiiiiiiiiii: invoke_viiiiiiiiiiiiiii,
      js_clearscreen: _js_clearscreen,
      js_draw_characters: _js_draw_characters,
      js_draw_ellipse: _js_draw_ellipse,
      js_draw_init_2d: _js_draw_init_2d,
      js_draw_lines: _js_draw_lines,
      js_get_win_height: _js_get_win_height,
      js_get_win_width: _js_get_win_width,
      llvm_eh_typeid_for: _llvm_eh_typeid_for,
      localtime_r: _localtime_r,
      setTempRet0: _setTempRet0,
      strftime_l: _strftime_l
    }
    var asm = createWasm()
    var ___wasm_call_ctors = (Module['___wasm_call_ctors'] =
      createExportWrapper('__wasm_call_ctors'))
    var _DRAW_Open = (Module['_DRAW_Open'] = createExportWrapper('DRAW_Open'))
    var _DRAW_Close = (Module['_DRAW_Close'] = createExportWrapper('DRAW_Close'))
    var _DRAW_SetWebCanvas = (Module['_DRAW_SetWebCanvas'] =
      createExportWrapper('DRAW_SetWebCanvas'))
    var _DRAW_InputJsonData = (Module['_DRAW_InputJsonData'] =
      createExportWrapper('DRAW_InputJsonData'))
    var _DRAW_InputRuleData = (Module['_DRAW_InputRuleData'] =
      createExportWrapper('DRAW_InputRuleData'))
    var _DRAW_InputRuleDataEx = (Module['_DRAW_InputRuleDataEx'] =
      createExportWrapper('DRAW_InputRuleDataEx'))
    var _DRAW_InputTrackData = (Module['_DRAW_InputTrackData'] =
      createExportWrapper('DRAW_InputTrackData'))
    var _DRAW_InputTrackDataEx2 = (Module['_DRAW_InputTrackDataEx2'] =
      createExportWrapper('DRAW_InputTrackDataEx2'))
    var _DRAW_InputIVSData = (Module['_DRAW_InputIVSData'] =
      createExportWrapper('DRAW_InputIVSData'))
    var _DRAW_DrawByRenderHandle = (Module['_DRAW_DrawByRenderHandle'] =
      createExportWrapper('DRAW_DrawByRenderHandle'))
    var _DRAW_SetLifeCount = (Module['_DRAW_SetLifeCount'] =
      createExportWrapper('DRAW_SetLifeCount'))
    var _DRAW_SetEnable = (Module['_DRAW_SetEnable'] = createExportWrapper('DRAW_SetEnable'))
    var _DRAW_SetPen = (Module['_DRAW_SetPen'] = createExportWrapper('DRAW_SetPen'))
    var _DRAW_Clean = (Module['_DRAW_Clean'] = createExportWrapper('DRAW_Clean'))
    var _DRAW_CleanScreen = (Module['_DRAW_CleanScreen'] = createExportWrapper('DRAW_CleanScreen'))
    var _DRAW_SetDrawSurfaceParam = (Module['_DRAW_SetDrawSurfaceParam'] = createExportWrapper(
      'DRAW_SetDrawSurfaceParam'
    ))
    var _DRAW_SetTranslateString = (Module['_DRAW_SetTranslateString'] =
      createExportWrapper('DRAW_SetTranslateString'))
    var _DRAW_SetLanguageEnvi = (Module['_DRAW_SetLanguageEnvi'] =
      createExportWrapper('DRAW_SetLanguageEnvi'))
    var _DRAW_SetPrintLogLevel = (Module['_DRAW_SetPrintLogLevel'] =
      createExportWrapper('DRAW_SetPrintLogLevel'))
    var ___errno_location = (Module['___errno_location'] = createExportWrapper('__errno_location'))
    var _fflush = (Module['_fflush'] = createExportWrapper('fflush'))
    var __get_tzname = (Module['__get_tzname'] = createExportWrapper('_get_tzname'))
    var __get_daylight = (Module['__get_daylight'] = createExportWrapper('_get_daylight'))
    var __get_timezone = (Module['__get_timezone'] = createExportWrapper('_get_timezone'))
    var _emscripten_main_thread_process_queued_calls = (Module[
      '_emscripten_main_thread_process_queued_calls'
    ] = createExportWrapper('emscripten_main_thread_process_queued_calls'))
    var stackSave = (Module['stackSave'] = createExportWrapper('stackSave'))
    var stackRestore = (Module['stackRestore'] = createExportWrapper('stackRestore'))
    var stackAlloc = (Module['stackAlloc'] = createExportWrapper('stackAlloc'))
    var _emscripten_stack_init = (Module['_emscripten_stack_init'] = function () {
      return (_emscripten_stack_init = Module['_emscripten_stack_init'] =
        Module['asm']['emscripten_stack_init']).apply(null, arguments)
    })
    var _emscripten_stack_get_free = (Module['_emscripten_stack_get_free'] = function () {
      return (_emscripten_stack_get_free = Module['_emscripten_stack_get_free'] =
        Module['asm']['emscripten_stack_get_free']).apply(null, arguments)
    })
    var _emscripten_stack_get_end = (Module['_emscripten_stack_get_end'] = function () {
      return (_emscripten_stack_get_end = Module['_emscripten_stack_get_end'] =
        Module['asm']['emscripten_stack_get_end']).apply(null, arguments)
    })
    var _setThrew = (Module['_setThrew'] = createExportWrapper('setThrew'))
    var ___cxa_can_catch = (Module['___cxa_can_catch'] = createExportWrapper('__cxa_can_catch'))
    var ___cxa_is_pointer_type = (Module['___cxa_is_pointer_type'] =
      createExportWrapper('__cxa_is_pointer_type'))
    var _malloc = (Module['_malloc'] = createExportWrapper('malloc'))
    var _free = (Module['_free'] = createExportWrapper('free'))
    var dynCall_jiji = (Module['dynCall_jiji'] = createExportWrapper('dynCall_jiji'))
    var dynCall_jiiii = (Module['dynCall_jiiii'] = createExportWrapper('dynCall_jiiii'))
    var dynCall_iiiiij = (Module['dynCall_iiiiij'] = createExportWrapper('dynCall_iiiiij'))
    var dynCall_iiiiijj = (Module['dynCall_iiiiijj'] = createExportWrapper('dynCall_iiiiijj'))
    var dynCall_iiiiiijj = (Module['dynCall_iiiiiijj'] = createExportWrapper('dynCall_iiiiiijj'))
    var dynCall_viijii = (Module['dynCall_viijii'] = createExportWrapper('dynCall_viijii'))
    function invoke_iii(index, a1, a2) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_i(index) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)()
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_v(index) {
      var sp = stackSave()
      try {
        wasmTable.get(index)()
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_vii(index, a1, a2) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_ii(index, a1) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiiii(index, a1, a2, a3, a4, a5) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiii(index, a1, a2, a3) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_viiii(index, a1, a2, a3, a4) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2, a3, a4)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiii(index, a1, a2, a3, a4) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_fiii(index, a1, a2, a3) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_diii(index, a1, a2, a3) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_vi(index, a1) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_viii(index, a1, a2, a3) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2, a3)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_viiiii(index, a1, a2, a3, a4, a5) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2, a3, a4, a5)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_viiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiiiiii(index, a1, a2, a3, a4, a5, a6, a7) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiiiiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_viiiiiii(index, a1, a2, a3, a4, a5, a6, a7) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiiiii(index, a1, a2, a3, a4, a5, a6) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5, a6)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiiiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_viiiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_viiiiiiiiiiiiiii(
      index,
      a1,
      a2,
      a3,
      a4,
      a5,
      a6,
      a7,
      a8,
      a9,
      a10,
      a11,
      a12,
      a13,
      a14,
      a15
    ) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13, a14, a15)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_viiiiii(index, a1, a2, a3, a4, a5, a6) {
      var sp = stackSave()
      try {
        wasmTable.get(index)(a1, a2, a3, a4, a5, a6)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiiid(index, a1, a2, a3, a4, a5) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iif(index, a1, a2) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiiifi(index, a1, a2, a3, a4, a5, a6) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5, a6)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiiiiif(index, a1, a2, a3, a4, a5, a6, a7) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiiif(index, a1, a2, a3, a4, a5) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4, a5)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_iiiif(index, a1, a2, a3, a4) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1, a2, a3, a4)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_di(index, a1) {
      var sp = stackSave()
      try {
        return wasmTable.get(index)(a1)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    function invoke_jiiii(index, a1, a2, a3, a4) {
      var sp = stackSave()
      try {
        return dynCall_jiiii(index, a1, a2, a3, a4)
      } catch (e) {
        stackRestore(sp)
        if (e !== e + 0 && e !== 'longjmp') throw e
        _setThrew(1, 0)
      }
    }
    if (!Object.getOwnPropertyDescriptor(Module, 'intArrayFromString'))
      Module['intArrayFromString'] = function () {
        abort(
          "'intArrayFromString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'intArrayToString'))
      Module['intArrayToString'] = function () {
        abort(
          "'intArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'ccall'))
      Module['ccall'] = function () {
        abort("'ccall' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'cwrap'))
      Module['cwrap'] = function () {
        abort("'cwrap' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'setValue'))
      Module['setValue'] = function () {
        abort("'setValue' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'getValue'))
      Module['getValue'] = function () {
        abort("'getValue' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'allocate'))
      Module['allocate'] = function () {
        abort("'allocate' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'UTF8ArrayToString'))
      Module['UTF8ArrayToString'] = function () {
        abort(
          "'UTF8ArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'UTF8ToString'))
      Module['UTF8ToString'] = function () {
        abort(
          "'UTF8ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'stringToUTF8Array'))
      Module['stringToUTF8Array'] = function () {
        abort(
          "'stringToUTF8Array' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'stringToUTF8'))
      Module['stringToUTF8'] = function () {
        abort(
          "'stringToUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'lengthBytesUTF8'))
      Module['lengthBytesUTF8'] = function () {
        abort(
          "'lengthBytesUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'stackTrace'))
      Module['stackTrace'] = function () {
        abort(
          "'stackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'addOnPreRun'))
      Module['addOnPreRun'] = function () {
        abort(
          "'addOnPreRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'addOnInit'))
      Module['addOnInit'] = function () {
        abort(
          "'addOnInit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'addOnPreMain'))
      Module['addOnPreMain'] = function () {
        abort(
          "'addOnPreMain' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'addOnExit'))
      Module['addOnExit'] = function () {
        abort(
          "'addOnExit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'addOnPostRun'))
      Module['addOnPostRun'] = function () {
        abort(
          "'addOnPostRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'writeStringToMemory'))
      Module['writeStringToMemory'] = function () {
        abort(
          "'writeStringToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    Module['writeArrayToMemory'] = writeArrayToMemory
    if (!Object.getOwnPropertyDescriptor(Module, 'writeAsciiToMemory'))
      Module['writeAsciiToMemory'] = function () {
        abort(
          "'writeAsciiToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'addRunDependency'))
      Module['addRunDependency'] = function () {
        abort(
          "'addRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'removeRunDependency'))
      Module['removeRunDependency'] = function () {
        abort(
          "'removeRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'FS_createFolder'))
      Module['FS_createFolder'] = function () {
        abort(
          "'FS_createFolder' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'FS_createPath'))
      Module['FS_createPath'] = function () {
        abort(
          "'FS_createPath' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'FS_createDataFile'))
      Module['FS_createDataFile'] = function () {
        abort(
          "'FS_createDataFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'FS_createPreloadedFile'))
      Module['FS_createPreloadedFile'] = function () {
        abort(
          "'FS_createPreloadedFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'FS_createLazyFile'))
      Module['FS_createLazyFile'] = function () {
        abort(
          "'FS_createLazyFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'FS_createLink'))
      Module['FS_createLink'] = function () {
        abort(
          "'FS_createLink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'FS_createDevice'))
      Module['FS_createDevice'] = function () {
        abort(
          "'FS_createDevice' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'FS_unlink'))
      Module['FS_unlink'] = function () {
        abort(
          "'FS_unlink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'getLEB'))
      Module['getLEB'] = function () {
        abort("'getLEB' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'getFunctionTables'))
      Module['getFunctionTables'] = function () {
        abort(
          "'getFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'alignFunctionTables'))
      Module['alignFunctionTables'] = function () {
        abort(
          "'alignFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerFunctions'))
      Module['registerFunctions'] = function () {
        abort(
          "'registerFunctions' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'addFunction'))
      Module['addFunction'] = function () {
        abort(
          "'addFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'removeFunction'))
      Module['removeFunction'] = function () {
        abort(
          "'removeFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'getFuncWrapper'))
      Module['getFuncWrapper'] = function () {
        abort(
          "'getFuncWrapper' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'prettyPrint'))
      Module['prettyPrint'] = function () {
        abort(
          "'prettyPrint' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'makeBigInt'))
      Module['makeBigInt'] = function () {
        abort(
          "'makeBigInt' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'dynCall'))
      Module['dynCall'] = function () {
        abort("'dynCall' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'getCompilerSetting'))
      Module['getCompilerSetting'] = function () {
        abort(
          "'getCompilerSetting' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'print'))
      Module['print'] = function () {
        abort("'print' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'printErr'))
      Module['printErr'] = function () {
        abort("'printErr' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'getTempRet0'))
      Module['getTempRet0'] = function () {
        abort(
          "'getTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'setTempRet0'))
      Module['setTempRet0'] = function () {
        abort(
          "'setTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'callMain'))
      Module['callMain'] = function () {
        abort("'callMain' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'abort'))
      Module['abort'] = function () {
        abort("'abort' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'stringToNewUTF8'))
      Module['stringToNewUTF8'] = function () {
        abort(
          "'stringToNewUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'setFileTime'))
      Module['setFileTime'] = function () {
        abort(
          "'setFileTime' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'emscripten_realloc_buffer'))
      Module['emscripten_realloc_buffer'] = function () {
        abort(
          "'emscripten_realloc_buffer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'ENV'))
      Module['ENV'] = function () {
        abort("'ENV' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'ERRNO_CODES'))
      Module['ERRNO_CODES'] = function () {
        abort(
          "'ERRNO_CODES' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'ERRNO_MESSAGES'))
      Module['ERRNO_MESSAGES'] = function () {
        abort(
          "'ERRNO_MESSAGES' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'setErrNo'))
      Module['setErrNo'] = function () {
        abort("'setErrNo' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'inetPton4'))
      Module['inetPton4'] = function () {
        abort(
          "'inetPton4' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'inetNtop4'))
      Module['inetNtop4'] = function () {
        abort(
          "'inetNtop4' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'inetPton6'))
      Module['inetPton6'] = function () {
        abort(
          "'inetPton6' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'inetNtop6'))
      Module['inetNtop6'] = function () {
        abort(
          "'inetNtop6' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'readSockaddr'))
      Module['readSockaddr'] = function () {
        abort(
          "'readSockaddr' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'writeSockaddr'))
      Module['writeSockaddr'] = function () {
        abort(
          "'writeSockaddr' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'DNS'))
      Module['DNS'] = function () {
        abort("'DNS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'getHostByName'))
      Module['getHostByName'] = function () {
        abort(
          "'getHostByName' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'GAI_ERRNO_MESSAGES'))
      Module['GAI_ERRNO_MESSAGES'] = function () {
        abort(
          "'GAI_ERRNO_MESSAGES' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'Protocols'))
      Module['Protocols'] = function () {
        abort(
          "'Protocols' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'Sockets'))
      Module['Sockets'] = function () {
        abort("'Sockets' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'getRandomDevice'))
      Module['getRandomDevice'] = function () {
        abort(
          "'getRandomDevice' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'traverseStack'))
      Module['traverseStack'] = function () {
        abort(
          "'traverseStack' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'UNWIND_CACHE'))
      Module['UNWIND_CACHE'] = function () {
        abort(
          "'UNWIND_CACHE' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'withBuiltinMalloc'))
      Module['withBuiltinMalloc'] = function () {
        abort(
          "'withBuiltinMalloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'readAsmConstArgsArray'))
      Module['readAsmConstArgsArray'] = function () {
        abort(
          "'readAsmConstArgsArray' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'readAsmConstArgs'))
      Module['readAsmConstArgs'] = function () {
        abort(
          "'readAsmConstArgs' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'mainThreadEM_ASM'))
      Module['mainThreadEM_ASM'] = function () {
        abort(
          "'mainThreadEM_ASM' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'jstoi_q'))
      Module['jstoi_q'] = function () {
        abort("'jstoi_q' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'jstoi_s'))
      Module['jstoi_s'] = function () {
        abort("'jstoi_s' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'getExecutableName'))
      Module['getExecutableName'] = function () {
        abort(
          "'getExecutableName' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'listenOnce'))
      Module['listenOnce'] = function () {
        abort(
          "'listenOnce' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'autoResumeAudioContext'))
      Module['autoResumeAudioContext'] = function () {
        abort(
          "'autoResumeAudioContext' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'dynCallLegacy'))
      Module['dynCallLegacy'] = function () {
        abort(
          "'dynCallLegacy' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'getDynCaller'))
      Module['getDynCaller'] = function () {
        abort(
          "'getDynCaller' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'dynCall'))
      Module['dynCall'] = function () {
        abort("'dynCall' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'callRuntimeCallbacks'))
      Module['callRuntimeCallbacks'] = function () {
        abort(
          "'callRuntimeCallbacks' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'reallyNegative'))
      Module['reallyNegative'] = function () {
        abort(
          "'reallyNegative' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'unSign'))
      Module['unSign'] = function () {
        abort("'unSign' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'reSign'))
      Module['reSign'] = function () {
        abort("'reSign' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'formatString'))
      Module['formatString'] = function () {
        abort(
          "'formatString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'PATH'))
      Module['PATH'] = function () {
        abort("'PATH' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'PATH_FS'))
      Module['PATH_FS'] = function () {
        abort("'PATH_FS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'SYSCALLS'))
      Module['SYSCALLS'] = function () {
        abort("'SYSCALLS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'syscallMmap2'))
      Module['syscallMmap2'] = function () {
        abort(
          "'syscallMmap2' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'syscallMunmap'))
      Module['syscallMunmap'] = function () {
        abort(
          "'syscallMunmap' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'getSocketFromFD'))
      Module['getSocketFromFD'] = function () {
        abort(
          "'getSocketFromFD' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'getSocketAddress'))
      Module['getSocketAddress'] = function () {
        abort(
          "'getSocketAddress' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'JSEvents'))
      Module['JSEvents'] = function () {
        abort("'JSEvents' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerKeyEventCallback'))
      Module['registerKeyEventCallback'] = function () {
        abort(
          "'registerKeyEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'specialHTMLTargets'))
      Module['specialHTMLTargets'] = function () {
        abort(
          "'specialHTMLTargets' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'maybeCStringToJsString'))
      Module['maybeCStringToJsString'] = function () {
        abort(
          "'maybeCStringToJsString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'findEventTarget'))
      Module['findEventTarget'] = function () {
        abort(
          "'findEventTarget' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'findCanvasEventTarget'))
      Module['findCanvasEventTarget'] = function () {
        abort(
          "'findCanvasEventTarget' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'getBoundingClientRect'))
      Module['getBoundingClientRect'] = function () {
        abort(
          "'getBoundingClientRect' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'fillMouseEventData'))
      Module['fillMouseEventData'] = function () {
        abort(
          "'fillMouseEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerMouseEventCallback'))
      Module['registerMouseEventCallback'] = function () {
        abort(
          "'registerMouseEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerWheelEventCallback'))
      Module['registerWheelEventCallback'] = function () {
        abort(
          "'registerWheelEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerUiEventCallback'))
      Module['registerUiEventCallback'] = function () {
        abort(
          "'registerUiEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerFocusEventCallback'))
      Module['registerFocusEventCallback'] = function () {
        abort(
          "'registerFocusEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'fillDeviceOrientationEventData'))
      Module['fillDeviceOrientationEventData'] = function () {
        abort(
          "'fillDeviceOrientationEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerDeviceOrientationEventCallback'))
      Module['registerDeviceOrientationEventCallback'] = function () {
        abort(
          "'registerDeviceOrientationEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'fillDeviceMotionEventData'))
      Module['fillDeviceMotionEventData'] = function () {
        abort(
          "'fillDeviceMotionEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerDeviceMotionEventCallback'))
      Module['registerDeviceMotionEventCallback'] = function () {
        abort(
          "'registerDeviceMotionEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'screenOrientation'))
      Module['screenOrientation'] = function () {
        abort(
          "'screenOrientation' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'fillOrientationChangeEventData'))
      Module['fillOrientationChangeEventData'] = function () {
        abort(
          "'fillOrientationChangeEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerOrientationChangeEventCallback'))
      Module['registerOrientationChangeEventCallback'] = function () {
        abort(
          "'registerOrientationChangeEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'fillFullscreenChangeEventData'))
      Module['fillFullscreenChangeEventData'] = function () {
        abort(
          "'fillFullscreenChangeEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerFullscreenChangeEventCallback'))
      Module['registerFullscreenChangeEventCallback'] = function () {
        abort(
          "'registerFullscreenChangeEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerRestoreOldStyle'))
      Module['registerRestoreOldStyle'] = function () {
        abort(
          "'registerRestoreOldStyle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'hideEverythingExceptGivenElement'))
      Module['hideEverythingExceptGivenElement'] = function () {
        abort(
          "'hideEverythingExceptGivenElement' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'restoreHiddenElements'))
      Module['restoreHiddenElements'] = function () {
        abort(
          "'restoreHiddenElements' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'setLetterbox'))
      Module['setLetterbox'] = function () {
        abort(
          "'setLetterbox' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'currentFullscreenStrategy'))
      Module['currentFullscreenStrategy'] = function () {
        abort(
          "'currentFullscreenStrategy' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'restoreOldWindowedStyle'))
      Module['restoreOldWindowedStyle'] = function () {
        abort(
          "'restoreOldWindowedStyle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'softFullscreenResizeWebGLRenderTarget'))
      Module['softFullscreenResizeWebGLRenderTarget'] = function () {
        abort(
          "'softFullscreenResizeWebGLRenderTarget' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'doRequestFullscreen'))
      Module['doRequestFullscreen'] = function () {
        abort(
          "'doRequestFullscreen' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'fillPointerlockChangeEventData'))
      Module['fillPointerlockChangeEventData'] = function () {
        abort(
          "'fillPointerlockChangeEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerPointerlockChangeEventCallback'))
      Module['registerPointerlockChangeEventCallback'] = function () {
        abort(
          "'registerPointerlockChangeEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerPointerlockErrorEventCallback'))
      Module['registerPointerlockErrorEventCallback'] = function () {
        abort(
          "'registerPointerlockErrorEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'requestPointerLock'))
      Module['requestPointerLock'] = function () {
        abort(
          "'requestPointerLock' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'fillVisibilityChangeEventData'))
      Module['fillVisibilityChangeEventData'] = function () {
        abort(
          "'fillVisibilityChangeEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerVisibilityChangeEventCallback'))
      Module['registerVisibilityChangeEventCallback'] = function () {
        abort(
          "'registerVisibilityChangeEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerTouchEventCallback'))
      Module['registerTouchEventCallback'] = function () {
        abort(
          "'registerTouchEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'fillGamepadEventData'))
      Module['fillGamepadEventData'] = function () {
        abort(
          "'fillGamepadEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerGamepadEventCallback'))
      Module['registerGamepadEventCallback'] = function () {
        abort(
          "'registerGamepadEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerBeforeUnloadEventCallback'))
      Module['registerBeforeUnloadEventCallback'] = function () {
        abort(
          "'registerBeforeUnloadEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'fillBatteryEventData'))
      Module['fillBatteryEventData'] = function () {
        abort(
          "'fillBatteryEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'battery'))
      Module['battery'] = function () {
        abort("'battery' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'registerBatteryEventCallback'))
      Module['registerBatteryEventCallback'] = function () {
        abort(
          "'registerBatteryEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'setCanvasElementSize'))
      Module['setCanvasElementSize'] = function () {
        abort(
          "'setCanvasElementSize' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'getCanvasElementSize'))
      Module['getCanvasElementSize'] = function () {
        abort(
          "'getCanvasElementSize' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'polyfillSetImmediate'))
      Module['polyfillSetImmediate'] = function () {
        abort(
          "'polyfillSetImmediate' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'demangle'))
      Module['demangle'] = function () {
        abort("'demangle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'demangleAll'))
      Module['demangleAll'] = function () {
        abort(
          "'demangleAll' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'jsStackTrace'))
      Module['jsStackTrace'] = function () {
        abort(
          "'jsStackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'stackTrace'))
      Module['stackTrace'] = function () {
        abort(
          "'stackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'getEnvStrings'))
      Module['getEnvStrings'] = function () {
        abort(
          "'getEnvStrings' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'checkWasiClock'))
      Module['checkWasiClock'] = function () {
        abort(
          "'checkWasiClock' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'flush_NO_FILESYSTEM'))
      Module['flush_NO_FILESYSTEM'] = function () {
        abort(
          "'flush_NO_FILESYSTEM' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'writeI53ToI64'))
      Module['writeI53ToI64'] = function () {
        abort(
          "'writeI53ToI64' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'writeI53ToI64Clamped'))
      Module['writeI53ToI64Clamped'] = function () {
        abort(
          "'writeI53ToI64Clamped' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'writeI53ToI64Signaling'))
      Module['writeI53ToI64Signaling'] = function () {
        abort(
          "'writeI53ToI64Signaling' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'writeI53ToU64Clamped'))
      Module['writeI53ToU64Clamped'] = function () {
        abort(
          "'writeI53ToU64Clamped' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'writeI53ToU64Signaling'))
      Module['writeI53ToU64Signaling'] = function () {
        abort(
          "'writeI53ToU64Signaling' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'readI53FromI64'))
      Module['readI53FromI64'] = function () {
        abort(
          "'readI53FromI64' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'readI53FromU64'))
      Module['readI53FromU64'] = function () {
        abort(
          "'readI53FromU64' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'convertI32PairToI53'))
      Module['convertI32PairToI53'] = function () {
        abort(
          "'convertI32PairToI53' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'convertU32PairToI53'))
      Module['convertU32PairToI53'] = function () {
        abort(
          "'convertU32PairToI53' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'uncaughtExceptionCount'))
      Module['uncaughtExceptionCount'] = function () {
        abort(
          "'uncaughtExceptionCount' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'exceptionLast'))
      Module['exceptionLast'] = function () {
        abort(
          "'exceptionLast' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'exceptionCaught'))
      Module['exceptionCaught'] = function () {
        abort(
          "'exceptionCaught' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'ExceptionInfoAttrs'))
      Module['ExceptionInfoAttrs'] = function () {
        abort(
          "'ExceptionInfoAttrs' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'ExceptionInfo'))
      Module['ExceptionInfo'] = function () {
        abort(
          "'ExceptionInfo' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'CatchInfo'))
      Module['CatchInfo'] = function () {
        abort(
          "'CatchInfo' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'exception_addRef'))
      Module['exception_addRef'] = function () {
        abort(
          "'exception_addRef' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'exception_decRef'))
      Module['exception_decRef'] = function () {
        abort(
          "'exception_decRef' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'Browser'))
      Module['Browser'] = function () {
        abort("'Browser' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'funcWrappers'))
      Module['funcWrappers'] = function () {
        abort(
          "'funcWrappers' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'getFuncWrapper'))
      Module['getFuncWrapper'] = function () {
        abort(
          "'getFuncWrapper' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'setMainLoop'))
      Module['setMainLoop'] = function () {
        abort(
          "'setMainLoop' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'FS'))
      Module['FS'] = function () {
        abort("'FS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'mmapAlloc'))
      Module['mmapAlloc'] = function () {
        abort(
          "'mmapAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'MEMFS'))
      Module['MEMFS'] = function () {
        abort("'MEMFS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'TTY'))
      Module['TTY'] = function () {
        abort("'TTY' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'PIPEFS'))
      Module['PIPEFS'] = function () {
        abort("'PIPEFS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'SOCKFS'))
      Module['SOCKFS'] = function () {
        abort("'SOCKFS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, '_setNetworkCallback'))
      Module['_setNetworkCallback'] = function () {
        abort(
          "'_setNetworkCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'tempFixedLengthArray'))
      Module['tempFixedLengthArray'] = function () {
        abort(
          "'tempFixedLengthArray' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'miniTempWebGLFloatBuffers'))
      Module['miniTempWebGLFloatBuffers'] = function () {
        abort(
          "'miniTempWebGLFloatBuffers' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'heapObjectForWebGLType'))
      Module['heapObjectForWebGLType'] = function () {
        abort(
          "'heapObjectForWebGLType' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'heapAccessShiftForWebGLHeap'))
      Module['heapAccessShiftForWebGLHeap'] = function () {
        abort(
          "'heapAccessShiftForWebGLHeap' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'GL'))
      Module['GL'] = function () {
        abort("'GL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'emscriptenWebGLGet'))
      Module['emscriptenWebGLGet'] = function () {
        abort(
          "'emscriptenWebGLGet' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'computeUnpackAlignedImageSize'))
      Module['computeUnpackAlignedImageSize'] = function () {
        abort(
          "'computeUnpackAlignedImageSize' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'emscriptenWebGLGetTexPixelData'))
      Module['emscriptenWebGLGetTexPixelData'] = function () {
        abort(
          "'emscriptenWebGLGetTexPixelData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'emscriptenWebGLGetUniform'))
      Module['emscriptenWebGLGetUniform'] = function () {
        abort(
          "'emscriptenWebGLGetUniform' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'emscriptenWebGLGetVertexAttrib'))
      Module['emscriptenWebGLGetVertexAttrib'] = function () {
        abort(
          "'emscriptenWebGLGetVertexAttrib' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'writeGLArray'))
      Module['writeGLArray'] = function () {
        abort(
          "'writeGLArray' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'AL'))
      Module['AL'] = function () {
        abort("'AL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'SDL_unicode'))
      Module['SDL_unicode'] = function () {
        abort(
          "'SDL_unicode' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'SDL_ttfContext'))
      Module['SDL_ttfContext'] = function () {
        abort(
          "'SDL_ttfContext' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'SDL_audio'))
      Module['SDL_audio'] = function () {
        abort(
          "'SDL_audio' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'SDL'))
      Module['SDL'] = function () {
        abort("'SDL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'SDL_gfx'))
      Module['SDL_gfx'] = function () {
        abort("'SDL_gfx' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'GLUT'))
      Module['GLUT'] = function () {
        abort("'GLUT' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'EGL'))
      Module['EGL'] = function () {
        abort("'EGL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'GLFW_Window'))
      Module['GLFW_Window'] = function () {
        abort(
          "'GLFW_Window' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'GLFW'))
      Module['GLFW'] = function () {
        abort("'GLFW' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'GLEW'))
      Module['GLEW'] = function () {
        abort("'GLEW' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'IDBStore'))
      Module['IDBStore'] = function () {
        abort("'IDBStore' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'runAndAbortIfError'))
      Module['runAndAbortIfError'] = function () {
        abort(
          "'runAndAbortIfError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'warnOnce'))
      Module['warnOnce'] = function () {
        abort("'warnOnce' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'stackSave'))
      Module['stackSave'] = function () {
        abort(
          "'stackSave' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'stackRestore'))
      Module['stackRestore'] = function () {
        abort(
          "'stackRestore' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'stackAlloc'))
      Module['stackAlloc'] = function () {
        abort(
          "'stackAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'AsciiToString'))
      Module['AsciiToString'] = function () {
        abort(
          "'AsciiToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'stringToAscii'))
      Module['stringToAscii'] = function () {
        abort(
          "'stringToAscii' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'UTF16ToString'))
      Module['UTF16ToString'] = function () {
        abort(
          "'UTF16ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'stringToUTF16'))
      Module['stringToUTF16'] = function () {
        abort(
          "'stringToUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'lengthBytesUTF16'))
      Module['lengthBytesUTF16'] = function () {
        abort(
          "'lengthBytesUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'UTF32ToString'))
      Module['UTF32ToString'] = function () {
        abort(
          "'UTF32ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'stringToUTF32'))
      Module['stringToUTF32'] = function () {
        abort(
          "'stringToUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    if (!Object.getOwnPropertyDescriptor(Module, 'lengthBytesUTF32'))
      Module['lengthBytesUTF32'] = function () {
        abort(
          "'lengthBytesUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    Module['allocateUTF8'] = allocateUTF8
    if (!Object.getOwnPropertyDescriptor(Module, 'allocateUTF8OnStack'))
      Module['allocateUTF8OnStack'] = function () {
        abort(
          "'allocateUTF8OnStack' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
        )
      }
    Module['writeStackCookie'] = writeStackCookie
    Module['checkStackCookie'] = checkStackCookie
    if (!Object.getOwnPropertyDescriptor(Module, 'ALLOC_NORMAL'))
      Object.defineProperty(Module, 'ALLOC_NORMAL', {
        configurable: true,
        get: function () {
          abort(
            "'ALLOC_NORMAL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
          )
        }
      })
    if (!Object.getOwnPropertyDescriptor(Module, 'ALLOC_STACK'))
      Object.defineProperty(Module, 'ALLOC_STACK', {
        configurable: true,
        get: function () {
          abort(
            "'ALLOC_STACK' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)"
          )
        }
      })
    var calledRun
    function ExitStatus(status) {
      this.name = 'ExitStatus'
      this.message = 'Program terminated with exit(' + status + ')'
      this.status = status
    }
    dependenciesFulfilled = function runCaller() {
      if (!calledRun) run()
      if (!calledRun) dependenciesFulfilled = runCaller
    }
    function stackCheckInit() {
      _emscripten_stack_init()
      writeStackCookie()
    }
    function run(args) {
      args = args || arguments_
      if (runDependencies > 0) {
        return
      }
      stackCheckInit()
      preRun()
      if (runDependencies > 0) {
        return
      }
      function doRun() {
        if (calledRun) return
        calledRun = true
        Module['calledRun'] = true
        if (ABORT) return
        initRuntime()
        preMain()
        readyPromiseResolve(Module)
        if (Module['onRuntimeInitialized']) Module['onRuntimeInitialized']()
        assert(
          !Module['_main'],
          'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]'
        )
        postRun()
      }
      if (Module['setStatus']) {
        Module['setStatus']('Running...')
        setTimeout(function () {
          setTimeout(function () {
            Module['setStatus']('')
          }, 1)
          doRun()
        }, 1)
      } else {
        doRun()
      }
      checkStackCookie()
    }
    Module['run'] = run
    function checkUnflushedContent() {
      var oldOut = out
      var oldErr = err
      var has = false
      out = err = function (x) {
        has = true
      }
      try {
        var flush = flush_NO_FILESYSTEM
        if (flush) flush()
      } catch (e) {}
      out = oldOut
      err = oldErr
      if (has) {
        warnOnce(
          'stdio streams had content in them that was not flushed. you should set EXIT_RUNTIME to 1 (see the FAQ), or make sure to emit a newline when you printf etc.'
        )
        warnOnce(
          '(this may also be due to not including full filesystem support - try building with -s FORCE_FILESYSTEM=1)'
        )
      }
    }
    if (Module['preInit']) {
      if (typeof Module['preInit'] == 'function') Module['preInit'] = [Module['preInit']]
      while (Module['preInit'].length > 0) {
        Module['preInit'].pop()()
      }
    }
    run()

    return IVSDrawer_Module.ready
  }
})()
if (typeof exports === 'object' && typeof module === 'object') module.exports = IVSDrawer_Module
else if (typeof define === 'function' && define['amd'])
  define([], function () {
    return IVSDrawer_Module
  })
else if (typeof exports === 'object') exports['IVSDrawer_Module'] = IVSDrawer_Module
