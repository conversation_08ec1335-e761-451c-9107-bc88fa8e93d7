import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  // history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/OneMap',
      name: 'OneMap',
      component: () => import('@/app/views/OneMap/index.vue'),
      // redirect: '/OneMap/resourceDistribution',
      children: [
        {
          path: '/OneMap/resourceDistribution',
          name: 'OneMap_ResourceDistribution',
          component: () => import('@/app/views/OneMap/children/ResourceDistribution/index.vue')
        },
        {
          path: '/OneMap/pointList',
          name: 'OneMap_PointList',
          component: () => import('@/app/views/OneMap/children/PointList/index.vue')
        },
        {
          path: '/OneMap/scientificMonitoring',
          name: 'OneMap_ScientificMonitoring',
          component: () => import('@/app/views/OneMap/children/ScientificMonitoring/index.vue')
        },
        {
          path: '/OneMap/videoMonitoring',
          name: 'OneMap_VideoMonitoring',
          component: () => import('@/app/views/OneMap/children/VideoMonitoring/index.vue')
        }
      ]
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login/index.vue')
    },
    {
      path: '/ResetPassword',
      name: 'ResetPassword',
      component: () => import('@/views/ResetPassword/index.vue')
    },
    {
      path: '/:catchAll(.*)',
      name: 'Home',
      component: () => import('@/views/Home/index.vue'),
      // redirect: '/user/manage',
      children: [
        // {
        //   path: '/user/manage',
        //   name: 'UserManage',
        //   component: () => import('@/views/UserManage/index.vue')
        // }
      ]
    }
    // {
    //   path: '/:catchAll(.*)',
    //   name: 'NotFound',
    //   component: () => import('@/views/NotFound/index.vue')
    // }
  ]
})

router.beforeEach((to, from, next) => {
  const filterRouteName: string[] = ['NotFound', 'Login']
  // console.log('to, from, :>> ', to, from)
  const access_token = localStorage.getItem('access_token')
  // console.log('access_token :>> ', access_token)
  if (filterRouteName.includes(to.name as string)) {
    next()
  } else if (to.name?.toString().startsWith('OneMap')) {
    next()
  } else {
    // 需要登录的路由，进行 token 判断
    if (access_token) {
      // token 存在，可以继续导航
      next()
    } else {
      // token 不存在，重定向到登录页
      next('/login')
    }
  }
})
export default router
