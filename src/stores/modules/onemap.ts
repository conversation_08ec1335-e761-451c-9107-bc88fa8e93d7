import { defineStore } from 'pinia'
interface OneMapState {
  selectedArea: any
  selectedProtectPointId: string | null
  cityDicts: any[]
  countyDicts: any[]
  pageCurrent: number
  pageStateRouter: any
  pageState: any
}

export const useOneMapStore = defineStore('onemap', () => {
  const state: Ref<OneMapState> = ref({
    selectedArea: null,
    selectedProtectPointId: null,
    cityDicts: [],
    countyDicts: [],
    pageCurrent: 0,
    pageStateRouter: null,
    pageState: null
  })

  // getters
  const selectedArea = computed(() => state.value.selectedArea)
  const selectedProtectPointId = computed(() => state.value.selectedProtectPointId)
  const cityDicts = computed(() => {
    let data = state.value.cityDicts
    data = data.sort((a, b) => {
      return Number(a.ccode) - Number(b.ccode)
    })
    return data
  })
  const countyDicts = computed(() => state.value.countyDicts)
  const pageCurrent = computed(() => state.value.pageCurrent)
  const pageState = computed(() => state.value.pageState)

  // actions
  const setSelectedArea = (areas: any) => {
    state.value.selectedArea = areas
  }

  const setProtectPointId = (id: string) => {
    state.value.selectedProtectPointId = id
  }

  const setAreaDicts = (data, type) => {
    switch (type) {
      case 'city':
        state.value.cityDicts = data
        break
      case 'county':
        state.value.countyDicts = data
        break
    }
  }

  const setAreaByCode = (code: string) => {
    const city = state.value.cityDicts.find((item) => item.ccode === code)
    state.value.selectedArea = city
  }

  const setPageCurrent = (page: number) => {
    state.value.pageCurrent = page
  }

  const pageStatePush = (data: any) => {
    if (state.value.pageStateRouter) {
      state.value.pageStateRouter.next = data
    } else {
      state.value.pageStateRouter = data
    }
    // 加入状态的时候清除state
    state.value.pageState = null
  }

  const getLastPageState = () => {
    // 获取state数据
    let lastState = state.value.pageStateRouter
    if (!lastState) {
      state.value.pageState = null
      return null
    } else if (!lastState.next) {
      state.value.pageState = lastState
      state.value.pageStateRouter = null
      return lastState
    }
    let originState = state.value.pageStateRouter
    // 循环找出最底的next
    while (lastState.next) {
      lastState = lastState.next
    }
    state.value.pageState = lastState
    // 删除最底的next并且返回
    while (originState.next) {
      if (originState.next.next) {
        originState = originState.next
      } else {
        originState.next = null
      }
    }
    state.value.pageStateRouter = originState
    return lastState
  }

  const clearSelectedArea = () => {
    state.value.selectedArea = null
  }

  const clearProtectPointId = () => {
    state.value.selectedProtectPointId = null
  }

  const clearPageState = () => {
    state.value.pageState = null
    state.value.pageStateRouter = null
  }

  const getCityInfo = (code: string) => {
    const info = state.value.cityDicts.find((item) => item.ccode === code)
    return info
  }

  return {
    // state
    selectedArea,
    cityDicts,
    countyDicts,
    selectedProtectPointId,
    pageCurrent,
    pageState,
    // actions
    setSelectedArea,
    setProtectPointId,
    setAreaDicts,
    setAreaByCode,
    setPageCurrent,
    pageStatePush,
    // clear
    clearSelectedArea,
    clearProtectPointId,
    clearPageState,
    // get
    getLastPageState,
    getCityInfo
  }
})
