import { defineStore } from 'pinia'
// import { TreeData } from '@/apis/role/types'
interface TreeData {
  _id: string
  component: string
  icon: string
  is_system: string
  name: string
  pid: string
  uri: string
  key?: string
  path?: string
  type: number
  hide: number
  sort_no: number
  children?: TreeData[]
}
// type infoKey = ['userInfo', 'menuList']
interface infoData {
  userInfo: {
    nickname?: string
  }
  themeName?: ThemeType
  menuList: TreeData[]
}
type infoKey = keyof infoData

export const useInfoStore = defineStore('info', () => {
  const info: Ref<infoData> = ref({ userInfo: {}, menuList: [] })
  const themeName: Ref<ThemeType> = ref('light')
  const menuList = computed(() => info.value.menuList)
  const userInfo = computed(() => info.value.userInfo)
  const setValue = (key: infoKey, value) => {
    info.value[key] = value
  }
  return { menuList, userInfo, setValue, themeName }
})
