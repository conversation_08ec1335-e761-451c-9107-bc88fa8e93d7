import axios from 'axios'
import { Xhttp } from '@gt/mis-components-web/apis'

/**
 * 大华专用请求封装
 * @param url 请求路径（会自动拼接 /dahua-api 前缀）
 * @param data 请求参数
 * @param method 请求方法，默认为 POST
 * @returns Promise<any>
 */
export const dhRequest = async (
  url: string,
  data?: any,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'POST'
) => {
  try {
    const fullUrl = `/dahua-api${url}`
    const token = localStorage.getItem('dhToken') || ''

    const config = {
      headers: {
        'Content-Type': 'application/json',
        Authorization: token
      }
    }

    let response
    switch (method) {
      case 'GET':
        response = await axios.get(fullUrl, { ...config, params: data })
        break
      case 'POST':
        response = await axios.post(fullUrl, data, config)
        break
      case 'PUT':
        response = await axios.put(fullUrl, data, config)
        break
      case 'DELETE':
        response = await axios.delete(fullUrl, { ...config, data })
        break
      default:
        throw new Error(`不支持的请求方法: ${method}`)
    }

    if (response.data && (response.data.code == 0 || response.data.code == 1000)) {
      return response.data.data
    } else {
      throw new Error(response.data?.desc || '请求失败')
    }
  } catch (error) {
    console.error('大华API请求失败:', error)
    throw error
  }
}

/**
 * 大华技术开放平台视频监控API
 */
export default {
  /**
   * 监控平台token获取
   */
  async authToken() {
    // let token = localStorage.getItem('dhToken')
    return await Xhttp.post('/oauth/getToken')
  },

  /**
   * 获取设备列表
   */
  async getDeviceList(params) {
    return await dhRequest('/evo-apigw/evo-brm/1.2.1/device/subsystem/page', params)
  },

  /**
   * 获取实时视频流地址
   * @param channelId 视频通道编码，第一个$后数字代表通道类型，必须是1
   * @param dataType 视频类型：1=视频
   * @param streamType 码流类型：1=主码流，2=辅码流，3=辅码流2
   * @returns 返回RTSP地址和令牌
   */
  async getVideoList(channelId: string, dataType = '1', streamType = '1') {
    return await dhRequest('/evo-apigw/admin/API/MTS/Video/StartVideo', {
      data: {
        channelId,
        dataType,
        streamType
      }
    })
  },

  /**
   * 开始语音对讲
   * @param deviceCode 设备编码
   * @param channelSeq 通道序号
   * @param talkType 对讲类型：1=设备对讲，2=通道广播
   * @param talkMode 对讲模式：1=设备模式，2=客户端模式
   * @param audioBit 音频位数：8=8位，16=16位
   * @param audioType 音频类型：1=PCM，2=G711a，3=G711u，4=G726，5=AAC，6=MP2，7=MP3
   * @param sampleRate 采样率：8000=8K，16000=16K，32000=32K，48000=48K，64000=64K
   * @returns 返回对讲信息
   */
  async startVoiceTalk(params: {
    deviceCode: string
    channelSeq: string
    talkType?: string
    talkMode?: string
    audioBit?: string
    audioType?: string
    sampleRate?: string
  }) {
    const {
      deviceCode,
      channelSeq,
      talkType = '1',
      talkMode = '1',
      audioBit = '16',
      audioType = '2',
      sampleRate = '8000'
    } = params

    return await dhRequest('/evo-apigw/admin/API/MTS/Video/StartVoiceTalk', {
      data: {
        deviceCode,
        channelSeq,
        talkType,
        talkMode,
        audioBit,
        audioType,
        sampleRate
      }
    })
  }
}
