/// <reference types="vite/client" />

declare module '*.vue' {
  import { DefineComponent } from 'vue'
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 环境变量 TypeScript的智能提示
interface ImportMetaEnv {
  VITE_APP_TITLE: string
  VITE_APP_VERSION: string
  VITE_APP_URL: string
  VITE_APP_AXIOS_BASE_URL: string
  VITE_APP_LON_LAT_SERVE?: string
  VITE_APP_PHOTO: string
  VITE_APP_FOOTER: string
}
interface ImportMeta {
  readonly env: ImportMetaEnv
}
