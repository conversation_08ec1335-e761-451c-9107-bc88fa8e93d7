@font-face {
  font-family: 'mis-icon'; /* Project id 3340176 */
  src:
    url('iconfont.woff2?t=1688527972264') format('woff2'),
    url('iconfont.woff?t=1688527972264') format('woff'),
    url('iconfont.ttf?t=1688527972264') format('truetype');
}

.mis-icon {
  font-family: 'mis-icon' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.mis-icon-user:before {
  content: '\e682';
}

.mis-icon-dian:before {
  content: '\e62c';
}

.mis-icon-a-zu179:before {
  content: '\e602';
}

.mis-icon-a-zu129:before {
  content: '\e601';
}
