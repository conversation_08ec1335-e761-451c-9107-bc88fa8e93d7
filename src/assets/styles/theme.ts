const themes = {
  light: {
    colorPrimary: '#1677ff',
    colorLink: '#1677ff',
    colorLinkActive: '#1677ff',
    colorLinkHover: '#1677ff'
  },
  dark: {
    colorPrimary: '#44BC87',
    colorLink: '#44BC87',
    colorLinkActive: '#44BC87',
    colorLinkHover: '#44BC87'
  }
}
// 设置ant主题色
export const setTokenTheme = (themeName: ThemeType = 'light') => themes[themeName]

// 设置系统主题色
export const applyTheme = (themeName: ThemeType = 'light') => {
  const theme = themes[themeName]
  // console.log(' document.documentElement.style :>> ', document.documentElement.style)
  Object.keys(theme).forEach((key) => {
    document.documentElement.style.setProperty(`--${key}`, theme[key])
  })
}
