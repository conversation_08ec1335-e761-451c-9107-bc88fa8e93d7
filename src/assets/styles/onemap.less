.onemap-container {
  .ant-input-affix-wrapper:hover {
    border-color: #3bb8c1;
  }

  .ant-select-selector {
    border-radius: 0 !important;
    background-color: #00475e !important;
    border: 1px solid #3bb8c1 !important;
    color: #eafeff !important;
  }

  .ant-select-selection-placeholder {
    color: #6b8d90 !important;
  }

  .ant-select-arrow {
    color: #eafeff !important;
  }

  .ant-select-selection-item {
    color: #eafeff !important;
  }

  .ant-select-clear {
    background: transparent !important;
    color: #fff !important;
  }

  .ant-picker {
    border-radius: 0 !important;
    background-color: #00475e !important;
    border: 1px solid #3bb8c1 !important;
    color: #eafeff !important;

    &:hover {
      border-color: #3bb8c1 !important;
      box-shadow: 0 0 0 2px rgba(59, 184, 193, 0.2) !important;
    }

    &:focus,
    &.ant-picker-focused {
      border-color: #3bb8c1 !important;
      box-shadow: 0 0 0 2px rgba(59, 184, 193, 0.2) !important;
    }

    .ant-picker-input > input {
      color: #eafeff !important;
      background-color: transparent !important;

      &::placeholder {
        color: #6b8d90 !important;
      }
    }

    .ant-picker-separator {
      color: #6b8d90 !important;
    }

    .ant-picker-suffix {
      color: #eafeff !important;
    }

    .ant-picker-clear {
      background-color: transparent !important;
      color: #eafeff !important;

      &:hover {
        color: #3bb8c1 !important;
      }
    }
  }

  .ant-modal-content {
    background-color: rgba(0, 50, 67, 0.8) !important;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    box-shadow: inset 0 0 24px rgba(107, 246, 255, 0.5) !important;
    padding: 30px !important;
  }

  .ant-modal-close {
    color: #487b81 !important;
    top: 30px !important;
    right: 30px !important;
  }

  .ant-modal-close-x {
    font-size: 30px !important;
  }

  .ant-modal-header {
    background: transparent !important;
  }

  .ant-modal-title {
    font-size: 24px !important;
    color: #6bf6ff !important;
    position: relative;
    padding-left: 20px;

    &::after {
      content: '';
      width: 5px;
      height: 23px;
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      margin: auto 0;
      background-color: #6bf6ff;
    }
  }
}

.onemap-select-dropdown.ant-select-dropdown {
  width: 330px !important;
  background-color: #00475e !important;
  border-radius: 0 !important;
  border: 1px solid #3bb8c1 !important;
}

body .onemap-select-dropdown {
  .ant-select-item-option {
    color: #91b9bc !important;
    font-size: 14px !important;
    background-color: transparent !important;
    padding: 8px 12px !important;

    &:hover {
      background-color: rgba(59, 184, 193, 0.2) !important;
      color: #eafeff !important;
    }

    &:active {
      background-color: rgba(59, 184, 193, 0.3) !important;
    }
  }

  .ant-select-item-option-selected {
    color: #00475e !important;
    background-color: #3bb8c1 !important;
    font-weight: 500 !important;

    &:hover {
      background-color: #3bb8c1 !important;
      color: #00475e !important;
    }
  }

  .ant-select-item-group {
    color: #eafeff !important;
    font-size: 16px !important;
    font-weight: bold;
    background-color: rgba(0, 71, 94, 0.8) !important;
    padding: 8px 12px !important;
    border-bottom: 1px solid rgba(59, 184, 193, 0.3) !important;
  }

  .ant-select-item-empty {
    color: #91b9bc !important;
    text-align: center !important;
    padding: 16px !important;
  }

  .ant-select-dropdown-search {
    .ant-input {
      background-color: rgba(0, 71, 94, 0.5) !important;
      border: 1px solid #3bb8c1 !important;
      color: #eafeff !important;

      &::placeholder {
        color: #6b8d90 !important;
      }
    }
  }
}

body .onemap-modal {
  .ant-modal-content {
    background-color: rgba(0, 50, 67, 0.8) !important;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    box-shadow: inset 0 0 24px rgba(107, 246, 255, 0.5) !important;
    padding: 30px !important;
  }

  .ant-modal-close {
    color: #487b81 !important;
    top: 30px !important;
    right: 30px !important;
  }

  .ant-modal-close-x {
    font-size: 30px !important;
  }

  .ant-modal-header {
    background: transparent !important;
  }

  .ant-modal-title {
    font-size: 24px !important;
    color: #6bf6ff !important;
    position: relative;
    padding-left: 20px;

    &::after {
      content: '';
      width: 5px;
      height: 23px;
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      margin: auto 0;
      background-color: #6bf6ff;
    }
  }
}

body .onemap-picker-panel {
  .ant-picker-panel-container {
    background-color: #00475e !important;
    border: 1px solid #3bb8c1 !important;
    border-radius: 0 !important;
  }
  .ant-picker-cell-in-range::before {
    background: rgba(59, 184, 193, 0.5) !important;
  }

  .ant-picker-header {
    color: #eafeff !important;
    border-bottom: 1px solid rgba(59, 184, 193, 0.3) !important;

    .ant-picker-header-view {
      color: #eafeff !important;
      font-weight: 500 !important;

      &:hover {
        color: #3bb8c1 !important;
      }
    }

    .ant-picker-prev-icon,
    .ant-picker-next-icon,
    .ant-picker-super-prev-icon,
    .ant-picker-super-next-icon {
      color: #eafeff !important;

      &:hover {
        color: #3bb8c1 !important;
      }
    }
  }

  .ant-picker-content {
    th {
      color: #eafeff !important;
      font-weight: 500 !important;
    }
  }

  .ant-picker-cell {
    color: rgba(234, 254, 255, 0.6) !important;
  
    &:hover {
      .ant-picker-cell-inner {
      background-color: #3bb8c1 !important;
        color: #eafeff !important;
      }
    }

    &.ant-picker-cell-today {
      .ant-picker-cell-inner::before {
        border: 1px solid #3bb8c1 !important;
        color: #3bb8c1 !important;
      }
    }

    &.ant-picker-cell-selected {
      .ant-picker-cell-inner {
        background-color: #3bb8c1 !important;
        color: #00475e !important;
        font-weight: 500 !important;
      }
    }

    &.ant-picker-cell-range-start {
      .ant-picker-cell-inner {
        background-color: #3bb8c1 !important;
        color: #00475e !important;
        font-weight: 500 !important;
      }
    }
    &.ant-picker-cell-range-start::before {
      background: rgba(59, 184, 193, 0.5) !important;
    }
    &.ant-picker-cell-range-end {
      .ant-picker-cell-inner {
        background-color: #3bb8c1 !important;
        color: #00475e !important;
        font-weight: 500 !important;
      }
    }

    &.ant-picker-cell-range-end::before {
      background: rgba(59, 184, 193, 0.5) !important;
    }
    &.ant-picker-cell-in-range {
      .ant-picker-cell-inner {
        color: #eafeff !important;
      }
    }

    &.ant-picker-cell-disabled {
      .ant-picker-cell-inner {
        color: rgba(234, 254, 255, 0.3) !important;
        background-color: transparent !important;
      }
    }
  }

  .ant-picker-time-panel {
    background-color: #00475e !important;
    border-left: 1px solid rgba(59, 184, 193, 0.3) !important;

    .ant-picker-time-panel-column {
      .ant-picker-time-panel-cell {
        color: rgba(234, 254, 255, 0.6) !important;

        &:hover {
          background-color: rgba(59, 184, 193, 0.2) !important;
          color: #eafeff !important;
        }

        &.ant-picker-time-panel-cell-selected {
          background-color: #3bb8c1 !important;
          color: #00475e !important;
        }
      }
    }
  }

  .ant-picker-footer {
    border-top: 1px solid rgba(59, 184, 193, 0.3) !important;
    background-color: rgba(0, 71, 94, 0.5) !important;

    .ant-picker-today-btn {
      color: #3bb8c1 !important;

      &:hover {
        color: #eafeff !important;
      }
    }

    .ant-picker-now-btn {
      color: #3bb8c1 !important;

      &:hover {
        color: #eafeff !important;
      }
    }
  }

  .ant-picker-ranges {
    .ant-picker-preset {
      .ant-tag {
        background-color: rgba(59, 184, 193, 0.2) !important;
        border: 1px solid #3bb8c1 !important;
        color: #eafeff !important;

        &:hover {
          background-color: rgba(59, 184, 193, 0.3) !important;
        }

        &.ant-tag-blue {
          background-color: #3bb8c1 !important;
          color: #00475e !important;
        }
      }
    }
  }
}
