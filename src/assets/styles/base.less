* {
    list-style: none;

    &:hover {
        outline: none;
    }
}

@font-face {
    font-family: 'mis-icon';
    src: url('iconfont.woff2?t=1688527972264') format('woff2'),
        url('iconfont.woff?t=1688527972264') format('woff'),
        url('iconfont.ttf?t=1688527972264') format('truetype');
}

/* 设置iconfont标签font-family */
[class^="mis-icon-"],
[class*="mis-icon-"] {
    font-family: "iconfont";
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}


/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
    width: 0px;
    height: 0px;
    background-color: #F5F5F5;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
    border-radius: 10px;
    background-color: #F5F5F5;
}


/*去除虚线*/
input:focus,
select:focus,
textarea:focus,
button:focus {
    outline: none;
}

input {
    border: none
}

/*背景色*/
html,
body {
    height: 100%;
    width: 100%;
}


a {
    text-decoration: none;
    /*ios禁止页面在新窗口打开*/
    -webkit-touch-callout: none;
}

p,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
address,
caption,
cite,
code,
em,
strong,
table,
th,
td {
    font-style: normal;
    font-weight: normal;
    margin: 0;
    padding: 0;
}

strong,
.strong {
    font-weight: bold;
}

ul,
ol {
    list-style: none outside none;
}

fieldset,
img {
    border: medium none;
    vertical-align: middle;
}

caption,
th {
    text-align: left;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

* {
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

i,
cite,
em {
    font-style: normal;
}

input,
button,
select,
textarea {
    outline: none
}

a {
    color: #333;
    text-decoration: none;
    outline: medium none;
    cursor: pointer;

}

a:link,
a:visited,
a:active {
    text-decoration: none;
}


article,
aside,
dialog,
footer,
header,
section,
footer,
nav,
figure,
menu,
.dis_block {
    display: block;
}


.font-yahei {
    font-family: "Microsoft YaHei";
}

.font-SimSun {
    font-family: "SimSun";
}

.gt-table {

    .select-count,
    .genduo-box {
        color: var(--colorPrimary) !important;
    }
}

.ant-btn {
    svg {
        vertical-align: baseline;
    }
}

.permission-box {
    .operation-box {
        .ant-btn {
            color: var(--colorPrimary) !important;

        }
    }

}