<template>
  <div class="password-max">
    <NavBar class="navbar-box" />
    <div class="password-concent">
      <h3>当前密码使用时间过长，需修改密码后才能正常使用！</h3>
      <div class="form-max">
        <a-form
          ref="formRef"
          name="custom-validation"
          :model="formState"
          :rules="rules"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-form-item label="旧密码" name="oldPassword" required>
            <a-input-password
              allowClear
              v-model:value="formState.oldPassword"
              type="password"
              placeholder="请输入旧密码"
            />
          </a-form-item>
          <a-form-item label="新密码" name="newPassword" required>
            <a-input-password
              allowClear
              v-model:value="formState.newPassword"
              placeholder="请输入新密码"
              type="password"
            />
          </a-form-item>
          <a-form-item label="确认密码" name="confirmPassword" autocomplete="off" required>
            <a-input-password
              allowClear
              v-model:value="formState.confirmPassword"
              placeholder="请输入确认密码"
            />
          </a-form-item>
        </a-form>
        <a-button class="button-sub" type="primary" :loading="confirmLoading" @click="handlerOk"
          >提交</a-button
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'ResetPassword' })
import { useInfoStore } from '@/stores'
import { storeToRefs } from 'pinia'
import NavBar from '../Home/layout/NavBar.vue'
import type { Rule } from 'ant-design-vue/es/form'
import { userApi } from '@gt/mis-components-web/apis'
import { message } from 'ant-design-vue'
// import logout from '@/common/logout'
const { userInfo } = storeToRefs(useInfoStore())
const { setValue: setInfoStore } = useInfoStore()
const router = useRouter()
import { loginApi, formDataApi } from '@gt/mis-components-web/apis'
const formRef = ref()
const confirmLoading = ref(false)
const formState = reactive({
  confirmPassword: '',
  newPassword: '',
  oldPassword: ''
})
const validatePass = async (_rule: Rule, value: string) => {
  if (value === '') {
    return Promise.reject('请输入确认密码')
  } else if (value !== formState.newPassword) {
    return Promise.reject('两次输入密码不一致')
  } else {
    return Promise.resolve()
  }
}
const rules = {
  newPassword: [
    {
      trigger: 'blur',
      required: true,
      message: '请输入新密码'
    }
    // {
    //   trigger: 'blur',
    //   pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&+=.])(?=.*[^\w\d\s])\S{10,}$/,
    //   message: '必须包含大写字母、小写字母、数字和特殊字符，不少于10位'
    // }
  ],
  confirmPassword: [{ required: true, validator: validatePass, trigger: 'change' }]
}
const handlerOk = () => {
  formRef.value.validate().then(async () => {
    try {
      confirmLoading.value = true
      await userApi.userModifyPassword(formState)
      message.success('修改成功！')
      router.replace('/')
      // logout()
    } catch (error) {
      message.error(error.msg)
    } finally {
      confirmLoading.value = false
    }
  })
}
onMounted(async () => {
  const user_id = localStorage.getItem('user_id')
  if (!user_id) {
    router.push('/login')
    return
  }
  if (Object.keys(userInfo.value).length === 0) {
    let data = await loginApi.getUserInformation()
    if (data.resetPassword === false) {
      router.replace('/')
    }
    if (user_id) {
      try {
        const { list } = await formDataApi.searchFormData('tb_user_info_map', {
          filter: ['=', 'user_id', user_id]
        })
        if (list.length > 0) {
          data = Object.assign({}, list[0], data)
        }
      } catch (error) {
        console.log('用户信息映射表-error :>> ', error)
      }
    }
    setInfoStore('userInfo', data)
    localStorage.setItem('userInfo', JSON.stringify(data))
  }
})
</script>

<style lang="less" scoped>
.password-max {
  height: 100%;
  display: flex;
  flex-direction: column;
  .navbar-box {
    flex-shrink: 0;
  }
  .password-concent {
    flex: 1;
    display: flex;
    flex-direction: column;
    // align-items: center;
    // justify-content: center;
    padding-top: 15vh;
    > h3 {
      width: 100%;
      text-align: center;
      padding: 20px 0;
    }
    .form-max {
      width: 500px;
      margin: 0 auto;
      padding: 10px 0;
      .button-sub {
        margin: 10px auto;
        display: flex;
      }
    }
  }
}
</style>
