<template>
  <MenuManage :menuComponents="getMenuComponents()" />
</template>

<script lang="ts" setup>
import { MenuManage } from '@gt/mis-components-web/views'
const getMenuComponents = (): any[] => {
  const allPathArray = getComponent(['app', 'views', 'pages'])
  const pathArray: any[] = []
  allPathArray.forEach((pathArr) => {
    let current = pathArray
    pathArr.forEach((path, index) => {
      const isExist = current.find((item) => item.value === path)
      if (isExist) {
        current = isExist.children || (isExist.children = [])
      } else {
        const obj: any = { value: path, label: path }
        current.push(obj)
        current = obj.children || (obj.children = [])
        if (index === pathArr.length - 1) {
          delete obj.children
        }
      }
    })
  })
  return pathArray
}
function getComponent(rootPath) {
  const files = import.meta.glob('/src/**/*.vue')
  const arr = Object.keys(files)
    .map((item) => {
      const newArr = item.replace('./', '').split('/')
      newArr.splice(0, 2)
      return newArr
    })
    .filter((item) => {
      return rootPath.includes(item[0]) && item.length > 1 && !item.includes('components')
    })
  return arr
}
</script>

<style lang="less" scoped></style>
