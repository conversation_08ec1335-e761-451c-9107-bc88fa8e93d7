<template>
  <div class="login-page">
    <div class="page-body">
      <div class="system-description">
        <div class="logo">
          <span class="title">{{ title }}</span>
        </div>
        <!-- <div class="description">
          {{ description }}
        </div> -->
      </div>
      <div class="login-form">
        <div class="left">
          <img src="~@/assets/images/login_image.png" alt="" />
        </div>
        <div class="right">
          <component :is="isComponents[status]" v-model:status="status" />
        </div>
      </div>
    </div>
    <div class="footer">
      {{ footer }}
    </div>
  </div>
</template>

<script setup lang="ts" name="Login">
import { loginApi } from '@gt/mis-components-web/apis'
import { message } from 'ant-design-vue'
import { STATUS } from './types'
import Signing from './components/Signing/index.vue'
import Register from './components/Register/index.vue'
import ResetPasswd from './components/ResetPasswd/index.vue'
const title = ref(import.meta.env.VITE_APP_TITLE)
const footer = ref(import.meta.env.VITE_APP_FOOTER)
const status = ref<STATUS>(STATUS.SIGNING)
const isComponents = readonly({
  Signing: shallowRef(Signing),
  Register: shallowRef(Register),
  ResetPasswd: shallowRef(ResetPasswd)
})
const router = useRouter()
const userInfo = JSON.parse(localStorage.getItem('userInfo') as string)
const checkLogin = async () => {
  if (localStorage.getItem('refresh_token')) {
    try {
      const data = await loginApi.refreshToken()
      const { access_token, refresh_token, user_id, token_type } = data
      localStorage.setItem('access_token', `${token_type} ${access_token}`)
      localStorage.setItem('refresh_token', refresh_token)
      localStorage.setItem('user_id', user_id)
      let hour = new Date().getHours()
      let nickname = userInfo.nickname
      let msg = ''
      if (hour >= 22 || hour <= 6) {
        msg += `欢迎您，${nickname}！夜深了，注意休息哦~`
      } else if (hour <= 12) {
        msg += `尊敬的${nickname}，上午好！`
      } else if (hour <= 18) {
        msg += `尊敬的${nickname}，下午好！`
      } else if (hour <= 22) {
        msg += `尊敬的${nickname}，晚上好！`
      }
      message.success(msg)
      router.push('/')
    } catch (error) {
      localStorage.clear()
    }
  } else {
    localStorage.clear()
  }
}
onMounted(() => {
  checkLogin()
})
</script>

<style scoped lang="less">
.login-page {
  padding-top: calc(50vh - 320px);
  height: 100%;
  //background: #f0f2f5 url('~@/assets/images/background.png');
  background-repeat: repeat;
  background-color: #333e37;
  background-position: bottom;

  .page-body {
    margin: 0 auto;
  }

  .footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    line-height: 64px;
    font-size: 16px;
    color: #ffffff;
    text-align: center;
  }

  .system-description {
    text-align: center;
    color: #fff;
    margin-bottom: 20px;

    .logo {
      display: flex;
      justify-content: center;
      align-items: center;

      .title {
        font-size: 60px;
        font-weight: 600;
        padding: 0 0.5rem;
      }
    }

    .description {
      font-size: 1.2rem;
      margin: 12px 0 32px 0;
    }
  }

  .login-form {
    display: flex;
    width: 800px;
    height: 420px;
    margin: 0 auto;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0 10px #35363a;
    .left,
    .right {
      flex: 1;
      height: 100%;
    }

    .left {
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}
</style>
