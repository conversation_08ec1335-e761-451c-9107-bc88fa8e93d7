<template>
  <div class="login-wrapper">
    <div class="title">重置密码</div>
    <div class="body">
      <a-form :model="resetInfo" ref="resetPwdRef" :rules="resetRules">
        <a-form-item name="phone">
          <a-input
            v-model:value="resetInfo.phone"
            class="input-item"
            allowClear
            placeholder="手机号码"
          >
            <template #prefix>
              <svg-icon icon="PhoneOutlined" />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item name="verifyCode">
          <a-input
            ref="verifyInputRef"
            v-model:value="resetInfo.verifyCode"
            class="input-item verify-code"
            placeholder="验证码"
          >
            <template #prefix>
              <svg-icon icon="NumberOutlined" />
            </template>
          </a-input>
          <a-button
            class="btn-verify-code"
            type="link"
            @click="verifyCodeHandler"
            :disabled="data.verifyCodeDisabled"
            >{{ data.getVerifyCodeText }}</a-button
          >
        </a-form-item>
        <a-form-item name="password">
          <a-input
            v-model:value="resetInfo.password"
            type="password"
            allowClear
            class="input-item"
            placeholder="密码6到16位，包含数字和大小写字母"
          >
            <template #prefix>
              <svg-icon icon="LockOutlined" />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item name="confirmPassword">
          <a-input
            type="password"
            v-model:value="resetInfo.confirmPassword"
            class="input-item"
            allowClear
            placeholder="确认密码"
          >
            <template #prefix>
              <svg-icon icon="LockOutlined" />
            </template>
          </a-input>
        </a-form-item>
        <div class="submit">
          <a-button
            type="primary"
            block
            @click="submitResetForm"
            class="input-item"
            :loading="loading"
          >
            确定
          </a-button>
        </div>
        <div class="link-wrapper">
          <a-button
            class="btn-forget-passwd"
            type="link"
            @click="() => emit('update:status', STATUS.SIGNING)"
            >立即登录</a-button
          >
        </div>
      </a-form>
      <a-modal
        class="img-verify-code-wrapper"
        v-model:open="verifyOpen"
        :footer="null"
        @cancel="handleCancel"
      >
        <div class="img-verify-code-content">
          <h3>请输入验证码</h3>
          <div class="img-wrapper">
            <img class="img-verify-code" :style="{ 'background-image': `url(${verifyImg})` }" />
            <a-button class="btn-change-code" type="link" @click="getImageCode"
              >看不清，换一个</a-button
            >
          </div>
          <a-form ref="codeFormRef" :model="imgVerify" :rules="verifyCodeRules">
            <a-form-item class="input-img-code" name="imgVerifyCode">
              <a-input
                ref="verifyInputRef"
                v-model:value="imgVerify.imgVerifyCode"
                :maxLength="4"
                :allowClear="true"
                placeholder="验证码"
              >
              </a-input>
            </a-form-item>
            <a-button
              type="primary"
              block
              @click="submitImageCode"
              class="input-item"
              :loading="verifyCodeLoading"
            >
              确定
            </a-button>
          </a-form>
        </div>
      </a-modal>
    </div>
  </div>
</template>

<script setup lang="ts">
import { STATUS } from '../../types'
import { type FormInstance, message } from 'ant-design-vue'
import { loginApi, smsApi } from '@gt/mis-components-web/apis'

interface ResetInfo {
  phone: string
  verifyCode: string
  password: string
  confirmPassword: string
}
const emit = defineEmits(['update:status'])
const loading = ref<Boolean>(false)
const resetInfo = ref<ResetInfo>({
  phone: '',
  verifyCode: '',
  password: '',
  confirmPassword: ''
})

const confirmValidator = async (_rule, value: string) => {
  if (value !== resetInfo.value.password) {
    return Promise.reject('两次输入的密码不一致')
  }
  return Promise.resolve()
}
const resetRules = readonly({
  phone: [
    {
      trigger: 'blur',
      required: true,
      message: '请输入手机号码'
    },
    {
      trigger: 'blur',
      pattern:
        /^1(3[0-9]|4[5,7]|5[0,1,2,3,5,6,7,8,9]|6[2,5,6,7]|7[0,1,2,3,5,6,7,8]|8[0-9]|9[1,5,8,9])\d{8}$/,
      message: '请输入正确的手机号码'
    }
  ],
  verifyCode: [
    {
      trigger: 'blur',
      required: true,
      message: '请输入验证码'
    }
  ],
  password: [
    {
      trigger: 'blur',
      required: true,
      message: '请输入密码'
    },
    {
      trigger: 'blur',
      pattern: /^.*(?=.{6,16})(?=.*\d)(?=.*[A-Z])(?=.*[a-z]).*$/,
      message: '密码6到16位，包含数字和大小写字母'
    }
  ],
  confirmPassword: [
    {
      trigger: 'blur',
      required: true,
      message: '请输再次输入密码'
    },
    {
      trigger: 'blur',
      pattern: /^.*(?=.{6,16})(?=.*\d)(?=.*[A-Z])(?=.*[a-z]).*$/,
      message: '密码6到16位，包含数字和大小写字母'
    },
    {
      trigger: 'blur',
      validator: confirmValidator
    }
  ]
})

const resetPwdRef = ref<FormInstance>()
const codeFormRef = ref<FormInstance>()
const verifyInputRef = ref<HTMLElement>()
const verifyCodeLoading = ref<Boolean>(false)
const verifyOpen = ref<Boolean>(false)
const verifyKey = ref<string>('')
const verifyImg = ref<string>('')
const WAIT_SECONDS = 180
const data = reactive({
  loading: false,
  verifyCodeDisabled: false,
  modelVisible: false,
  getVerifyCodeText: '获取验证码'
})
const imgVerify = reactive({
  imgVerifyCode: ''
})
const verifyCodeRules = readonly({
  imgVerifyCode: [
    {
      required: true,
      message: '请输入验证码'
    }
  ]
})

const getImageCode = async () => {
  const res = await smsApi.getImageCode()
  if (!res) return
  verifyKey.value = res.imgKey
  verifyImg.value = res.base64
}

const handleCancel = () => {
  verifyOpen.value = false
}

const verifyCodeHandler = () => {
  resetPwdRef.value?.validateFields('phone').then(async () => {
    verifyOpen.value = true
    getImageCode()
    nextTick(() => {
      verifyInputRef.value?.focus()
    })
  })
}

const submitImageCode = () => {
  codeFormRef.value?.validateFields().then(
    async () => {
      verifyCodeLoading.value = true
      try {
        await smsApi.getResetPasswdSmsCode(
          resetInfo.value.phone,
          verifyKey.value,
          imgVerify.imgVerifyCode
        )
        message.success('短信验证码已发送')
        verifyOpen.value = false
        disableVerify()
      } catch (err) {
        message.error(`验证码错误：${err.msg || '未知错误'}`)
        getImageCode()
        imgVerify.imgVerifyCode = ''
        verifyInputRef.value?.focus()
      } finally {
        verifyCodeLoading.value = false
      }
    },
    (err) => {
      message.error(JSON.stringify(err))
    }
  )
}

const disableVerify = () => {
  imgVerify.imgVerifyCode = ''
  data.verifyCodeDisabled = true
  let seconds = WAIT_SECONDS
  const interval = setInterval(() => {
    data.getVerifyCodeText = `${seconds}秒后重新获取`
    seconds -= 1
    if (seconds === 0) {
      data.getVerifyCodeText = '获取验证码'
      data.verifyCodeDisabled = false
      clearInterval(interval)
    }
  }, 1000)
}

const submitResetForm = () => {
  resetPwdRef.value?.validate().then(async (obj: ResetInfo) => {
    loading.value = true
    const { phone, verifyCode, password } = obj
    try {
      const res = await loginApi.resetPassword(phone, verifyCode, password)
      if (!res) return
      message.success('重置密码成功')
      resetInfo.value = {
        phone: '',
        verifyCode: '',
        password: '',
        confirmPassword: ''
      }
    } catch (error) {
      message.error(error.msg)
    } finally {
      loading.value = false
    }
  })
}
</script>

<style lang="less" scoped>
.login-wrapper {
  display: flex;
  flex-direction: column;
  padding: 1.5rem 1rem;

  .title {
    flex-basis: 60px;
    line-height: 60px;
    font-size: 24px;
    text-align: center;
  }

  .body {
    flex: 1;
    padding: 0 2rem;

    .ant-form {
      .ant-form-item {
        margin-bottom: 19px;
      }

      .ant-form-item-with-help {
        margin-bottom: 0;
      }
    }

    .ant-btn-primary {
      background-color: var(--colorPrimary);
      border: var(--colorPrimary);
      margin-top: 4px;

      &:hover {
        background-color: var(--colorPrimary);
        border: var(--colorPrimary);
      }
    }

    .btn-verify-code {
      position: absolute;
      right: 0;
      line-height: 32px;
      padding-top: 0px;
      padding-bottom: 0px;
    }

    .link-wrapper {
      padding: 6px 0;

      .ant-btn-link {
        padding: 0;
      }

      .btn-forget-passwd {
        float: right;
      }
    }
  }
}
</style>
