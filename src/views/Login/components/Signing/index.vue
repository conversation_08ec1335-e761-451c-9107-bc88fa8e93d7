<template>
  <div class="login-wrapper">
    <div class="title">欢迎登录</div>
    <div class="body">
      <div class="model_box" v-show="false">
        <div
          :class="['model_item', 'left', { active: loginMode === LoginMode.USERNAME }]"
          @click="loginMode = LoginMode.USERNAME"
        >
          用户名登录
        </div>
        <div
          :class="['model_item', { active: loginMode === LoginMode.PHONE }]"
          @click="loginMode = LoginMode.PHONE"
        >
          手机号登录
        </div>
      </div>
      <a-form autocomplete="off" :model="loginInfo" :rules="rules" ref="loginRef">
        <a-form-item name="username" v-if="loginMode === 'username'">
          <a-input
            v-model:value="loginInfo.username"
            class="input-item"
            placeholder="用户名"
            allowClear
          >
            <template #prefix>
              <svg-icon icon="UserOutlined" />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item name="phone" v-if="loginMode === 'phone'">
          <a-input
            v-model:value="loginInfo.phone"
            class="input-phone"
            allClear
            placeholder="请输入手机号"
          >
            <template #prefix>
              <svg-icon icon="PhoneOutlined" />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item name="password">
          <a-input
            type="password"
            v-model:value="loginInfo.password"
            class="input-item"
            placeholder="密码"
            allowClear
            @keydown.enter="submitLoginForm"
          >
            <template #prefix>
              <svg-icon icon="LockOutlined" />
            </template>
          </a-input>
        </a-form-item>
      </a-form>
      <div class="submit">
        <a-button
          type="primary"
          block
          @click="submitLoginForm"
          class="input-item"
          :loading="loading"
        >
          登录
        </a-button>
      </div>
      <div class="link-wrapper" v-show="false">
        <a-button type="link" @click="emit('update:status', STATUS.REGISTER)">注册账号</a-button>
        <a-button
          class="btn-forget-passwd"
          type="link"
          @click="emit('update:status', STATUS.RESET_PASSWD)"
          >忘记密码</a-button
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="Signing">
import { STATUS } from '../../types'
import { loginApi } from '@gt/mis-components-web/apis'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import { LoginMode, loginModeJson } from './config'
import { cloneDeep } from 'lodash'

const emit = defineEmits(['update:status'])
const router = useRouter()
const loginRef = ref<FormInstance>()
const loading = ref(false)
const loginMode = ref<LoginMode>(LoginMode.USERNAME)
const loginInfo = ref()
const rules = ref()

watch(
  loginMode,
  (newVal) => {
    // let password = ''
    // if (loginInfo.value?.password) {
    //   password = loginInfo.value?.password
    // }
    loginInfo.value = cloneDeep(loginModeJson[newVal].formData)
    // loginInfo.value.password = password
    // loginInfo.value.password = idPassword
    rules.value = loginModeJson[newVal].rules
  },
  {
    immediate: true
  }
)

const login = async () => {
  try {
    loading.value = true
    const { username, password } = loginInfo.value
    let data = await loginApi.login(username, password)
    const { access_token, refresh_token, user_id, token_type } = data
    localStorage.setItem('access_token', `${token_type} ${access_token}`)
    localStorage.setItem('refresh_token', refresh_token)
    localStorage.setItem('user_id', user_id)
    // await getUserInfo()
    router.push('/')
  } catch (error) {
    message.error(error.msg)
  } finally {
    loading.value = false
  }
}

const submitLoginForm = () => {
  // console.log('router :>> ', router)
  loginRef!.value
    ?.validate()
    .then(() => {
      login()
    })
    .catch((error) => {
      console.log('error :>> ', error)
    })
}
</script>

<style lang="less" scoped>
.login-wrapper {
  display: flex;
  flex-direction: column;
  padding: 3rem 1rem;

  .title {
    flex-basis: 60px;
    line-height: 60px;
    font-size: 24px;
    text-align: center;
  }
  .model_box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
    font-size: 18px;
    .model_item {
      width: 50%;
      text-align: center;
      cursor: pointer;
    }
    .model_item.left {
      border-right: 1px solid #ccc;
    }
    .model_item.active {
      color: #5faeff;
    }
  }

  .body {
    flex: 1;
    padding: 1rem 2rem 2rem;
  }

  .ant-btn-primary {
    background-color: var(--colorPrimary);
    border: var(--colorPrimary);

    &:hover {
      background-color: var(--colorPrimary);
      border: var(--colorPrimary);
    }
  }

  .link-wrapper {
    padding: 6px 0;

    .ant-btn-link {
      padding: 0;
    }

    .btn-forget-passwd {
      float: right;
    }
  }
}
</style>
