export enum LoginMode {
  PHONE = 'phone',
  USERNAME = 'username'
}
export const loginModeJson = {
  phone: {
    rules: {
      phone: [
        {
          required: true,
          message: '请输入用户名'
        },
        {
          trigger: 'blur',
          pattern:
            /^1(3[0-9]|4[5,7]|5[0,1,2,3,5,6,7,8,9]|6[2,5,6,7]|7[0,1,2,3,5,6,7,8]|8[0-9]|9[1,5,8,9])\d{8}$/,
          message: '请输入正确的手机号码'
        }
      ],
      password: [
        {
          required: true,
          message: '请输入密码'
        }
      ]
    },
    formData: {
      phone: '',
      password: ''
    }
  },
  username: {
    rules: {
      username: [
        {
          required: true,
          message: '请输入用户名'
        }
      ],
      password: [
        {
          required: true,
          message: '请输入密码'
        }
      ]
    },
    formData: {
      username: '',
      password: ''
    }
  }
}
