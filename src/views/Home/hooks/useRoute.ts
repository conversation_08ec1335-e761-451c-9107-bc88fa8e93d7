// export const addRoutes = () => {}
import { roleApi } from '@gt/mis-components-web/apis'
// import { TreeData } from '@/apis/role/types'
interface TreeData {
  _id: string
  component: string
  icon: string
  is_system: string
  name: string
  pid: string
  uri: string
  key?: string
  path?: string
  type: number
  hide: number
  sort_no: number
  children?: TreeData[]
}
interface GetMenuCallBack {
  menuList: TreeData[]
  routes: TreeData[]
}
interface GetMenu {
  (list: TreeData[]): GetMenuCallBack | undefined
}
interface GetUrl {
  (url: string): {
    url: string
    query: {}
  }
}
// const _import = (file: String) => () => import(`@/${file}.vue`)
const modules = import.meta.glob(['@/views/**/*.vue', '@/app/views/**/*.vue'])
// 获取菜单列表和路由值
export const getMenuData = async (): Promise<GetMenuCallBack | undefined> => {
  const res = (await roleApi.queryAuthorizeTreeMenus()) as TreeData[]
  // console.log('res :>> ', res)
  // res.unshift({
  //   name: '测试页面',
  //   pid: '-1',
  //   uri: '/home',
  //   component: 'views/Home/index.vue',
  //   icon: 'role',
  //   hide: 1,
  //   type: 0,
  //   _id: '3654a793be0c4aa897824a3e35a4b183111',
  //   sort_no: 1,
  //   is_system: ''
  // })
  if (!isHideTree) {
    return
  }
  return genMenuAndRoutes(res)
}
// 判断是否有菜单和显示菜单是否为0
const isHideTree = (list: TreeData[]): boolean => {
  if (!Array.isArray(list) || list.length === 0) {
    return true
  }
  return list.every((item) => item.hide === 0)
}
// 解析后端接口
const genMenuAndRoutes: GetMenu = (list) => {
  if (!list || !Array.isArray(list)) return
  const menuList: TreeData[] = []
  const routes: TreeData[] = []
  for (let i = 0; i < list.length; i++) {
    const { _id, children, uri } = list[i]
    const { url, query } = getUrlParams(uri)
    const route = {
      key: uri || _id,
      path: url,
      query,
      ...list[i]
    }
    if (children) {
      const result = genMenuAndRoutes(children)
      routes.push(...result!.routes)
      route.children = result!.menuList
    } else {
      routes.push(route)
    }
    if (route.hide === 1) menuList.push(route)
  }
  // 排序
  menuList.sort((a, b) => a.sort_no - b.sort_no)
  return {
    menuList,
    routes
  }
}
// 解析路径中带有的query
const getUrlParams: GetUrl = (url) => {
  const newUrl = {
    url,
    query: {}
  }
  const index = url.indexOf('?')
  if (index != -1) {
    newUrl['url'] = `${url.substring(0, index)}`
    const searchParams = url.substring(index + 1)
    const searchPar = new URLSearchParams(searchParams)
    for (const [key, value] of searchPar.entries()) {
      newUrl['query'][key] = value
    }
  }
  return newUrl
}
// 增加菜单到系统router路由中
export const addRoutes = (routes: TreeData[], router) => {
  // console.log('modules :>> ', modules)
  if (!routes || !Array.isArray(routes)) return
  routes.forEach((route) => {
    // route.component = 'views/UserManage/index.vue'
    // console.log('object :>> ', route.component, modules)
    router.addRoute('Home', {
      path: route.path!,
      meta: { title: route.name },
      component: modules[`/src/${route.component}`]
    })
  })
}
// 判断登录后跳转到第一个页面
export const getFirstVisibleMenu = (menuList: TreeData[]) => {
  if (!menuList) return
  for (const menu of menuList) {
    const { hide, children } = menu
    if (hide) {
      if (Array.isArray(children) && children.length >= 0) {
        return getFirstVisibleMenu(children)
      } else {
        return menu
      }
    }
  }
}
