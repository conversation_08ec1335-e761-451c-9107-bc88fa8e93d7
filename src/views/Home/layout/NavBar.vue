<template>
  <div class="navbar-box">
    <div class="left-box">
      <img src="~@/assets/images/logo.png" alt="" />
      <h3>
        {{ title }}
        <p>{{ version }}</p>
      </h3>
    </div>
    <div class="right-box">
      <!-- <SvgIcon icon="DownOutlined" class="icon-box" /> -->
      <!-- <SvgIcon icon="mis-icon-dian" class="icon-box"></SvgIcon> -->
      <a-dropdown>
        <a class="ant-dropdown-link" @click.prevent v-show="userInfo?.nickname">
          <p>{{ userInfo?.nickname }}</p>
          <SvgIcon icon="DownOutlined" class="icon-box" />
        </a>
        <template #overlay>
          <a-menu @click="onClick">
            <a-menu-item key="password">修改密码</a-menu-item>
            <a-menu-item key="logout">退出</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    <PasswordModify v-model:open="isPassword" v-if="isPassword" />
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'NavBar' })
import { useInfoStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { Modal } from 'ant-design-vue'
import logout from '@/common/logout'
import PasswordModify from './PasswordModify.vue'
const title = import.meta.env.VITE_APP_TITLE
const version = import.meta.env.VITE_APP_VERSION
const { userInfo } = storeToRefs(useInfoStore())
const isPassword = ref<boolean>(false)
// console.log('counterStore :>> ', userInfo)
const onClick = ({ key }) => {
  // console.log('key :>> ', key)
  if (key === 'logout') {
    Modal.confirm({
      content: '是否退出当前账号？',
      onOk() {
        logout()
      },
      onCancel() {
        Modal.destroyAll()
      }
    })
  } else if (key === 'password') {
    isPassword.value = true
  }
}
</script>

<style lang="less" scoped>
.navbar-box {
  height: 80px;
  line-height: 80px;
  display: flex;
  justify-content: space-between;
  background-color: var(--colorPrimary);
  padding: 0 20px;
  .left-box {
    display: flex;
    align-items: center;
    img {
      width: 44px;
      height: 44px;
    }
    h3 {
      color: #fff;
      padding: 0 10px;
      font-size: 2rem;
      font-weight: bold;
      margin: 0;
      p {
        display: inline;
        font-size: 14px;
        font-weight: normal;
      }
    }
  }
  .right-box {
    :deep(.ant-dropdown-link) {
      display: flex;
      align-items: center;
      color: #fff;
      cursor: pointer;
      > p {
        font-size: 20px;
      }
      .icon-box {
        font-size: 16px;
        margin-left: 10px;
      }
    }
  }
}
</style>
