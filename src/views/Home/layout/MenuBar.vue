<template>
  <div class="menu-box" :class="{ 'menu-inline': !state.collapsed }">
    <a-menu
      v-model:selectedKeys="state.selectedKeys"
      mode="inline"
      :inline-collapsed="state.collapsed"
      @click="menuClick"
    >
      <template v-for="item in menuList" :key="item.key">
        <a-menu-item v-if="!item.children" :key="item.key">
          <template #icon>
            <SvgIcon :icon="item.icon" />
          </template>
          <span>{{ item.name }}</span>
        </a-menu-item>
        <SubMenu :item="item" v-else />
      </template>
    </a-menu>
    <div class="menu-handler" @click="toggleCollapsed">
      <p v-show="!state.collapsed">收起菜单</p>
      <p v-show="state.collapsed">>></p>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'MenuBar' })
import { useInfoStore } from '@/stores'
import { storeToRefs } from 'pinia'
import SubMenu from './SubMenu.vue'
const { menuList } = storeToRefs(useInfoStore())
interface FormatRoute {
  (url: string): {
    path: string
    query?: {}
  }
}
// const route = useRoute()
const router = useRouter()
const state = reactive({
  collapsed: false,
  selectedKeys: [] as string[]
})
// state.selectedKeys = ['/app/data/history']
const menuClick = ({ key }) => {
  let { path, query } = formatRoute(key)
  query = query || {}
  router.push({ path, query })
  state.selectedKeys = [key]
  // console.log('route :>> ', route.query)
}
const toggleCollapsed = () => {
  state.collapsed = !state.collapsed
}
const formatRoute: FormatRoute = (url) => {
  if (url && url.includes('?')) {
    const path = url.split('?')[0]
    let query = {}
    let str = url.split('?')[1].split('&')
    for (let i = 0; i < str.length; i++) {
      let a = str[i].split('=')
      query[a[0]] = a[1]
    }
    return { path, query }
  } else {
    return { path: url }
  }
}
defineExpose({ menuClick })
</script>

<style lang="less" scoped>
.menu-box {
  width: auto !important;
  flex-shrink: 0;
  height: 100%;
  // background: #f00;
  overflow: hidden;
  background-color: #fff;
  &.menu-inline {
    width: 260px !important;
  }
  :deep(.ant-menu) {
    height: calc(100% - 30px);
    overflow: hidden;
    overflow-y: auto;
  }
  .menu-handler {
    height: 30px;
    flex-shrink: 0;
    text-align: center;
    cursor: pointer;
  }
}
</style>
