<template>
  <div class="password-modify">
    <a-modal
      destroyOnClose
      :maskClosable="false"
      getContainer=".password-modify"
      class="modify-model"
      v-model:open="openState"
      title="修改密码"
      style="width: 500px"
      :confirmLoading="confirmLoading"
      @ok="handlerOk"
    >
      <a-form
        ref="formRef"
        name="custom-validation"
        :model="formState"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="旧密码" name="oldPassword" required>
          <a-input-password
            allowClear
            v-model:value="formState.oldPassword"
            type="password"
            placeholder="请输入旧密码"
          />
        </a-form-item>
        <a-form-item label="新密码" name="newPassword" required>
          <a-input-password
            allowClear
            v-model:value="formState.newPassword"
            placeholder="请输入新密码"
            type="password"
          />
        </a-form-item>
        <a-form-item label="确认密码" name="confirmPassword" autocomplete="off" required>
          <a-input-password
            allowClear
            v-model:value="formState.confirmPassword"
            placeholder="请输入确认密码"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'PasswordModify' })
import type { Rule } from 'ant-design-vue/es/form'
import { userApi } from '@gt/mis-components-web/apis'
import { message } from 'ant-design-vue'
import logout from '@/common/logout'
const openState = defineModel('open')
const formRef = ref()
const confirmLoading = ref(false)
const formState = reactive({
  confirmPassword: '',
  newPassword: '',
  oldPassword: ''
})
const validatePass = async (_rule: Rule, value: string) => {
  if (value === '') {
    return Promise.reject('请输入确认密码')
  } else if (value !== formState.newPassword) {
    return Promise.reject('两次输入密码不一致')
  } else {
    return Promise.resolve()
  }
}
const rules = {
  newPassword: [
    {
      trigger: 'blur',
      required: true,
      message: '请输入新密码'
    }
    // {
    //   trigger: 'blur',
    //   pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&+=.])(?=.*[^\w\d\s])\S{10,}$/,
    //   message: '必须包含大写字母、小写字母、数字和特殊字符，不少于10位'
    // }
  ],
  confirmPassword: [{ required: true, validator: validatePass, trigger: 'change' }]
}
const handlerOk = () => {
  formRef.value.validate().then(async () => {
    try {
      confirmLoading.value = true
      await userApi.userModifyPassword(formState)
      message.success('修改成功！')
      logout()
      openState.value = false
    } catch (error) {
      message.error(error.msg)
    } finally {
      confirmLoading.value = false
    }
  })
}
</script>

<style lang="less" scoped>
.password-modify {
  position: absolute;
  // :deep(.modify-model) {
  // }
}
</style>
