<template>
  <a-sub-menu :key="item.key">
    <template #icon>
      <SvgIcon :icon="item.icon" />
    </template>
    <template #title>{{ item.name }}</template>
    <template v-for="itemChildren in item.children" :key="itemChildren.key">
      <a-menu-item v-if="!itemChildren.children" :key="itemChildren.key">
        <template #icon>
          <SvgIcon :icon="itemChildren.icon" />
        </template>
        {{ itemChildren.name }}</a-menu-item
      >
      <SubMenu v-else :item="itemChildren" />
    </template>
  </a-sub-menu>
</template>

<script lang="ts" setup>
// import { TreeData } from '@/apis/role/types'
defineProps<{
  item: TreeData
}>()
interface TreeData {
  _id: string
  component: string
  icon: string
  is_system: string
  name: string
  pid: string
  uri: string
  key?: string
  path?: string
  type: number
  hide: number
  sort_no: number
  children?: TreeData[]
}
</script>

<style lang="less" scoped></style>
