<template>
  <a-config-provider :locale="locale" :theme="theme">
    <RouterView />
  </a-config-provider>
</template>
<script setup lang="ts">
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import { applyTheme, setTokenTheme } from '@/assets/styles/theme'
import { useInfoStore } from '@/stores'
import { storeToRefs } from 'pinia'
const { themeName } = storeToRefs(useInfoStore())
const theme = ref({
  token: setTokenTheme(themeName.value)
})
watch(
  () => themeName.value,
  (newVal) => {
    theme.value = {
      token: setTokenTheme(newVal)
    }
    applyTheme(newVal)
  },
  {
    immediate: true
  }
)
const locale = ref(zhCN)
const router = useRouter()
onMounted(() => {
  // 增加对退出后还有旧页面的处理
  window.addEventListener('storage', () => {
    const userInfo = localStorage.getItem('userInfo')
    if (!userInfo) {
      router.replace('/login')
    }
  })
})
</script>
<style>
#app {
  width: 100%;
  height: 100%;
}
</style>
