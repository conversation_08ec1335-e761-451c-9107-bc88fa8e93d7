export interface filterType {
  tableTitle: ''
  tableName: ''
}

export interface resultType {
  description: string
  update_time: number
  version: number
  status: number
  table_name: string
  table_title: string
  _id: string
}

export const searchFields = [
  {
    operator: 'like',
    renderer: {
      type: 'TextField',
      options: {
        type: 'text',
        field: 'tableTitle',
        label: '表单标题'
      }
    }
  },
  {
    operator: 'like',
    renderer: {
      type: 'TextField',
      options: {
        type: 'text',
        field: 'tableName',
        label: '表单名称'
      }
    }
  }
]

export const tableFields = [
  { name: 'table_title', label: '表单标题', width: 120 },
  { name: 'table_name', label: '表单名称', width: 120 },
  { name: 'version', label: '版本', align: 'center', width: 60 },
  {
    name: 'status',
    label: '运行状态',
    align: 'center',
    dict: '运行状态',
    width: 100
  },
  {
    name: 'update_time',
    type: 'datetime',
    label: '更新时间',
    align: 'center',
    width: 120
  }
]

export const recordOperators = [
  {
    key: 'data',
    label: '数据'
  }
  // {
  //   key: 'delete',
  //   confirm: true,
  //   label: '清空数据',
  //   color: '#f00'
  // }
]

export const dicts = [
  {
    name: '运行状态',
    type: 'inline',
    items: [
      {
        label: '未开始',
        value: 0,
        tagColor: 'warning',
        tag: true
      },
      {
        label: '运行中',
        value: 1,
        tagColor: 'success',
        tag: true
      },
      {
        label: '已结束',
        value: -1,
        tagColor: 'error',
        tag: true
      }
    ]
  }
]
