<template>
  <div class="box" ref="dataMessageBox">
    <a-card title="数据管理">
      <div class="search-wrapper">
        <GTSearch
          ref="search-box"
          :fields="searchFields"
          v-if="searchFields.length != 0"
          @search="searchHandler"
        />
      </div>
      <div class="table-wrapper">
        <GTTable
          v-if="tableFields.length != 0"
          :fields="tableFields"
          :data="filterTableData"
          :dicts="dicts"
          :recordOperators="recordOperators"
          @recordOperatorClick="recordOperatorClickHandler"
          :loading="tableLoading"
          :allowSelect="false"
          :yOffset="310"
          keyField="_id"
        />
      </div>
    </a-card>
    <a-modal
      v-model:open="open"
      :maskClosable="false"
      destroyOnClose
      :getContainer="() => $refs.dataMessageBox"
      :footer="null"
      width="90%"
    >
      <Suspense>
        <GTSearchTable :formName="formName" v-if="formName"></GTSearchTable>
      </Suspense>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
defineOptions({ name: 'DataFill' })
import { searchFields, tableFields, dicts, recordOperators, filterType, resultType } from './config'
import { GTSearchTable } from '@gt/mis-components-web'
import { roleApi } from '@gt/mis-components-web/apis'
import { message } from 'ant-design-vue'

let userInfo = JSON.parse(localStorage.getItem('userInfo') as any)
let tableLoading = ref<boolean>(false)
let open = ref<boolean>(false)

let tableData = ref<resultType[]>([])
let filterTableData = ref<resultType[]>([])
let formName = ref<string>('')

const getResourcesFormList = async () => {
  tableLoading.value = true
  const { roles } = userInfo
  const roleId = roles[0]._id
  try {
    const res = await roleApi.getRoleFormList(roleId)
    tableData.value = res as resultType[]
    filterTableData.value = res as resultType[]
  } catch ({ msg }) {
    message.error(msg)
  } finally {
    tableLoading.value = false
  }
}

const searchHandler = (searchValues) => {
  let filter: filterType = {
    tableTitle: '',
    tableName: ''
  }
  if (searchValues.length !== 0) {
    searchValues.forEach((item) => {
      filter[item.name] = item.value
    })
  }

  filterTableData.value = tableData.value.filter(
    ({ table_title, table_name }) =>
      table_title.includes(filter.tableTitle) && table_name.includes(filter.tableName)
  )
}

const recordOperatorClickHandler = (payload) => {
  if (!payload) return
  const { key, record } = payload
  const { table_name } = record

  switch (key) {
    case 'data':
      open.value = true
      formName.value = table_name
      break
    default:
      break
  }
}
onMounted(() => {
  getResourcesFormList()
})
</script>
<style lang="less" scoped>
.box {
  height: 100%;
  overflow-y: hidden;
  :deep(.gt-table) {
    padding: 0;
  }
  :deep(.ant-modal) {
    top: 20px;
    padding-bottom: 0;
    height: calc(100% - 40px);
    > div {
      height: 100%;
      > .ant-modal-content {
        padding: 10px 20px;
        .ant-modal-close {
          top: 20px;
        }
      }
      .ant-modal-body {
        padding: 0;
        height: calc(100vh - 100px);
        overflow-y: auto;
      }
    }
  }
}
</style>
