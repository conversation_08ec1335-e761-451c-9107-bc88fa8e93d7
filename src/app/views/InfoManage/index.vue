<template>
  <div>
    <Suspense>
      <GTSearchTable
        v-if="formName"
        ref="GTSearchTableRef"
        :formName="formName"
        :showTitle="false"
        :initFilter="initFilter"
        :formTableOptionsHandler="formTableOptionsHandler"
        :beforeSubmitHandler="beforeSubmitHandler"
      />
    </Suspense>
  </div>
</template>

<script lang="ts" setup>
import { GTSearchTable } from '@gt/mis-components-web'
import { message } from 'ant-design-vue'

const route = useRoute()
const formName = ref(route.path.split('/').at(-1) || 'province')

const GTSearchTableRef = ref()

const userInfo = reactive(JSON.parse(localStorage.getItem('userInfo') || '{}'))

const initFilter = computed(() => {
  const { ccode, fcode } = userInfo
  const filters: any = []

  if (ccode) {
    filters.push(['=', 'ccode', ccode])
  }
  if (fcode) {
    filters.push(['=', 'fcode', fcode])
  }

  return filters
})

const formTableOptionsHandler = (table: any) => {
  const { ccode, fcode } = userInfo
  if (ccode) {
    table.searchItems[0].renderer.options.hide = true
  }
  if (fcode) {
    table.searchItems[1].renderer.options.hide = true
  }
  return table
}

const beforeSubmitHandler = (data, type) => {
  const { ccode, fcode } = userInfo
  if (ccode && data.ccode !== ccode) {
    message.warn('仅能操作本市县范围内的数据。')
    return
  } else if (fcode && data.fcode !== fcode) {
    message.warn('仅能操作本市县范围内的数据。')
    return
  } else {
    return data
  }
}
</script>

<style lang="less" scoped></style>
