<template>
  <div>
    <Suspense>
      <GTSearchTable
        v-if="tableOptions"
        ref="GTSearchTableRef"
        :tableOptions="tableOptions"
        formName="tb_user_info_map"
        @operate="operateHandler"
        :showTitle="false"
        :initFilter="initFilter"
        :beforeTableData="beforeTableData"
      />
    </Suspense>
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :maskClosable="false"
      destroyOnClose
      @ok="handleOk"
      :confirmLoading="confirmLoading"
      width="600px"
      :centered="true"
    >
      <GTForm ref="gtFormRef" :options="formOptions" />
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { GTSearchTable } from '@gt/mis-components-web'
import { roleApi } from '@gt/mis-components-web/apis'
import { message } from 'ant-design-vue'
import _ from 'lodash'
import { userConfig, tableOperators, recordOperators } from './config'
import { useFormOptions } from './hooks/useFormOptions'
import { useBaseOperate } from './hooks/useBaseOperate'

const route = useRoute()
const type = ref(route.path.split('/').at(-1) || 'province')
const roleValue = computed(() => {
  // 特殊配置
  return type.value
})

const roleId = ref()
const roleList = ref()

const GTSearchTableRef = ref()
const gtFormRef = ref()

const modalVisible = ref(false)
const modalTitle = ref('新增')
const confirmLoading = ref(false)

const userInfo = reactive(JSON.parse(localStorage.getItem('userInfo') || '{}'))

const { formOptions, initFormData } = useFormOptions({
  roleValue,
  userInfo
})

const { resetPassword, updateUserStatus, deleteUser, handleEdit, handleAdd } = useBaseOperate(
  GTSearchTableRef,
  roleId
)

const tableOptions = computed(() => {
  const obj: any = {}
  obj.tableFields = roleValue.value ? userConfig[roleValue.value].tableField : []
  obj.searchItems = roleValue.value ? userConfig[roleValue.value].searchItems : []
  obj.tableOperators = tableOperators

  let newRecordOperators = _.cloneDeep(recordOperators)
  newRecordOperators.unshift({
    key: '_edit',
    label: '编辑'
  })

  obj.recordOperators = newRecordOperators

  obj.modalWidth = 600

  console.log('obj.recordOperators', obj)

  return obj
})
const initFilter = computed(() => {
  const { ccode, fcode } = userInfo
  const filters: any = []
  filters.push(['contains', 'role_code', roleValue.value])

  if (ccode) {
    filters.push(['=', 'ccode', ccode])
  }
  if (fcode) {
    filters.push(['=', 'fcode', fcode])
  }

  return filters
})

const beforeTableData = (data) => {
  data.list.forEach((item) => {
    item.status = item.enabled ? '启用' : '禁用'
  })
  return data
}

onMounted(() => {
  getRoleId()
})

const getRoleId = async () => {
  const res = await roleApi.queryAllRole()
  if (res.list.length > 0) {
    let role = res.list.find((item) => item.code === roleValue.value)
    if (role) roleId.value = role._id
  } else {
    message.error('获取设置角色权限失败！请刷新页面后重试！')
  }
  roleList.value = res.list.map((item) => {
    return {
      label: item.name,
      value: item._id,
      code: item.code
    }
  })
}

const keyType = ref('')
const operateHandler = ({ key, data }) => {
  keyType.value = key
  const { user_id, enabled, nickname } = data || {}
  formOptions.readonlyFields = ''
  switch (key) {
    case '_add':
    case '_edit':
      editUser(key, data)
      break
    case 'resetPassword':
      resetPassword(data)
      break
    case 'start':
    case 'stop':
      updateUserStatus(user_id, !enabled, nickname)
      break
    case '_delete':
      deleteUser(data)
      break
    default:
      break
  }
}

const editUser = async (key, record) => {
  await initFormData(key, record)
  if (key === '_add') {
    modalTitle.value = '新增'
  } else {
    modalTitle.value = '编辑'
  }
  modalVisible.value = true
}

const handleOk = async () => {
  confirmLoading.value = true
  let res = await gtFormRef.value.submit()
  if (res.state) {
    let values = res.data

    if (typeof values === 'undefined') {
      confirmLoading.value = false
      return
    }
    const { ConfirmPassword, password } = values
    if (ConfirmPassword !== password) {
      message.error('两次输入密码不一致,请修改后再提交！')
      confirmLoading.value = false
      return
    }

    try {
      const extraInfoKey = ['pcode', 'pname', 'cname', 'ccode', 'fname', 'fcode', 'tcode', 'tname']

      const { fields } = formOptions.formDef
      const userParamKeys = fields
        .filter(({ name }) => !extraInfoKey.includes(name))
        .map(({ name }) => name)

      const params = {
        ..._.pick(values, userParamKeys),
        extraInfo: _.pick(values, extraInfoKey)
      }

      if (keyType.value === '_edit') {
        await handleEdit(params, values.user_id)
      } else if (keyType.value === '_add') {
        // 如果没设置params.roles，则在handleAdd中默认arams.roles= [roleId.value]；
        await handleAdd(params)
      }
      message.success('操作成功！')
      GTSearchTableRef.value.getList()
      modalVisible.value = false
    } catch (error) {
      console.log(error)
      message.error(error.msg || '操作失败！')
    } finally {
      confirmLoading.value = false
    }
  } else {
    confirmLoading.value = false
  }
}
</script>

<style lang="less" scoped></style>
