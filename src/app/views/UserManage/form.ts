// 表格字段类型定义
export interface TableField {
  name: string
  type: string
  label: string
  width?: number
  align?: string
}

// 搜索项类型定义
export interface SearchItem {
  operator: string
  renderer: {
    type: string
    options: {
      type: string
      field: string
      label: string
      dict?: any
      filter?: any[]
      hide?: boolean
    }
  }
}
// -----------------表格字段配置-----------------
export const baseTableFields: TableField[] = [
  {
    name: 'username',
    type: 'text',
    label: '用户名',
    width: 100,
    align: 'center'
  },
  {
    name: 'nickname',
    type: 'text',
    label: '姓名',
    align: 'center',
    width: 100
  },
  {
    name: 'phone',
    type: 'text',
    label: '手机号',
    align: 'center',
    width: 110
  },
  {
    name: 'role_name',
    type: 'json',
    label: '角色',
    align: 'center',
    width: 150
  },
  {
    name: 'status',
    type: 'text',
    label: '状态',
    align: 'center',
    width: 100
  }
]

// 市级表格字段
export const cityTableField: TableField = {
  name: 'cname',
  type: 'text',
  label: '市名称',
  width: 100,
  align: 'center'
}

// 县级表格字段
export const countyTableField: TableField = {
  name: 'fname',
  type: 'text',
  label: '县名称',
  width: 100,
  align: 'center'
}

// ------------搜索项配置---------------
export const baseSearchItems: SearchItem[] = [
  {
    operator: 'like',
    renderer: {
      type: 'TextField',
      options: {
        type: 'text',
        field: 'username',
        label: '用户名'
      }
    }
  },
  {
    operator: 'like',
    renderer: {
      type: 'TextField',
      options: {
        type: 'text',
        field: 'nickname',
        label: '姓名'
      }
    }
  }
]

// 市级搜索项
export const citySearchItem: SearchItem = {
  operator: '=',
  renderer: {
    type: 'SelectField',
    options: {
      type: 'text',
      field: 'ccode',
      label: '市名称',
      dict: {
        name: 'admin_city',
        type: 'table',
        fields: ['ccode', 'cname'],
        labelField: 'cname',
        valueField: 'ccode'
      }
    }
  }
}

// 县级搜索项
export const countySearchItem: SearchItem = {
  operator: '=',
  renderer: {
    type: 'SelectField',
    options: {
      type: 'text',
      field: 'fcode',
      label: '县名称',
      dict: {
        name: 'admin_county',
        type: 'table',
        fields: ['fcode', 'fname', 'ccode'],
        labelField: 'fname',
        valueField: 'fcode'
      },
      filter: ['=', 'ccode', 'ccode']
    }
  }
}

//-----------------表单定义-----------------
export const baseLayout = [
  {
    type: 'TextField',
    options: {
      type: 'text',
      field: 'username',
      label: '用户名',
      maxLength: 17,
      required: true
    }
  },
  {
    type: 'TextField',
    options: {
      type: 'text',
      field: 'nickname',
      label: '姓名',
      required: true
    }
  },
  {
    type: 'TextField',
    options: {
      type: 'text',
      field: 'password',
      label: '密码',
      mode: 'password',
      maxLength: 16,
      required: true
    }
  },
  {
    type: 'TextField',
    options: {
      type: 'text',
      field: 'ConfirmPassword',
      maxLength: 16,
      label: '确认密码',
      mode: 'password',
      required: true
    }
  },
  {
    type: 'TextField',
    options: {
      type: 'text',
      field: 'phone',
      label: '手机号码',
      maxLength: 11,
      required: true
    }
  },
  {
    type: 'TextField',
    options: {
      hide: true,
      type: 'text',
      field: 'pcode',
      label: '省编码',
      readonly: true,
      required: true,
      default: '45'
    }
  },
  {
    type: 'TextField',
    options: {
      hide: true,
      type: 'text',
      field: 'pname',
      label: '省名称',
      readonly: true,
      required: true,
      default: '广西壮族自治区'
    }
  }
]

export const cityLayout = [
  {
    type: 'SelectField',
    options: {
      type: 'text',
      field: 'ccode',
      label: '市名称',
      required: true,
      dict: {
        name: 'admin_city',
        type: 'table',
        fields: ['ccode', 'cname'],
        labelField: 'cname',
        valueField: 'ccode'
      }
    }
  },
  {
    type: 'TextField',
    options: {
      hide: true,
      type: 'text',
      field: 'cname',
      label: '市名称',
      readonly: true,
      calculator: ['get', 'cname', 'ccode']
    }
  }
]

export const countyLayout = [
  {
    type: 'SelectField',
    options: {
      type: 'text',
      field: 'fcode',
      label: '县名称',
      required: true,
      filter: ['=', 'ccode', 'ccode'],
      dict: {
        name: 'admin_county',
        type: 'table',
        fields: ['fcode', 'fname', 'ccode'],
        labelField: 'fname',
        valueField: 'fcode'
      }
    }
  },
  {
    type: 'TextField',
    options: {
      hide: true,
      type: 'text',
      field: 'fname',
      label: '县名称',
      calculator: ['get', 'fname', 'fcode']
    }
  }
]

// -----------------验证器配置-----------------
export const baseValidators = {
  fieldValidators: [
    {
      field: 'phone',
      label: '手机号码',
      validators: [
        {
          type: 'regex',
          options: {
            pattern: '^1([38][0-9]|4[01456789]|5[^4]|6[0256789]|7[0-9]|8[0-9]|9[^4])\\d{8}$'
          }
        }
      ]
    },
    {
      field: 'username',
      label: '用户名',
      validators: [
        {
          type: 'regex',
          options: {
            message: '仅可包含中文、英文和数字，5-17位',
            pattern: '^[\u4e00-\u9fa5A-Za-z0-9]{5,17}'
          }
        }
      ]
    },
    {
      field: 'password',
      label: '密码',
      validators: [
        {
          type: 'regex',
          options: {
            message: '仅可包含英文和数字，6-16位',
            pattern: '^[a-zA-Z0-9]{6,16}$'
          }
        }
      ]
    }
  ]
}
