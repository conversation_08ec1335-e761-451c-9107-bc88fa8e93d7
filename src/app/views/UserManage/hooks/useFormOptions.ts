import _ from 'lodash'
import { userConfig } from '../config'
import { userApi } from '@gt/mis-components-web/apis'
import { baseValidators } from '../form'
import { addKeyToLayout, removeLayout } from '../config'
import { parseJson, toFrontFormData } from '@gt/mis-components-web/utils'

interface FormRecordData {
  roles?: string[]
  [key: string]: any
}

interface UseFormConfigParams {
  roleValue: Ref<string>
  userInfo: Record<string, any>
}

/**
 * 表单配置 Hook，封装表单定义、布局处理和数据转换逻辑
 * @param {UseFormConfigParams} params - 配置参数
 * @returns {Object} 表单配置对象和相关方法
 */
export function useFormOptions(params: UseFormConfigParams) {
  // 表单状态管理
  const formOptions = reactive({
    formDef: {} as any,
    formRecordData: {} as FormRecordData,
    readonlyFields: ''
  })

  /**
   * 获取表单定义
   * @param {string} key - 操作类型('_add' | '_edit')
   * @returns {Object} 表单定义对象
   */
  const getFormDef = (key: string) => {
    let layout = _.cloneDeep(userConfig[params.roleValue.value].layout)
    let validators: any = []

    layout = handleLayoutByRole(layout)

    if (key !== '_add') {
      // 编辑时移除密码相关字段
      layout = removeLayout(layout, 'password')
      layout = removeLayout(layout, 'ConfirmPassword')
    } else {
      validators = baseValidators
      layout = handleLayoutByAdd(layout)
    }

    return parseJson({
      ...formOptions,
      layout,
      validators
    })
  }

  /**
   * 获取用户信息
   * @param {string} userId - 用户ID
   */
  const getQueryUserInfo = async (userId) => {
    const res = await userApi.queryUserInfo(userId)
    res.roles = res.roles.map((item) => item._id)
    return res.roles
  }

  /**
   * 初始化表单数据
   * @param {string} key - 操作类型('_add' | '_edit')
   * @param {Record<string, any>} record - 编辑时的记录数据
   */
  const initFormData = async (key: string, record?: Record<string, any>) => {
    const data = record ? _.cloneDeep(record) : {}
    formOptions.formRecordData = toFrontFormData(data)

    if (key === '_edit' && record?.user_id) {
      // 角色回显
      formOptions.formRecordData.roles = await getQueryUserInfo(record.user_id)
      formOptions.readonlyFields = 'username'
    }
    formOptions.formDef = getFormDef(key)
  }

  /**
   * 根据用户角色处理布局
   * @param {Array} layout - 原始布局配置
   * @returns {Array} 处理后的布局配置
   */
  const handleLayoutByRole = (layout: any[]) => {
    const { ccode, cname, fcode, fname } = params.userInfo
    let newLayout = _.cloneDeep(layout)

    if (ccode) {
      newLayout = addKeyToLayout(newLayout, 'ccode', 'default', ccode)
      newLayout = addKeyToLayout(newLayout, 'cname', 'default', cname)
      newLayout = addKeyToLayout(newLayout, 'ccode', 'readonly', true)
    }
    if (fcode) {
      newLayout = addKeyToLayout(newLayout, 'fcode', 'default', fcode)
      newLayout = addKeyToLayout(newLayout, 'fname', 'default', fname)
      newLayout = addKeyToLayout(newLayout, 'fcode', 'readonly', true)
    }

    return newLayout
  }

  /**
   * 添加操作时特殊 处理布局
   * @param {Array} layout - 原始布局配置
   * @returns {Array} 处理后的布局配置
   */
  const handleLayoutByAdd = (layout: any[]) => {
    return layout
  }

  return {
    formOptions,
    getFormDef,
    initFormData
  }
}
