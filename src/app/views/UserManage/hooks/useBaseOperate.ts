import { Modal, message } from 'ant-design-vue'
import { userApi } from '@gt/mis-components-web/apis'
import _ from 'lodash'

// 基础操作 api
export function useBaseOperate(tableRef, roleId) {
  /**
   * 重置用户密码
   * @param {Object} record - 用户记录对象
   */
  const resetPassword = (record) => {
    const { user_id, nickname } = record
    const ids = [user_id]
    const params: any = {}
    params.userIds = ids
    const nameStr = record ? nickname : '所选中'
    Modal.confirm({
      title: `请确定是否重置 ${nameStr} 的密码?`,
      content: '重置后,该(类)用户将会要求使用新密码重新登录',
      okText: '确定',
      onOk: () => {
        userApi
          .resetUserPasswords(params)
          .then(() => {
            message.success('重置密码成功!')
          })
          .catch((error) => {
            message.error(error.msg)
          })
      },
      onCancel() {}
    })
  }

  /**
   * 更新用户状态(启用/停用)
   * @param {string} id - 用户ID
   * @param {boolean} status - 目标状态
   * @param {string} username - 用户名
   */
  const updateUserStatus = (id, status, username) => {
    const msg = status ? '启用' : '停用'
    Modal.confirm({
      content: `确定${msg}${username}用户吗?`,
      okText: '确定',
      onOk: () => {
        const res = status ? userApi.resumeUser(id) : userApi.suspendUser(id)
        res
          .then(() => {
            message.success(`${msg}用户成功`)
            tableRef.value.getList()
          })
          .catch((error) => message.error(error.msg))
      }
    })
  }

  /**
   * 删除用户
   * @param {Object} data - 用户数据对象
   */
  const deleteUser = (data) => {
    const { user_id } = data
    Modal.confirm({
      title: '确定删除该用户吗?',
      content: '删除后,该用户将无法登录系统',
      okText: '确定',
      onOk: () => {
        userApi
          .deleteUser(user_id)
          .then(() => {
            message.success('删除用户成功')
            tableRef.value.getList()
          })
          .catch((error) => message.error(error.msg))
      }
    })
  }

  /**
   * 处理编辑用户 的请求
   * @param {Object} params - 用户参数
   * @param {string} userId - 用户ID
   */
  const handleEdit = async (params, userId) => {
    params._id = userId
    await userApi.updateUser(params)
  }

  /**
   * 处理新增用户 的请求
   * @param {Object} params - 用户参数
   */
  const handleAdd = async (params) => {
    if (params.roles && !Array.isArray(params.roles)) {
      params.roles = [params.roles]
    }
    if (!params.roles || params.roles.length === 0) {
      params.roles = [roleId.value]
    }
    await userApi.addUser(params)
  }

  return {
    resetPassword,
    updateUserStatus,
    deleteUser,
    handleEdit,
    handleAdd
  }
}
