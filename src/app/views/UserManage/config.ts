import {
  baseTableFields,
  baseSearchItems,
  cityTableField,
  cityLayout,
  baseLayout,
  countyTableField,
  countyLayout
} from './form'

// 用户相关权限配置
export const userConfig = {
  province: {
    tableField: baseTableFields,
    layout: [...baseLayout],
    searchItems: baseSearchItems
  },
  city: {
    tableField: [cityTableField, ...baseTableFields],
    layout: [...baseLayout, ...cityLayout],
    searchItems: baseSearchItems
  },
  county: {
    tableField: [cityTableField, countyTableField, ...baseTableFields],
    layout: [...baseLayout, ...cityLayout, ...countyLayout],
    searchItems: baseSearchItems
  }
}
// 表格操作按钮类型定义
export interface TableOperator {
  key: string
  label: string
  icon?: string
}

// 记录操作按钮类型定义
export interface RecordOperator {
  key: string
  label: string
  color?: string
  expression?: (data: any) => boolean
  disabled?: (data: any) => boolean
  showRoles?: string[]
}

// 表格操作按钮
export const tableOperators: TableOperator[] = [{ key: '_add', label: '新增', icon: 'plus' }]

// 记录操作按钮
export const recordOperators: RecordOperator[] = [
  { key: 'resetPassword', label: '重置密码' },
  {
    key: 'stop',
    label: '停用',
    color: '#f00',
    expression: (data) => data.enabled
  },
  {
    key: 'start',
    label: '启用',
    expression: (data) => !data.enabled
  },
  {
    key: '_delete',
    label: '删除',
    color: '#f00',
    showRoles: ['admin']
  }
]

/**
 * 递归查找对象数组中 options 属性并匹配 name，添加指定的键名和值
 * @param {Array} arr - 对象数组
 * @param {string} targetName - 要匹配的 name
 * @param {string} keyToAdd - 要添加的键名
 * @param {string} valueToAdd - 要添加的值
 */
export function addKeyToLayout(arr, targetName, keyToAdd, valueToAdd) {
  // 深拷贝原数组，避免修改原数组
  const newArray = JSON.parse(JSON.stringify(arr))

  newArray.forEach((obj) => {
    // 检查当前对象是否有 options 属性
    if (obj.options && typeof obj.options === 'object') {
      // 检查 options 中是否有匹配的 field
      if (obj.options.field === targetName) {
        // 添加指定的键名和值
        obj.options[keyToAdd] = valueToAdd
      }

      // 递归检查对象的每个属性，查找嵌套的 options
      for (const key in obj) {
        if (obj[key] && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
          // 递归处理嵌套对象
          addKeyToLayout([obj[key]], targetName, keyToAdd, valueToAdd)
        }
      }
    }
  })

  return newArray
}

/**
 * 递归查找对象数组中 options 属性并匹配 name，删除对应的对象，返回新数组
 * @param {Array} arr - 对象数组
 * @param {string} targetName - 要匹配的 name
 * @returns {Array} - 处理后的新数组
 */
export function removeLayout(arr, targetName) {
  // 深拷贝原数组，避免修改原数组
  const newArray = JSON.parse(JSON.stringify(arr))

  // 递归函数，用于查找并删除匹配的对象
  function recursiveRemove(obj) {
    if (!obj) return

    // 检查当前对象是否有 options 属性
    if (obj.options && typeof obj.options === 'object') {
      // 检查 options 中是否有匹配的 field
      if (obj.options.field === targetName) {
        // 如果匹配，标记为删除（这里用 null 表示，实际可以改为其他逻辑）
        return null
      }
    }

    // 递归检查对象的嵌套属性
    for (const key in obj) {
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        const result = recursiveRemove(obj[key])
        if (result === null) {
          // 如果嵌套对象需要删除，则删除当前对象
          return null
        }
      }
    }

    // 如果没有匹配，返回当前对象
    return obj
  }

  // 过滤掉标记为删除的对象
  const filteredArray = newArray.filter((obj) => recursiveRemove(obj) !== null)
  return filteredArray
}
