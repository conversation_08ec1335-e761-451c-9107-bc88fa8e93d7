<template>
  <div class="max">
    <GTForm id="report" ref="formRef" :options="options" @change="changeHandler" />
    <a-button type="primary" @click="submit">提交</a-button>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'FormTest' })
const formRef = ref()
const formDef = {
  dicts: [
    {
      name: '12222',
      type: 'enum',
      items: ['1233', '234']
    },
    {
      name: '省级行政区',
      type: 'enum',
      items: ['1233', '37']
    },
    {
      name: '市级行政区',
      type: 'table',
      fields: ['市编码', '市名称'],
      labelField: '市名称',
      valueField: '市编码'
    },
    {
      name: '县级行政区',
      type: 'table',
      fields: ['市编码', '县名称', '县编码'],
      labelField: '县名称',
      valueField: '县编码'
    }
  ],
  layout: {
    // itemOptions: {
    //   labelSpan: 5,
    //   wrapperSpan: 18
    // },
    children: [
      {
        type: 'Card',
        options: {
          title: 3,
          depends: ['==', '数字', 11],
          children: [
            {
              type: 'TextField',
              options: {
                type: 'text',
                field: 'value',
                label: '文本框',
                required: true,
                // hide: true
                depends: ['==', 'value1', '11']
              }
            },
            {
              type: 'TextField',
              options: {
                type: 'text',
                field: 'value1',
                label: '文本框1',
                unit: 'MenuUnfoldOutlined',
                // calculator: ['concat', '+', 'value', '市名称'],
                // readonly: true,
                required: true
              }
            },
            {
              type: 'Title',
              options: {
                text: 'text'
              }
            },
            {
              type: 'SplitLine'
            }
            // {
            //   type: 'TextField',
            //   options: {
            //     type: 'text',
            //     field: '市名称',
            //     label: '市名称',
            //     readonly: true,
            //     calculator: ['get', '市名称', '市编码'],
            //     required: true
            //   }
            // },
            // {
            //   type: 'SelectField',
            //   options: {
            //     type: 'text',
            //     label: '市级行政区',
            //     required: true,
            //     field: '市编码',
            //     // default: '5403',
            //     dictRef: '市级行政区'
            //   }
            // },
            // {
            //   type: 'SelectField',
            //   options: {
            //     type: 'text',
            //     label: '县级行政区',
            //     required: true,
            //     default: '540325',
            //     field: '县编码',
            //     lazy: true,
            //     dictRef: '县级行政区',
            //     filter: ['=', '市编码', '市编码']
            //   }
            // }
          ]
        }
      },
      {
        type: 'NumberField',
        options: {
          type: 'text',
          field: '数字',
          label: '数字',
          required: true
        }
      },
      {
        type: 'NumberField',
        options: {
          type: 'text',
          field: '数字112',
          label: '数字2',
          required: true
        }
      },
      {
        type: 'RadioField',
        options: {
          type: 'text',
          field: '级联框',
          label: '级联框',
          dict: {
            type: 'inline',
            items: [
              {
                value: 'zhejiang',
                label: 'Zhejiang',
                children: [
                  {
                    value: 'hangzhou',
                    label: 'Hangzhou',
                    children: [
                      {
                        value: 'xihu',
                        label: 'West Lake'
                      }
                    ]
                  }
                ]
              },
              {
                value: 'jiangsu',
                label: 'Jiangsu',
                children: [
                  {
                    value: 'nanjing',
                    label: 'Nanjing',
                    children: [
                      {
                        value: 'zhonghuamen',
                        label: 'Zhong Hua Men'
                      }
                    ]
                  }
                ]
              }
            ]
          },
          required: true
        }
      },
      {
        type: 'NumberField',
        options: {
          type: 'text',
          field: '数字3',
          label: '数字3',
          precision: 2,
          unit: '吨',
          calculator: ['+', '数字', '数字2'],
          readonly: true,
          required: true
        }
      },
      {
        type: 'DateField',
        options: {
          type: 'text',
          default: '{now}',
          field: '数字选择框',
          label: '数字选择框',
          // mode: 'year',
          // showTime: true,
          monthOffset: [2, 2],
          // minDate: 1709654400000,
          // maxDate: 1711555200000,
          required: true
        }
      },
      {
        type: 'YearSelectField',
        options: {
          type: 'text',
          label: '年份',
          field: '年份',
          required: true
        }
      },
      {
        type: 'iconField',
        options: {
          type: 'text',
          label: 'icon',
          field: '图标',
          default: 'FileFilled',
          required: true
        }
      },
      {
        type: 'NumberWithUnitField',
        options: {
          type: 'text',
          label: '数字单位框11111',
          precision: 2,
          // calculator: ['+', '数字', '数字2'],
          // depends: ['==', '数字112', 11],
          field: {
            field: '数字单位框'
          },
          unitField: {
            field: '数字单位'
          },
          dictRef: '市级行政区',
          required: true
        }
      },
      {
        type: 'DateRangeField',
        options: {
          type: 'date',
          label: '时间范围选择器',
          required: true,
          // showTime: true,
          // default: [1710432000000, 1710518399999],
          depends: ['==', '数字112', 11],
          startField: {
            type: 'datetime',
            field: 'startField',
            label: '开始时间'
          },
          endField: {
            type: 'datetime',
            field: 'endField',
            label: '结束时间'
          }
        }
      },
      {
        type: 'DateRangeField',
        options: {
          type: 'date',
          label: '时间范围选择器2',
          required: true,
          // showTime: true,
          // default: [1710432000000, 1710518399999],
          depends: ['==', '数字112', 112],
          startField: {
            type: 'datetime',
            field: 'startField2',
            label: '开始时间'
          },
          endField: {
            type: 'datetime',
            field: 'endField2',
            label: '结束时间'
          }
        }
      },
      {
        type: 'FileField',
        options: {
          type: 'file',
          label: '文件上传',
          required: true,
          field: 'n6th7w4i7j',
          suffixes: ['xlsx'],
          // max: 1,
          sizeLimit: 20480
        }
      },
      {
        type: 'PictureField',
        options: {
          type: 'text',
          label: '图片选择器',
          required: true,
          field: '图片选择器',
          max: 1,
          suffixes: ['.png', '.jpg', '.jpeg']
        }
      },
      {
        type: 'LonLatInputField',
        options: {
          label: '经纬度输入器',
          type: 'numeric',
          precision: 6,
          required: true,
          // readonly: true,
          selectType: 'degree',
          field: 'uv4j1p1c3s'
        }
      },
      {
        type: 'SubForm',
        options: {
          field: '学习mis表子表',
          label: '测试子表',
          formUid: '学习mis表子表',
          uniqueKey: '字段1'
        }
      },
      {
        type: 'LonLatField',
        options: {
          type: 'numeric',
          mapLocationVisible: true,
          required: true,
          label: '经纬度',
          lonField: {
            field: '现场经度'
          },
          latField: {
            field: '现场纬度'
          },
          pcodeField: {
            field: '省编号'
          },
          pnameField: {
            field: '省名称'
          },
          cnameField: {
            field: '市名称'
          },
          ccodeField: {
            field: '市编号'
          },
          fnameField: {
            field: '县名称'
          },
          fcodeField: {
            field: '县编号'
          },
          addressField: {
            field: '地址'
          }
        }
      },
      {
        type: 'TextField',
        options: {
          type: 'text',
          field: '省名称',
          label: '省名称'
        }
      },
      {
        type: 'SelectField',
        options: {
          type: 'text',
          field: '省编号',
          dictRef: '省级行政区',
          label: '省编号'
        }
      },
      {
        type: 'TextField',
        options: {
          type: 'text',
          field: '市编号',
          label: '市编号'
        }
      },
      {
        type: 'TextField',
        options: {
          // help: '这是市名称',
          type: 'text',
          field: '市名称',
          label: '市名称',
          required: true
        }
      },
      {
        type: 'TextField',
        options: {
          type: 'text',
          field: '县编号',
          label: '县编号'
        }
      },
      {
        type: 'TextField',
        options: {
          type: 'text',
          field: '县名称',
          label: '县名称'
        }
      },
      {
        type: 'TextField',
        options: {
          type: 'text',
          field: '地址',
          label: '地址'
        }
      }
    ]
  },
  validators: {
    fieldValidators: [
      {
        label: '文本输入框',
        validators: [
          {
            type: 'required'
          },
          {
            type: 'text-range',
            options: {
              max: 11,
              min: 6
              // message: '1222'
            }
          }
        ],
        field: 'value1'
      }
    ],
    formValidators: [
      {
        type: 'compare',
        options: {
          expression: ['==', 'value2', 'value1'],
          message: 'a和b的值必须一致'
        }
      }
    ],
    subformValidators: []
  },
  subTables: [
    {
      fk: '__main_form_record_uid_field__',
      name: '学习mis表子表'
    }
  ]
}
const options = {
  formDef,
  //   readonlyFields: '*',
  formRecordData: {
    value: 1,
    startField: 1710432000000,
    endField: 1710518399999,
    uv4j1p1c3s: 233,
    图片选择器: ['mis-engine-server//20240321/v6d998ecljwmydmom2vunoey4c7yphqm.jpg'],
    n6th7w4i7j: [
      { name: '农业外来入侵植物重点调查记录表.xlsx', uid: '328168a92ecc4f029355b459716eedf9' }
    ],
    学习mis表子表: {
      form: '学习mis表子表',
      data: [
        {
          字段3: 122,
          字段2: 333,
          字段1: 444,
          字段4: 12122
        }
      ]
    }
  }
}
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const changeHandler = ({ field, value }) => {
  //   console.log(' field, value  :>> ', field, value)
}
const submit = async () => {
  const res = await formRef.value.submit()
  console.log('res :>> ', res)
}
</script>

<style lang="less" scoped>
.max {
  width: 100%;
  padding: 20px;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}
</style>
