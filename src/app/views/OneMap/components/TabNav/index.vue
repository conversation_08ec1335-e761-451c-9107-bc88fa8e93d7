<template>
  <div class="pb-4 pt-2 text-[16px]">
    <div class="flex items-center gap-2">
      <div
        v-for="(item, index) in tabs"
        :key="index"
        class="min-w-[120px] h-8 px-2 flex items-center justify-center rounded cursor-pointer transition-all duration-200 border border-[#6BF6FF] select-none"
        :class="{
          'text-[#A6C1D3] bg-[#003243]': activeTab === index,
          'text-[#8BB4B9] opacity-50 hover:opacity-70': activeTab !== index
        }"
        @click="updateActiveTab(index)"
      >
        {{ item }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps<{
  tabs: string[]
  activeTab: number
}>()

const emit = defineEmits(['update:activeTab'])

const updateActiveTab = (index: number) => {
  emit('update:activeTab', index)
}
</script>
