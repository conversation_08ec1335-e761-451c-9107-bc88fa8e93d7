<template>
  <!-- 
  <div class="line-chart" :style="{ marginBottom: gap }">
    <div class="chart-title">{{ props.title }}</div>
    <div ref="chartRef" :style="[pieStyle]"></div>
  </div>
   -->
  <div ref="chartRef" :style="[pieStyle]"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'

interface Props {
  title?: string
  centerText?: string
  data: Array<{ name: string; value: number }>
  options?: Partial<EChartsOption>
  width?: string
  height?: string
  unit?: string
  gap?: string
  lineType?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  gap: '20px',
  options: () => ({}),
  lineType: 'straight',
  width: '200px',
  height: '200px'
})

const pieStyle = computed(() => ({
  width: props.width,
  height: props.height
}))

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) return
  chart = echarts.init(chartRef.value, undefined, { width: 'auto', height: 'auto' })
}

const updateChart = () => {
  if (!chart) return

  // 判断是否需要显示dataZoom（滚动条）
  const showDataZoom = props.data.length > 5

  const defaultOption: EChartsOption = {
    textStyle: {
      color: '#A6C1D3',
      fontSize: 16
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const value = params[0].value
        const formattedValue = Number(value).toFixed(2)
        return `${params[0].name}${'年'}: ${formattedValue}${props.unit || ''}`
      }
    },
    grid: {
      top: 30,
      bottom: 40,
      left: 40,
      right: 20
    },
    xAxis: {
      type: 'category',
      data: props.data.map((item) => item.name),
      axisLabel: {
        fontSize: 16
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 16
      }
    },
    series: [
      {
        type: 'line',
        data: props.data.map((item) => item.value),
        areaStyle: props.lineType === 'curve' ? {} : undefined,
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          formatter: (params) => {
            return Number(params.value).toFixed(2)
          }
        },
        lineStyle: {
          color: '#22FFCC'
        },
        smooth: props.lineType === 'curve',
        itemStyle: {
          color: '#22FFCC'
        }
      }
    ],

    dataZoom: showDataZoom
      ? [
          {
            type: 'slider',
            show: true,
            backgroundColor: 'rgba(0, 89, 154, 0.5)',
            fillerColor: '#22FFCC',
            moveHandleSize: 0,
            handleStyle: { opacity: 0 },
            borderColor: 'rgba(0, 89, 154, 0)',
            showDataShadow: false,
            startValue: 0,
            endValue: 4,
            bottom: 0,
            height: 10,
            handleSize: 0,
            showDetail: false,
            brushSelect: false,
            zoomLock: false
          }
        ]
      : []
  }

  const option = {
    ...defaultOption,
    ...props.options
  }

  chart.setOption(option)
}

// 监听数据变化
watch(
  () => [props.data, props.title, props.options],
  () => {
    updateChart()
  },
  { deep: true }
)

// 监听窗口大小变化
const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  updateChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped></style>
