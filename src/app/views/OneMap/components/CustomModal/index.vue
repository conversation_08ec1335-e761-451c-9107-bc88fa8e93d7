<template>
  <a-modal
    :open="props.open"
    :title="props.title"
    :closable="true"
    :maskClosable="true"
    :destroyOnClose="true"
    :centered="true"
    footer=""
    :width="props.width"
    wrapClassName="onemap-modal"
    @update:open="emit('update:open', $event)"
  >
    <div class="overflow-y-scroll" :style="[style]">
      <slot></slot>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
const props = withDefaults(
  defineProps<{
    title: string
    open?: boolean
    width?: string
    height?: string
  }>(),
  {
    open: false,
    width: '45%',
    height: '55vh'
  }
)

const emit = defineEmits(['update:open'])

const style = computed(() => {
  return {
    height: props.height
  }
})
</script>
