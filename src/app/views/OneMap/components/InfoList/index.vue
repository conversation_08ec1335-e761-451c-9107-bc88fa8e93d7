<template>
  <div class="flex flex-col gap-2 text-base">
    <div
      v-for="item in list"
      :key="item.label"
      :class="{ 'flex-col': item.label.length > 6, 'flex gap-2': !contentWrap }"
    >
      <div class="flex-nowrap min-w-max text-map-simple-blue">{{ item.label }}</div>
      <div
        class="flex-grow break-words text-white"
        :class="{ 'line-clamp-3': isEllipsis, 'px-[40px]': contentWrap }"
      >
        {{ data[item.field] || '-' }}
        {{ item.unit ? item.unit : '' }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = withDefaults(
  defineProps<{
    list: Array<{ label: string; field: string; unit?: string }>
    data: Record<string, any>
    isEllipsis?: boolean
    contentWrap?: boolean
  }>(),
  {
    isEllipsis: true,
    contentWrap: true
  }
)
</script>
