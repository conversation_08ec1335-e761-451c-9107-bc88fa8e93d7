<template>
  <div>
    <div class="image-carousel" v-if="images">
      <a-image :src="currentImage.url" :width="width" :height="height" />

      <div v-if="desc" class="image-caption">
        {{ desc }}
      </div>

      <div v-if="images.length > 1" class="controls-container">
        <div class="control-btn prev-btn" @click="prev"></div>
        <div>
          <span class="text-[#51F3FF] text-base">{{ currentIndex + 1 }}</span>
          <span class="text-[#fff]"> / </span>
          <span class="text-xs text-[#fff]">{{ images.length }}</span>
        </div>
        <div class="control-btn next-btn" @click="next"></div>
      </div>
    </div>
    <div v-else class="flex justify-center items-center" :style="{ height: height }">
      <img :src="noImg" alt="暂无图片" class="h-full" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import noImg from '@/assets/images/onemap/noImg.png'

interface Props {
  images: any[]
  desc?: string
  width?: string
  height?: string
}
const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '100%'
})

const currentIndex = ref(0)
const showPreview = ref(false)

// 上一张
const prev = () => {
  currentIndex.value = Math.max(0, currentIndex.value - 1)
}

// 下一张
const next = () => {
  currentIndex.value = Math.min(props.images.length - 1, currentIndex.value + 1)
}

// 当前显示的图片
const currentImage = computed(() => props.images[currentIndex.value])
</script>

<style>
.ant-image-preview-img {
  width: 70vw;
  height: 70vh;
  object-fit: contain;
}
.ant-image-preview-mask {
  background-color: rgba(0, 0, 0, 0.6) !important;
}
</style>

<style lang="less" scoped>
:deep(.ant-image-img) {
  object-fit: cover;
}

.image-carousel {
  position: relative;
  max-width: 100%;
  height: auto;
  .main-image {
    object-fit: cover;
    border-radius: 8px;
  }

  .image-caption {
    position: absolute;
    bottom: 50px;
    left: 0;
    right: 0;
    height: 50px;
    background: rgba(0, 0, 0, 0.4);
    color: #ffffff;
    padding: 8px 16px;
    text-align: center;
    font-size: 14px;
    display: flex;
    align-items: center;
    border-radius: 0 0 8px 8px;
  }

  /* 新增控制容器样式 */
  .controls-container {
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 126px;
    height: 35px;
    margin: 15px auto;

    border: 1px solid #158e97;
    border-radius: 30px;
  }

  .control-btn {
    width: 30px;
    height: 30px;
    background-size: contain;
    background-repeat: no-repeat;
    cursor: pointer;
    transition: opacity 0.3s;

    &:hover {
      opacity: 1;
    }
  }

  .prev-btn {
    margin-left: 3px;
    background-image: url('@/assets/images/onemap/left.png');
    background-size: 100% 100%;
  }

  .next-btn {
    margin-right: 3px;
    background-image: url('@/assets/images/onemap/right.png');
    background-size: 100% 100%;
  }
}
</style>
