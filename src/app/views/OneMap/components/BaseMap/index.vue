<template>
  <div id="map" class="map" ref="mapContainer"></div>
  <div ref="fogOverlay" class="fog-overlay"></div>
  <div class="shadow-layer"></div>
</template>

<script setup lang="ts">
import mapboxgl from '@/common/mapbox-gl/mapbox-gl'
import { Scene, Mapbox } from '@antv/l7'
import '@/common/mapbox-gl/mapbox-gl.css'
import { cloneDeep } from 'lodash'
import {
  initMapBox,
  baseCenter,
  mapOperateSwitch,
  extrudeHeight,
  baseZoom,
  basePitch,
  baseLayerSwitch,
  initSceneLayerState
} from '../../hooks/useMapLayers'
import { useOneMapStore } from '@/stores'
import { elRectification } from 'autofit.js'
import { formDataKey } from '../../config'
import { initSceneLayers } from '../../hooks/useMainHandler'

// 数据
const formData: any = inject(formDataKey)

// 状态管理
const oneMapStore = useOneMapStore()
const emit = defineEmits(['mapLoad', 'sceneLoad', 'click'])

// DOM 引用
const mapContainer = ref<HTMLDivElement>()
const fogOverlay = ref<HTMLDivElement>()

// 地图实例
let map: mapboxgl.Map
let scene: Scene

// 生命周期钩子 - 组件挂载
onMounted(() => {
  nextTick(() => {
    elRectification('#map')
  })
  map = initMapBox(mapContainer.value) as mapboxgl.Map
  window.map = map
  map.on('load', async () => {
    initMapboxSource()
    window.map = map
    initScene()
    emit('mapLoad', map)
  })
})

// 场景初始化
const initScene = async () => {
  scene = new Scene({
    renderer: 'regl',
    id: 'map',
    logoVisible: false,
    map: new Mapbox({
      mapInstance: map
    })
  })
  scene.on('loaded', async () => {
    console.log('scene load')
    // 加载场景图层
    await initSceneLayers(scene, formData.value)
    emit('sceneLoad', scene)
    // 地图操作控制
    mapOperateSwitch(map, false)
  })
}

// Mapbox 资源初始化
const initMapboxSource = async () => {
  // 添加天空效果
  map.addLayer({
    id: 'sky',
    type: 'sky',
    paint: {
      'sky-type': 'atmosphere',
      'sky-atmosphere-sun': [0.0, 0.0],
      'sky-atmosphere-sun-intensity': 1
    }
  })
  // 添加图层源
  map!.addSource('tibet-dem', {
    type: 'raster-dem',
    tiles: ['http://39.104.87.15/api/maptiles/hubei_dem/{z}/{x}/{y}.png']
  })
  const geojson: any = {
    type: 'FeatureCollection',
    features: [
      {
        type: 'Feature',
        geometry: {
          type: 'Polygon',
          coordinates: [
            [
              [-180, -90],
              [180, -90],
              [180, 90],
              [-180, 90],
              [-180, -90]
            ]
          ]
        }
      }
    ]
  }

  map.addSource('mapMaskLayer', {
    type: 'geojson',
    data: geojson
  })

  map.addLayer({
    id: 'mapMaskLayer',
    type: 'fill',
    source: 'mapMaskLayer',
    paint: {
      'fill-color': '#7EFFE8',
      'fill-opacity': 0.2
    }
  })

  map.setLayoutProperty('mapMaskLayer', 'visibility', 'none')
}

// Mapbox 图层初始化
const initMapboxLayer = async () => {
  // 添加地形
  map.setTerrain({ source: 'tibet-dem', exaggeration: 1.5 })

  // 显示图层
  // mapboxLayer.forEach((layer: any) => {
  //   map.setLayoutProperty(layer.id, 'visibility', 'visible')
  // })

  // 开放地图操作
  mapOperateSwitch(map, true)
}

// 设置遮罩图层
async function setMaskLayer(code: string) {
  if (!map) return

  // 隐藏基本图层
  baseLayerSwitch(scene, false)

  initMapboxLayer()
  fogOverlay.value!.classList.add('active')

  // 更新图层

  // 创建遮罩 GeoJSON
  let geojson = cloneDeep(formData.value.city_geojson)
  geojson.features = geojson.features.filter((item) => item.properties.code === code)

  // 提取坐标
  const cityCoords: any = []
  geojson.features.forEach((feature) => {
    if (feature.geometry.type === 'MultiPolygon') {
      feature.geometry.coordinates.forEach((polygon) => {
        cityCoords.push(...polygon)
      })
    } else if (feature.geometry.type === 'Polygon') {
      cityCoords.push(...feature.geometry.coordinates)
    }
  })
  const maskGeoJSON: any = {
    type: 'FeatureCollection',
    features: [
      {
        type: 'Feature',
        geometry: {
          type: 'Polygon',
          coordinates: [
            [
              [-180, -90],
              [180, -90],
              [180, 90],
              [-180, 90],
              [-180, -90]
            ],
            ...cityCoords
          ]
        }
      }
    ]
  }

  if (map.getSource('mask')) map.removeSource('mask')
  if (map.getLayer('mask')) map.removeLayer('mask')
  if (map.getLayer('mapMaskLayer')) map.setLayoutProperty('mapMaskLayer', 'visibility', 'none')

  map.addSource('mask', {
    type: 'geojson',
    data: maskGeoJSON
  })
  map.addLayer({
    id: 'mask',
    type: 'fill',
    source: 'mask',
    paint: {
      'fill-color': 'rgba(0, 0, 0, 1)',
      'fill-opacity': 0.7
    }
  })
  const countyLineLayer = scene.getLayerByName('base-county-line')
  const countyLabelLayer = scene.getLayerByName('base-county-label')
  // 显示县级
  countyLineLayer?.show()
  countyLabelLayer?.show()
  countyLabelLayer?.style({
    raisingHeight: 0
  })
  countyLineLayer?.style({
    raisingHeight: 0
  })
  countyLineLayer?.color('ccode', (val) => {
    return code == val ? '#E5FFFF' : 'rgba(0,0,0,0)'
  })
  countyLabelLayer?.filter('fcode', (val) => {
    return code == val
  })

  let city = oneMapStore.getCityInfo(code)

  const { x_min, y_min, x_max, y_max } = city
  setTimeout(() => {
    map.setMaxBounds([x_min - 3, y_min - 3, x_max + 3, y_max + 3])
    fogOverlay.value!.classList.remove('active')
  }, 1000)
}

// 设置主题图层
function setThemeLayer() {
  map.setPitch(basePitch)

  if (map.getLayer('mask')) map.removeLayer('mask')
  if (map.getLayer('mapMaskLayer')) map.setLayoutProperty('mapMaskLayer', 'visibility', 'visible')
  if (map.getLayer('protect-point-area')) map.removeLayer('protect-point-area')
  // 取消限制四至
  map!.setMaxBounds(null)

  // 设置视图
  map.flyTo({
    center: baseCenter,
    zoom: baseZoom,
    duration: 2000
  })

  // 重新初始化图层
  initSceneLayerState(scene)

  const countyLineLayer = scene.getLayerByName('base-county-line')
  const countyLabelLayer = scene.getLayerByName('base-county-label')
  countyLabelLayer?.hide()
  countyLabelLayer?.style({
    raisingHeight: extrudeHeight
  })
  countyLineLayer?.style({
    raisingHeight: extrudeHeight
  })

  // 地图操作控制
  mapOperateSwitch(map, false)
}

// 飞到指定点
function flyToPoint(params: any) {
  map!.flyTo({
    center: [params.x, params.y],
    zoom: params.zoom || 9,
    pitch: basePitch,
    duration: 1000,
    easing: (t) => t
  })
}

// 导出方法
defineExpose({
  setMaskLayer,
  flyToPoint,
  setThemeLayer
})

// TODO 待优化
watch(
  () => oneMapStore.selectedArea,
  (val) => {
    const countyLineLayer = scene.getLayerByName('base-county-line')
    const countyLabelLayer = scene.getLayerByName('base-county-label')
    const cityLineLayer = scene.getLayerByName('base-city-line')
    const cityLabelLayer = scene.getLayerByName('base-city-label')
    const boundaryLayer = scene.getLayerByName('base-boundary')
    if (val) {
      // 隐藏市级
      cityLineLayer?.hide()
      cityLabelLayer?.hide()
      // 显示县级
      countyLineLayer?.size(2).color('#fff')
      countyLabelLayer?.show()

      countyLineLayer?.color('ccode', (code) => {
        return code == val.ccode ? '#E5FFFF' : 'rgba(0,0,0,0)'
      })
      countyLabelLayer?.filter('ccode', (code) => {
        return code == val.ccode
      })

      boundaryLayer?.filter('ccode', (code) => {
        return code == val.ccode
      })
    } else {
      // 隐藏县级标签
      countyLabelLayer?.hide()
      // 显示市级
      cityLineLayer?.show()
      cityLabelLayer?.show()
      // 设置县级线颜色
      countyLineLayer?.size(1).color('rgba(255,255,255,0.2)')
      // maskLayer.color('rgba(0,0,0,0)')
      boundaryLayer?.filter(true)
    }
    scene.render()
  }
)
</script>

<style lang="less" scoped>
.map {
  width: 100%;
  height: 100%;
}

.fog-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.7) 30%,
    rgba(255, 255, 255, 0.9) 70%
  );
  opacity: 0;
  pointer-events: none;
  transition: opacity 3s ease;
  z-index: 999;
}

.fog-overlay.active {
  opacity: 1;
  pointer-events: auto;
}

.shadow-layer {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background: radial-gradient(rgba(0, 0, 0, 0) 40%, rgba(0, 0, 0, 0.5) 90%);
}
</style>
