<template>
  <div class="relative" :style="pieStyle">
    <div class="relative z-10" :style="pieStyle" ref="chartRef"></div>
    <div class="decoration1"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import * as echarts from 'echarts'
import type { EChartsOption, EChartsCoreOption, EChartsType } from 'echarts'
import { useEventListener } from '@vueuse/core'

const emit = defineEmits<{
  click: [data: { name: string; value: number }]
}>()

interface PieChartProps {
  centerText?: string
  data: Array<{ name: string; value: number }>
  options?: Partial<EChartsOption>
  width?: string
  height?: string
  color?: string[]
  unit?: string
}

const props = withDefaults(defineProps<PieChartProps>(), {
  centerText: '',
  options: () => ({}),
  width: '200px',
  height: '200px',
  unit: '种'
})

const pieStyle = computed(() => ({
  width: props.width,
  height: props.height
}))

const chartRef = ref<HTMLElement>()
const chartText = ref<string>(props.centerText || '')
let chart: EChartsType | null = null

// 合并图表选项
const chartOptions = computed<EChartsCoreOption>(() => {
  const defaultOption: EChartsOption = {
    color: props.color || ['#00C2E1', '#25DB50', '#F8D42A', '#F59E2F', '#F24F29'],
    series: [
      {
        type: 'pie',
        radius: ['75%', '90%'],
        data: props.data,
        label: {
          show: true,
          position: 'center',
          formatter: `{a|${chartText.value}}{b|${props.unit}}`,
          rich: {
            a: {
              fontSize: 34,
              color: '#fff'
            },
            b: {
              fontSize: 18,
              color: '#fff',
              verticalAlign: 'bottom',
              padding: [2, 2]
            }
          },
          fontSize: 34,
          color: '#fff'
        },
        itemStyle: {
          borderWidth: 2
        }
      }
    ]
  }

  return {
    ...defaultOption,
    ...props.options
  }
})

// 处理图表点击事件
const handleChartClick = (params: any) => {
  emit('click', params.data)
}

// 处理鼠标悬停事件
const handleChartMouseover = (params: any) => {
  const { data } = params
  let { name, value } = data as { name: string; value: number }

  // 长名称换行处理
  if (name.length > 4) {
    name = name.substring(0, 4) + '\n' + name.substring(4, 8)
  }

  chartText.value = `${name}\n(${value}${props.unit})`
  updateChartLabel(chartText.value, 20)
}

// 处理鼠标移出事件
const handleChartMouseout = () => {
  chartText.value = `{a|${props.centerText}}{b|${props.unit}}` || ''
  updateChartLabel(chartText.value, 34)
}

// 更新图表标签
const updateChartLabel = (text: string, fontSize: number) => {
  chart?.setOption({
    series: [
      {
        label: {
          formatter: text,
          fontSize
        }
      }
    ]
  })
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  // 销毁旧实例
  if (chart) {
    chart.dispose()
  }

  chart = echarts.init(chartRef.value)

  // 绑定事件
  chart.on('click', handleChartClick)
  chart.on('mouseover', handleChartMouseover)
  chart.on('mouseout', handleChartMouseout)

  // 设置初始选项
  updateChart()
}

// 更新图表
const updateChart = () => {
  if (!chart) return
  chart.setOption(chartOptions.value)
}

// 监听数据变化
watch(
  () => [props.data, props.options, props.centerText],
  () => {
    chartText.value = props.centerText || ''
    updateChart()
  },
  { deep: true }
)

onMounted(() => {
  initChart()
})

onUnmounted(() => {
  // 移除事件监听
  if (chart) {
    chart.off('click', handleChartClick)
    chart.off('mouseover', handleChartMouseover)
    chart.off('mouseout', handleChartMouseout)
    chart.dispose()
    chart = null
  }
})
</script>

<style scoped>
.decoration1 {
  position: absolute;
  background-image: url('@/assets/images/onemap/pie-style.png');
  background-size: 100% 100%;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
</style>
