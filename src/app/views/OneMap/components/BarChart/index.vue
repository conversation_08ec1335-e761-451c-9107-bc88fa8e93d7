<template>
  <!-- <div class="bar-chart" :style="{ marginBottom: gap }">
    <div class="chart-title">{{ props.title }}</div>
    <div ref="chartRef" :style="[pieStyle]"></div>
  </div> -->
  <div ref="chartRef" :style="[pieStyle]"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'

interface Props {
  title?: string
  centerText?: string
  data: Array<{ name: string; value: number }>
  options?: Partial<EChartsOption>
  width?: string
  height?: string
  gap?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  gap: '20px',
  options: () => ({})
})

const pieStyle = {
  width: props.width || '200px',
  height: props.height || '200px'
}

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) return
  chart = echarts.init(chartRef.value, undefined, { width: 'auto', height: 'auto' })
}

const updateChart = () => {
  if (!chart) return

  const maxValue = props.data.length > 0 ? Math.max(...props.data.map((item) => item.value)) : 0

  const showDataZoom = props.data.length > 6

  const defaultOption: EChartsOption = {
    textStyle: {
      color: '#A6C1D3',
      fontSize: 16
    },
    tooltip: {
      trigger: 'item',
      formatter: `{b}: {c}`
    },
    grid: {
      right: 30,
      top: 0,
      left: 0,
      bottom: 0,
      containLabel: true
    },
    xAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: 'rgba(166, 193, 211, 0.3)'
        }
      },
      axisLabel: {
        fontSize: 16
      },
      minInterval: 1
    },
    yAxis: {
      type: 'category',
      data: props.data.map((item) => item.name),
      axisLabel: {
        fontSize: 16,
        formatter: (value) => {
          return String(value).replace(/(.{6})/g, '$1\n')
        }
      }
    },
    dataZoom: showDataZoom
      ? [
          {
            show: true,
            type: 'slider',
            orient: 'vertical',
            filterMode: 'none',
            backgroundColor: 'rgba(0, 89, 154, 0.5)',
            fillerColor: '#22FFCC',
            moveHandleSize: 0,
            handleStyle: {
              opacity: 0
            },
            borderColor: 'rgba(0, 89, 154, 0)',
            showDataShadow: false,

            start: 75,
            end: 100,
            handleSize: 0,
            showDetail: false,
            brushSelect: false,
            zoomLock: false,
            width: 6,
            top: 'middle',
            right: 3
          },
          {
            type: 'inside',
            yAxisIndex: 0,
            zoomOnMouseWheel: false,
            moveOnMouseMove: true,
            moveOnMouseWheel: true
          }
        ]
      : [],

    series: [
      {
        type: 'bar',
        data: props.data.map((item) => item.value),
        barWidth: 25,
        itemStyle: {
          color: (params) => {
            const currentValue = props.data[params.dataIndex].value
            return currentValue === maxValue ? '#00E0AC' : '#00C2E1'
          }
        }
      }
    ]
  }

  const option = {
    ...defaultOption,
    ...props.options
  }

  chart.setOption(option)
}

// 监听数据变化
watch(
  () => [props.data, props.title, props.options],
  () => {
    updateChart()
  },
  { deep: true }
)

// 监听窗口大小变化
const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  updateChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.bar-chart {
  box-sizing: border-box;
  position: relative;
}
.chart-title {
  position: absolute;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  top: 10px;
  left: 10px;
}
</style>
