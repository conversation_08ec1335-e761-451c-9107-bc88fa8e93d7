<template>
  <div
    class="info-box"
    :class="[infoType === 0 ? 'normal-style' : 'icon-style']"
    :style="{ width: width, marginBottom: gap }"
  >
    <template v-if="infoType === 0">
      <div class="info-box-title title-box">
        <div class="title-text">{{ title }}</div>
        <div class="sub-title" @click="onClickSubTitle">{{ subTitle || '' }}</div>
      </div>
      <div class="content-box" :style="{ height: height }">
        <div class="slot-content">
          <slot></slot>
        </div>
      </div>
    </template>
    <template v-if="infoType === 1">
      <div class="info-box-title title-box">
        <div class="title-icon"></div>
        <div class="title-text">{{ title }}</div>
        <div class="sub-title" @click="onClickSubTitle">{{ subTitle || '' }}</div>
      </div>
      <div class="content-box" :style="{ height: height }">
        <div class="slot-content">
          <slot></slot>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'InfoBox' })

const emit = defineEmits(['update:open'])

interface Props {
  title: string
  subTitle?: string
  width?: string
  height?: string
  gap?: string
  infoType?: number
}

withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '100%',
  gap: '20px',
  infoType: 0 // 0 普通样式 1 图标样式
})

const onClickSubTitle = () => {
  emit('update:open', 'subTitleClicked')
}
</script>

<style lang="less" scoped>
.info-box {
  overflow: hidden;

  .title-icon {
    width: 22px;
    height: 33px;
    background-image: url('@/assets/images/onemap/title-icon.png');
    background-size: 100% 100%;
    margin-right: 5px;
  }
  // 渐变文字
  .title-text {
    font-size: 22px;
    font-weight: 500;
    color: transparent;
    background-image: linear-gradient(to bottom, #fff 20%, #00c1f6);
    -webkit-background-clip: text;
    background-clip: text;
    display: inline-block;
    position: relative;
  }
  .sub-title {
    font-size: 14px;
    color: #8bb4b9;
    margin-right: 100px;
  }

  .title-style-normal {
  }
  .info-box-title {
    width: 100%;
    display: flex;
    align-items: center;
  }
  .slot-content {
    height: 100%;
    overflow: auto;
  }
}
.normal-style {
  .title-box {
    height: 44px;
    padding-left: 11%;
    justify-content: space-between;
    color: #333;
    background-image: url('@/assets/images/onemap/title-box.png');
    background-size: 100% 100%;
    margin-bottom: 6px;
  }
  .content-box {
    background-image: url('@/assets/images/onemap/data-bg.png');
    background-size: 100% 100%;
    flex: 1;
    padding: 10px 14px;
  }
}
.icon-style {
  .title-style {
  }
  .content-box {
    background-color: rgba(5, 23, 30, 0.4);
    flex: 1;
    margin-top: 10px;
    overflow: auto;
  }
}
</style>
