import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LineLayer, PointLayer, Scene, GeometryLayer } from '@antv/l7'
import simplify from '@turf/simplify'

import mapboxgl from '@/common/mapbox-gl/mapbox-gl'
import { LngLatLike } from 'mapbox-gl'
mapboxgl.accessToken =
  'pk.eyJ1IjoidmFuamsiLCJhIjoiY2tuemR0MDlrMDI5YzJ2bGNuaThwNjg3ZiJ9.Ix5XwtD2nE4rpqZ6u1j55Q'

export const extrudeHeight = 200000

const tokenAry = [
  '7312e19f34ffbe026d8bcc9170b09b0e',
  'bb74c3ba588dab987a7dee5d30938ead',
  '2760a4b142a494210d757cc2d8358500',
  'c0032fdcdf378f6ab550aae9e5fb8495',
  '42c374a7d8836a1a7a5a4390d64d4226',
  'd2afb498a1b6923ac8759439202c1041',
  'fd807db71572159675e9617b45cb4108',
  '6a87ad7714e765da8107332559393fd1',
  'b48cc5d82c84b39d2bb171d537ea85c8'
]
const tiandituVecUrl = `https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}`

export function createMaskLayer(geojson: any, options) {
  return new PolygonLayer({ name: options.name, zIndex: options.zIndex || 0 })
    .color('rgba(0,0,0,0)')
    .shape('fill')
    .source(geojson)
    .style({
      raisingHeight: extrudeHeight * 0.5
    })
}

export function createBoundaryLayer(geojson: any, options) {
  const simplified = simplify(geojson, {
    tolerance: 0.001, // 简化容差，单位：度，越大越简
    highQuality: false, // 是否使用高质量算法（慢，但更平滑）
    mutate: false // 不修改原始数据
  })
  return new PolygonLayer({ name: options.name, zIndex: options.zIndex || 0 })
    .source(simplified)
    .color('#6befce')
    .shape('extrude')
    .size(extrudeHeight)
    .style({
      heightfixed: true,
      opacity: 0.6
      // mapTexture: `${import.meta.env.VITE_APP_URL}gis/bg_county.png`,
      // sourceColor: '#204143',
      // targetColor: '#fff'
    })
}

export function createBaseLayer(geojson: any, options) {
  return new PolygonLayer({ name: options.name, zIndex: options.zIndex || 0 })
    .source(geojson)
    .scale('density', {
      type: 'quantile'
    })
    .shape('fill')
    .color('rgba(0,0,0,0)')
    .style({
      raisingHeight: extrudeHeight * 0.5
    })
}

export function createLineLayer(geojson: any, options) {
  return new LineLayer({ name: options.name, zIndex: options.zIndex || 0 })
    .source(geojson)
    .color('#E5FFFF')
    .size(1.5)
    .style({
      raisingHeight: extrudeHeight
    })
}
// 添加县级标签
export function createTextLayer(dicts: any, options) {
  return new PointLayer({ name: options.name, zIndex: options.zIndex || 0 })
    .source(dicts, {
      parser: {
        type: 'json',
        x: 'x',
        y: 'y'
      }
    })
    .shape(options.key, 'text')
    .size(14)
    .color('#fff')
    .style({
      stroke: '#000',
      strokeWidth: 1,
      raisingHeight: extrudeHeight,
      textAllowOverlap: false
    })
}

export function createPointLayer(data: any, options) {
  return new PointLayer({ name: options.name, zIndex: options.zIndex || 0 })
    .source(data, {
      parser: {
        type: 'json',
        x: 'lon',
        y: 'lat'
      }
    })
    .shape(options.shape || 'point')
    .size(options.size || 40)
    .animate(options.animate || true)
    .color(options.color || null)
    .hide()
    .style({
      heightfixed: true,
      raisingHeight: extrudeHeight
    })
}

export function baseLayerSwitch(scene: Scene, show: boolean) {
  const layers = scene.getLayers()
  layers.forEach((layer) => {
    if (layer.name.startsWith('base')) {
      if (show) layer.show()
      else layer.hide()
    }
  })
}

export function pageLayerSwitch(scene: Scene, page?: string) {
  const layers = scene.getLayers()
  layers.forEach((layer) => {
    if (!layer.name.startsWith('base')) {
      if (!page) {
        layer.hide()
        return
      }
      if (layer.name.startsWith(page)) {
        layer.show()
      } else {
        layer.hide()
      }
    }
  })
}

// 初始化图层状态
export function initSceneLayerState(scene: Scene) {
  // 显示所有基础图层
  baseLayerSwitch(scene, true)
  // 隐藏相应的图层
  const countyLabel = scene.getLayerByName('base-county-label')
  countyLabel?.hide()
  // 设置相应图层初始属性
  const baseLayer = scene.getLayerByName('base-layer')
  baseLayer?.color('rgba(0,0,0,0)')
  const countyLine = scene.getLayerByName('base-county-line')
  countyLine?.size(1).color('rgba(255,255,255,0.2)')
}

export function mapOperateSwitch(map: any, bool: boolean) {
  if (bool) {
    // 禁止地图缩放
    map.dragRotate.enable()
    map.doubleClickZoom.enable()
    map.touchZoomRotate.enable()
    map.scrollZoom.enable()
    map.dragPan.enable()
  } else {
    // 禁止地图缩放
    map.dragRotate.disable()
    map.doubleClickZoom.disable()
    map.touchZoomRotate.disable()
    map.scrollZoom.disable()
    map.dragPan.disable()
  }
}

export const baseCenter: LngLatLike = [113.48328032298093, 31.00720445678138]
export const baseZoom = 6.8
export const basePitch = 45

// 初始化MapBox
export function initMapBox(mapContainer) {
  return new mapboxgl.Map({
    container: mapContainer as HTMLDivElement,
    // style: 'http://39.104.87.15/api/basemap/style/default.json',
    // style: 'mapbox://styles/mapbox/dark-v10',
    style: {
      version: 8,
      glyphs: `https://api.mapbox.com/fonts/v1/mapbox/{fontstack}/{range}.pbf?access_token=${tokenAry[Math.floor(Math.random() * tokenAry.length)]}`,
      sources: {
        'tianditu-vec': {
          type: 'raster',
          tiles: [tiandituVecUrl],
          tileSize: 256
        }
      },
      layers: [
        {
          id: 'tianditu-vec-layer',
          type: 'raster',
          source: 'tianditu-vec'
        }
      ]
    },
    center: baseCenter as LngLatLike, // 湖北省中心点
    zoom: baseZoom,
    pitch: basePitch,
    minZoom: 2,
    maxZoom: 15,
    // maxBounds: [
    //   [106.8627968, 27.5320531],
    //   [117.6323974, 34.7729932]
    // ]
    dragRotate: false // 禁用拖动旋转
  })
}

export const mapboxLayer = [
  {
    id: 'county-line-layer',
    type: 'line',
    source: 'county-line',
    paint: {
      'line-color': '#fff',
      'line-width': 2
    }
  },
  {
    id: 'county-labels-layer',
    type: 'symbol',
    source: 'county-labels',
    layout: {
      'text-field': ['get', 'name'], // 替换为你的字段名
      'text-size': 12,
      'symbol-placement': 'point',
      'text-anchor': 'center'
    },
    paint: {
      'text-color': '#fff',
      'text-halo-width': 2
    }
  },
  {
    id: 'city-line-layer',
    type: 'line',
    source: 'city-line',
    paint: {
      'line-color': '#fff',
      'line-width': 2
    }
  },
  {
    id: 'city-labels-layer',
    type: 'symbol',
    source: 'city-labels',
    layout: {
      'text-field': ['get', 'name'], // 替换为你的字段名
      'text-size': 12,
      'symbol-placement': 'point',
      'text-anchor': 'center'
    },
    paint: {
      'text-color': '#fff',
      'text-halo-width': 2
    }
  }
]
