import { LayerPopup, PointLayer, Scene } from '@antv/l7'
import { formDataApi } from '@gt/mis-components-web/apis'
import {
  createBaseLayer,
  createBoundaryLayer,
  createLineLayer,
  createMaskLayer,
  createPointLayer,
  createTextLayer,
  extrudeHeight
} from './useMapLayers'
import PlayerManager from '../utils/icc/PlayerManager'

// 引入图片
import mapMarkerIcon from '@/assets/images/onemap/map-marker.png'
import mapMarker1Icon from '@/assets/images/onemap/map-marker1.png'
import mapMarker2Icon from '@/assets/images/onemap/map-marker2.png'
import mapMarker3Icon from '@/assets/images/onemap/map-marker3.png'

interface InitFormDataOptions {
  /** 是否显示错误信息 */
  showError?: boolean
  /** 加载进度回调函数 */
  onProgress?: (completed: number, total: number) => void
}

/**
 * 初始化加载所有数据
 * @param {InitFormDataOptions} options - 可选配置参数
 * @returns {Promise<Record<string, any[]>>} 返回所有表单数据的映射对象
 */
export async function initFormData(options: InitFormDataOptions = { showError: true }) {
  const formNameList = [
    'protection_point_list',
    'wild_plant_list',
    'plant_distribution_list',
    'protection_point_monitoring',
    'protection_point_development',
    'protection_point_scientific',
    'protection_point_species_status',
    'wild_plant_info',
    'admin_city',
    'admin_county',
    'monitor_device'
  ]

  // 创建请求列表
  const requestList = formNameList.map((formName) => {
    return formDataApi
      .searchFormData(formName)
      .then((result) => ({
        status: 'fulfilled' as const,
        value: { ...result, formName }
      }))
      .catch((error) => {
        if (options.showError) {
          console.error(`加载表单数据 ${formName} 失败:`, error)
        }
        return {
          status: 'rejected' as const,
          reason: error,
          value: { list: [], total: 0, formName }
        }
      })
  })

  let completed = 0
  const total = requestList.length + 4 // 加上4个geojson文件

  // 使用 Promise.all 处理所有表单数据请求
  const formDataResults = await Promise.all(requestList)
  const data: Record<string, any[]> = {}

  formDataResults.forEach((result) => {
    completed++
    if (options.onProgress) {
      options.onProgress(completed, total)
    }

    data[result.value.formName] = result.value.list
  })

  // 表单数据请求完成后，再请求geojson数据
  const geojsonPromises = [
    fetch(`${import.meta.env.VITE_APP_URL}gis/protect_area.geojson`).then((res) => res.json()),
    fetch(`${import.meta.env.VITE_APP_URL}gis/province.geojson`).then((res) => res.json()),
    fetch(`${import.meta.env.VITE_APP_URL}gis/city.geojson`).then((res) => res.json()),
    fetch(`${import.meta.env.VITE_APP_URL}gis/county.geojson`).then((res) => res.json())
  ]

  const [protect_area, province, city, county] = await Promise.all(geojsonPromises)

  // 更新进度
  completed += 4
  if (options.onProgress) {
    options.onProgress(completed, total)
  }

  // 将geojson数据添加到返回结果中
  data.protect_area_geojson = protect_area
  data.province_geojson = province
  data.city_geojson = city
  data.county_geojson = county

  return data
}

export async function initSceneLayers(scene: Scene, data: any) {
  // 添加图标图片
  await addSceneImage(scene)
  // 获取geojson数据
  const city = data.city_geojson
  const county = data.county_geojson

  // 构造基础图层
  // 构造图层
  const maskLayer = createMaskLayer(county, { name: 'base-mask' })
  const boundaryLayer = createBoundaryLayer(city, { name: 'base-boundary' })
  const baseLayer = createBaseLayer(county, { name: 'base-layer' })
  const cityLineLayer = createLineLayer(city, { name: 'base-city-line' })
  const countyLineLayer = createLineLayer(county, { name: 'base-county-line' })
  const countyLabelLayer = createTextLayer(data.admin_county, {
    name: 'base-county-label',
    zIndex: 999,
    key: 'fname'
  })
  const cityLabelLayer = createTextLayer(data.admin_city, {
    name: 'base-city-label',
    zIndex: 999,
    key: 'cname'
  })
  countyLineLayer.size(1).color('rgba(255,255,255,0.2)')

  countyLabelLayer.hide()

  scene.addLayer(maskLayer)
  scene.addLayer(boundaryLayer)
  scene.addLayer(baseLayer)
  scene.addLayer(cityLineLayer)
  scene.addLayer(cityLabelLayer)
  scene.addLayer(countyLineLayer)
  scene.addLayer(countyLabelLayer)

  // 构造模块图层
  // 资源分布-保护点
  const resourceBhdPoint = createPointLayer(data.protection_point_list, {
    name: 'resource-bhd-point',
    zIndex: 3
  })
  scene.addLayer(resourceBhdPoint)
  // 资源分布-3d柱状图
  // 添加3d柱状数据
  const resourcePlantType = new PointLayer({
    name: 'resource-plant-type',
    zIndex: 100
  })
    .source([], {
      parser: {
        type: 'json',
        x: 'x',
        y: 'y'
      }
    })
    .animate(true)
    .hide()
    .shape('squareColumn')
    .size('value', function (level) {
      return [8, 8, level * 4 + extrudeHeight / 5000 + 5]
    })
    .color('#F3F48C')

  scene.addLayer(resourcePlantType)
  // 添加popup
  const ResourcePlantTypePopup = new LayerPopup({
    items: [
      {
        layer: resourcePlantType as any,
        customContent: (data: any) => {
          return `<span>${data.value}种</span>`
        }
      }
    ]
  })
  scene.addPopup(ResourcePlantTypePopup)

  // 保护点列表-保护点
  const protectBhdPoint = createPointLayer(data.protection_point_list, {
    name: 'protect-bhd-point',
    zIndex: 100
  })
  scene.addLayer(protectBhdPoint)
  const protectBhdPointNew = createPointLayer(data.protection_point_list, {
    name: 'protect-bhd-point-new',
    zIndex: 100,
    animate: true,
    shape: 'circle',
    color: '#F3F48C'
  })
  scene.addLayer(protectBhdPointNew)

  // 科学监测-保护点
  const scienceBhdPoint = createPointLayer([], {
    name: 'science-bhd-point',
    zIndex: 100,
    shape: 'point'
  })
  scene.addLayer(scienceBhdPoint)
  // 科学监测-科学考察
  const scienceSciencePoint = createPointLayer([], {
    name: 'science-science-point',
    zIndex: 100,
    shape: 'marker1'
  })
  scene.addLayer(scienceSciencePoint)
  // 科学监测-监测评估
  const scienceMonitorPoint = createPointLayer([], {
    name: 'science-monitor-point',
    zIndex: 100,
    shape: 'marker2'
  })
  scene.addLayer(scienceMonitorPoint)
  // 科学监测-即使科学考察又是监测评估
  const scienceSMPoint = createPointLayer([], {
    name: 'science-science-monitor-point',
    zIndex: 100,
    shape: 'marker3'
  })
  scene.addLayer(scienceSMPoint)
}

function addSceneImage(scene: Scene) {
  scene.addImage('point', mapMarkerIcon)
  scene.addImage('marker1', mapMarker1Icon)
  scene.addImage('marker2', mapMarker2Icon)
  scene.addImage('marker3', mapMarker3Icon)
}

export function createPlayerContainer(el, receiveMessageFromWSPlayer) {
  return new PlayerManager({
    el,
    prefixUrl: import.meta.env.DEV ? 'public' : import.meta.env.VITE_APP_URL.replace('/', ''),
    type: 'real' /** real - 实时预览  record - 录像回放 **/,
    maxNum: 6 /** 一个播放器上限能播放的路数，可根据实际情况设置，支持 1 4 9 16 25 **/,
    num: 6 /** 初始化，页面显示的路数 **/,
    showControl: true /** 是否显示工具栏，默认显示 **/,
    showIcons: {
      // 自定义按钮，需要的请配置true, 不需要的按钮请配置false，所有的按钮属性都要写上
      streamChangeSelect: true, // 主辅码流切换
      ivsIcon: false, // 控制智能帧按钮
      talkIcon: false, // 对讲功能按钮
      localRecordIcon: true, // 录制视频功能按钮
      audioIcon: true, // 开启关闭声音按钮
      snapshotIcon: true, // 抓图按钮
      closeIcon: true // 关闭视频按钮
    },
    openIvs: false, // true-开启智能帧/规则线/目标框, false-不显示
    ivsTypeArr: [], // 传入数组，支持显示的情况, 空表示没有智能信息，1-智能规则线 2-智能目标框
    showRecordProgressBar: true, // 录像回放时，录像的进度条是否需要
    useH265MSE: true, // true-表示硬解  false-软解 默认不传该字段
    picCapCb: true, // 是否需要抓图回调，true-抓图回调触发， false-直接浏览器下载抓图
    receiveMessageFromWSPlayer
  })
}
