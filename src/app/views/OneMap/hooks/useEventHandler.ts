// 根据最大值最小值划分区间
export function splitDataAvg(max: number, min: number, length = 5): any[] {
  const result: any[] = []
  const step = (max - min) / length

  for (let i = 0; i < length; i++) {
    const intervalMin = min + step * i
    const intervalMax = i === length - 1 ? max : min + step * (i + 1)
    result.push({
      min: Math.floor(intervalMin),
      max: Math.floor(intervalMax)
    })
  }
  return result
}

// 根据提供的最大最小值划分区间
export function splitData(list: any[], key: string, length = 5, color?: string[]): any[] {
  color = color
    ? color
    : [
        'rgba(0, 32, 34, 0.6)',
        'rgba(0, 65, 69, 0.6)',
        'rgba(0, 128, 137, 0.6)',
        'rgba(0, 162, 173, 0.6)',
        'rgba(21, 255, 252, 0.6)'
      ]

  const plant = new Map()
  list.forEach((item) => {
    if (plant.has(item[key])) {
      plant.get(item[key]).push(item)
    } else {
      plant.set(item[key], [item])
    }
  })
  const statistic = Array.from(plant).map(([key, value]: any[]) => {
    return {
      name: key,
      value: value.length,
      color: 'rgba(0,0,0,0)'
    }
  })
  if (statistic.length !== 0) {
    // 提取最大值和最小值
    const max = Math.max(...statistic.map((item) => item.value))
    const min = Math.min(...statistic.map((item) => item.value))
    // 计算区间
    const intervals = splitDataAvg(max, min, length)

    // 判断数据区间，取颜色值
    statistic.map((item) => {
      const index = intervals.findIndex(
        (interval) => item.value >= interval.min && item.value <= interval.max
      )
      item.color = color[index]
      return item
    })
  }
  return statistic
}
