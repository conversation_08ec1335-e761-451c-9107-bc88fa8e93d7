<template>
  <div>
    <div class="fixed top-0 left-0 right-0 bottom-0 w-full h-full bg-[#175F72] opacity-[0.9]"></div>
    <div class="fixed top-[270px] left-[18px] flex flex-col gap-2 text-map-simple-blue">
      <div
        v-for="(item, index) in tabList"
        :key="index"
        class="h-[150px] px-[12px] cursor-pointer [writing-mode:vertical-lr] tracking-[4px] font-bold text-base select-none"
        :style="{
          'background-image': `url(${activeTab === index ? activeImgs[index] : normalImgs[index]})`,
          'background-size': '100% 100%'
        }"
        :class="{ 'text-[#FFDFAE]': activeTab === index }"
        @click="activeTab = index"
      >
        <span class="pt-[60px]">{{ item.label }}</span>
      </div>
    </div>
    <div class="fixed top-[120px] bottom-[35px] left-[85px] right-[35px]">
      <Monitor v-show="activeTab === 0" />
      <!-- <Weather v-show="activeTab === 1" /> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'VideoMonitoring' })
import Monitor from './components/Monitor/index.vue'
import Weather from './components/Weather/index.vue'

import spjk from '@/assets/images/onemap/spjk.png'
import spjking from '@/assets/images/onemap/spjking.png'
import qxjk from '@/assets/images/onemap/qxjk.png'
import qxjking from '@/assets/images/onemap/qxjking.png'

const activeTab = ref(0)
const tabList = [
  { label: '视频监控', img: 'spjk', imging: 'spjking' }
  // { label: '气象监测', img: 'qxjk', imging: 'qxjking' }
]
const imageMap = {
  spjk,
  spjking,
  qxjk,
  qxjking
}
const normalImgs = tabList.map((item) => imageMap[item.img])
const activeImgs = tabList.map((item) => imageMap[item.imging])
</script>

<style lang="less" scoped>
.left-container {
  position: fixed;
  top: 120px;
  left: 40px;
  z-index: 999;
  width: 330px;

  display: flex;
  flex-direction: column;
  gap: 10px;
}
</style>
