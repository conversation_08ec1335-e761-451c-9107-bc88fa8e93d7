<template>
  <div class="flex gap-[30px]">
    <div class="w-[468px] h-full flex flex-col gap-[20px]">
      <InfoBox title="设备状态" height="525px" :infoType="1">
        <div class="flex flex-col p-[19px]">
          <div class="flex">
            <div
              v-for="(item, index) in deviceState"
              :key="index"
              class="flex items-center w-1/2 h-[60px]"
              :style="{
                'background-image': `url(${item.img})`,
                'background-size': '100% 100%'
              }"
            >
              <div class="flex items-end justfity-center pl-[85px] text-map-simple-blue">
                <div class="text-base">{{ item.label }}</div>
                <p class="pl-[12px]">
                  <span
                    class="text-number font-bold leading-none"
                    :class="{
                      'text-[#13D1C1]': index === 0,
                      'text-[#FFF88B]': index === 1
                    }"
                  >
                    {{ item.value }}</span
                  >
                  <span class="pl-[8px] text-base">个</span>
                </p>
              </div>
            </div>
          </div>
          <div class="flex-1">
            <div>
              <div class="text-[#EAFEFF] text-base pt-[15px] pb-[10px]">原生境保护点列表</div>
              <a-input v-model="searchVal" size="large" placeholder="请输入关键字">
                <template #suffix>
                  <img :src="searchImg" style="width: 17px; height: 17px" @click="handleSearch" />
                </template>
              </a-input>
            </div>
            <div class="mt-[10px] max-h-[320px] overflow-y-auto">
              <div v-for="(item, index) in show_data" :key="index">
                <div
                  class="flex items-center w-full bg-[rgba(54,166,186,0.6)] py-[14px] px-[17px] mt-[5px] box-sizing cursor-pointer text-base text-[#EAFEFF] transition-colors duration-200"
                  @click="handleItemClick(index)"
                >
                  <div class="flex justify-between items-center w-full">
                    <span class="mr-[10px] line-clamp-1">{{ item.bhdmc }}</span>
                    <img
                      :src="currentExpandedIndex === index ? retractImg : expandImg"
                      alt=""
                      :style="{
                        width: currentExpandedIndex === index ? '20px' : '10px',
                        height: currentExpandedIndex === index ? '10px' : '20px'
                      }"
                    />
                  </div>
                </div>

                <div
                  v-if="currentExpandedIndex === index && item.devices.length > 0"
                  class="w-full py-[14px] px-[17px] mt-[2px] box-sizing text-base text-[#EAFEFF]"
                >
                  <div
                    class="flex justify-between items-center mb-[10px]"
                    v-for="device in item.devices"
                    :key="device.device_code"
                    @click="realPlay(device)"
                  >
                    <div class="flex items-center gap-1">
                      <img :src="monitorImg" alt="" style="width: 18px; height: 20px" />
                      <p>{{ device.device_name }}</p>
                    </div>

                    <div class="flex items-center gap-2">
                      <span
                        class="w-2.5 h-2.5 rounded-full"
                        :style="{
                          'background-color': device.info.isOnline === 1 ? '#13D1C1' : '#8BB4B9'
                        }"
                      ></span>
                      <span>{{ device.info.isOnline === 1 ? '在线' : '离线' }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </InfoBox>

      <InfoBox title="历史数据" height="290px" :infoType="1">
        <div class="p-[20px]">
          <div>
            <a-range-picker
              popupClassName="onemap-picker-panel"
              class="w-full"
              size="large"
              @change="onRangeChange"
            >
              <template #suffixIcon>
                <img :src="timeImg" alt="" style="width: 17px; height: 17px" />
              </template>
            </a-range-picker>
          </div>

          <div class="flex flex-nowrap gap-2 overflow-x-auto mt-[10px]">
            <div
              v-for="(item, index) in historyData"
              :key="index"
              @click="selected = item.field"
              class="px-2 py-2 text-[#fff] text-center transition-all duration-300 ease-in-out cursor-pointer relative border-b-1 border-[#8BB4B9]"
            >
              <div class="text-center" :class="{ 'text-[#00B3C4]': selected === item.field }">
                {{ item.lebal }}
              </div>

              <div
                v-if="selected === item.field"
                class="absolute bottom-0 left-1/2 -translate-x-1/2 h-1 w-full bg-[#00B3C4]"
              ></div>
            </div>
          </div>
        </div>
      </InfoBox>
    </div>

    <div class="flex-1">
      <InfoBox title="实时监控" height="900px" :infoType="1">
        <div class="h-full w-full">
          <div class="rounded-sm overflow-hidden" id="monitor-video"></div>
        </div>
      </InfoBox>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'Monitor' })
import InfoBox from '@/app/views/OneMap/components/InfoBox/index.vue'
import { formDataKey } from '@/app/views/OneMap/config'
import PlayerManager from '@/app/views/OneMap/utils/icc/PlayerManager'
import monitorApi from '@/apis/monitor'

import searchImg from '@/assets/images/onemap/search.png'
import retractImg from '@/assets/images/onemap/retract.png'
import expandImg from '@/assets/images/onemap/expand.png'
import monitorImg from '@/assets/images/onemap/monitor.png'
import timeImg from '@/assets/images/onemap/time.png'
import card_7 from '@/assets/images/onemap/card_7.png'
import card_8 from '@/assets/images/onemap/card_8.png'
import { useOneMapStore } from '@/stores'
import { createPlayerContainer } from '@/app/views/OneMap/hooks/useMainHandler'

const formData = inject(formDataKey)

const oneMapStore = useOneMapStore()

const deviceState = computed(() => {
  const online = show_data.value.reduce((pre, cur) => {
    return pre + cur.devices.filter((item) => item.info.isOnline === 1).length
  }, 0)
  const offline = show_data.value.reduce((pre, cur) => {
    return pre + cur.devices.filter((item) => item.info.isOnline === 0).length
  }, 0)
  return [
    {
      label: '在线',
      img: card_7,
      value: online
    },
    {
      label: '离线',
      img: card_8,
      value: offline
    }
  ]
})

const bhd_data = ref<any[]>([])
const show_data = ref<any[]>([])

onMounted(async () => {
  const token = await monitorApi.authToken()
  localStorage.setItem('dhToken', token)
  getDeviceList()
})

const searchVal = ref()
function handleSearch() {
  if (searchVal.value) {
    show_data.value = bhd_data.value.filter((item) => item.bhdmc.includes(searchVal.value))
  } else {
    show_data.value = bhd_data.value
  }
}

async function getDeviceList() {
  try {
    const list = await monitorApi.getDeviceList({
      pageNum: 1,
      pageSize: 10,
      ownerCode: '001',
      showChildNodeData: 1,
      syncVirtualData: 0
    })

    // 处理保护点列表以及在线情况
    const points = formData?.value.protection_point_list
    const devices = formData?.value.monitor_device
    const ary: any = []

    points.forEach((item) => {
      let device_list = devices.filter((items) => item.bhdbm == items.bhdbm)
      if (device_list.length > 0) {
        // 匹配相应的设备信息
        device_list.map((items) => {
          let info = list.pageData.find((device) => items.device_code == device.deviceCode)
          items.info = info ? info : null
          return items
        })
      }
      ary.push({
        bhdmc: item.bhdmc,
        bhdbm: item.bhdbm,
        devices: device_list
      })
    })
    bhd_data.value = ary
    show_data.value = ary
    console.log('bhd_data', bhd_data.value)
  } catch (error) {
    console.log(error)
  }
}

const realPlayer = ref<PlayerManager>()
const selectPlayerWindow = ref(0)
const init_completed = ref(false)
async function initVideo() {
  if (init_completed.value) return
  realPlayer.value = createPlayerContainer('monitor-video', (methods, data, err) => {
    switch (methods) {
      case 'initializationCompleted':
        console.log('播放器组件初始化完成')
        break
      case 'selectWindowChanged':
        selectPlayerWindow.value = data.playIndex
        break
    }
  })
  // 默认播放6个
  let online_device: string[] = []
  bhd_data.value.forEach((item) => {
    item.devices.forEach((items) => {
      if (items.info.isOnline === 1) {
        if (online_device.length < 6) online_device.push(items)
      }
    })
  })

  online_device.forEach((item, index) => {
    realPlay(item, index)
  })
  // 初始化完成
  init_completed.value = true
}

async function realPlay(device, play_window = selectPlayerWindow.value) {
  const result: any = await getDeviceUrl(device.device_code)
  const videoUrl = result.url.split('|')[1] + `?token=${result.token}`
  console.log('videoUrl', videoUrl)
  console.log('realPlayer.value', realPlayer.value)
  const channelId = device.channel_id ? device.channel_id : device.device_code + '$1$0$0'
  realPlayer.value?.realByUrl({
    wsURL: 'ws://223.75.52.103:9100/', // 建立的websocket连接地址, 必传, 根据返回的rtsp的流地址进行拼接，可在demo进行查看
    rtspURL: videoUrl, // 接口返回的rtsp地址
    channelId,
    selectIndex: play_window,
    playerAdapter: 'stretching'
    // channelData: {
    //   id: '1000002$1$0$0',
    //   deviceCode: '1000002',
    //   deviceType: 2,
    //   channelSeq: '0',
    //   cameraType: 2,
    //   capability: ''
    // }
  })
}

async function getDeviceUrl(deviceId: string) {
  try {
    const list = await monitorApi.getVideoList(`${deviceId}$1$0$0`)
    return list
  } catch (err) {
    console.log('getVideoList', err)
  }
}

const onRangeChange = (date, dateString) => {
  console.log(date, dateString)
}

const currentExpandedIndex = ref(-1) // -1表示未展开任何项

// 处理列表项点击事件
const handleItemClick = (index: number) => {
  currentExpandedIndex.value = currentExpandedIndex.value === index ? -1 : index
}

const selected = ref('alarm')
const historyData = [
  {
    lebal: '最近告警',
    field: 'alarm'
  },
  {
    lebal: '历史数据',
    field: 'history'
  }
]

watch(
  () => oneMapStore.pageCurrent,
  (val) => {
    if (val === 3) initVideo()
  }
)
</script>

<style lang="less" scoped>
:deep(.ant-input-affix-wrapper) {
  background-color: #043e53;
  border-color: #3bb8c1;
  .ant-input {
    background-color: #043e53;
    color: #eafeff !important;
    &::placeholder {
      color: #eafeff;
    }
  }
}

:deep(.ant-picker) {
  background-color: #043e53;
  border-color: #3bb8c1;
  color: #eafeff;
  .ant-picker-input input::placeholder {
    color: #eafeff;
  }
  .ant-picker-input > input {
    color: #eafeff;
  }

  .ant-picker-range-separator {
    font-size: 0;

    svg {
      color: #eafeff;
    }
  }

  .ant-picker-clear {
    svg {
      width: 20px;
      height: 20px;
    }
  }
}
</style>
