<template>
  <div>
    <div class="w-[330px] fixed top-[150px] left-[40px]">
      <ProtectPointSelect v-if="bhdColumns.length > 0" :list="bhdColumns" @change="changePoint" />
    </div>
    <div class="fixed top-[150px] bottom-[30px] my-auto right-[40px]">
      <TimelineData
        v-if="pointInfo"
        :data="pointInfo"
        @toDetail="toDetail"
        @changeType="changeType"
      />
      <div class="fixed bottom-10 left-10 p-2">
        <div v-for="item in legendData" :key="item.label" class="flex items-center mb-2">
          <div class="flex items-center">
            <img class="w-[30px]" :src="item.img" />
            <div class="mx-2 text-map-simple-blue text-sm">{{ item.label }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'ScientificMonitoring' })
import ProtectPointSelect from '../../components/ProtectPointSelect/index.vue'
import TimelineData from './components/TimelineData/index.vue'
import { Scene } from '@antv/l7'
import { sceneKey, mapRefKey, formDataKey } from '@/app/views/OneMap/config'
import { useOneMapStore } from '@/stores'
import mapMarkerIcon from '@/assets/images/onemap/map-marker.png'
import mapMarker1Icon from '@/assets/images/onemap/map-marker1.png'
import mapMarker2Icon from '@/assets/images/onemap/map-marker2.png'
import mapMarker3Icon from '@/assets/images/onemap/map-marker3.png'

import { pageLayerSwitch } from '../../hooks/useMapLayers'

const oneMapStore = useOneMapStore()

const scene = inject(sceneKey) as Ref<Scene>
const formData = inject(formDataKey) as any
const mapRef: any = inject(mapRefKey)
const bhd_list = ref<any[]>([])
const scientific_list = ref<any[]>([])
const monitoring_list = ref<any[]>([])
const scientific_and_monitoring_list = ref<any[]>([])

// 图层

const bhd_layer = scene.value.getLayerByName('science-bhd-point')
const scientific_layer = scene.value.getLayerByName('science-science-point')
const monitoring_layer = scene.value.getLayerByName('science-monitor-point')
const scienceMonitorLayer = scene.value.getLayerByName('science-science-monitor-point')

const bhdColumns = computed(() => {
  return [
    ...bhd_list.value,
    ...scientific_list.value,
    ...monitoring_list.value,
    ...scientific_and_monitoring_list.value
  ]
})

const pointInfo = ref<any>({
  scientific: [],
  monitoring: []
})

function toDetail(item: any) {
  console.log('跳转详情', item)
  oneMapStore.setProtectPointId(item.bhdbm)
  oneMapStore.pageStatePush({
    page: 2
  })
  oneMapStore.setPageCurrent(1)
}

function changeType(type: string) {
  console.log('type', type)

  const bhd_layer: any = scene.value.getLayerByName('science-bhd-point')
  const scientific_layer: any = scene.value.getLayerByName('science-science-point')
  const monitoring_layer: any = scene.value.getLayerByName('science-monitor-point')
  const scientific_and_monitoring_layer: any = scene.value.getLayerByName(
    'science-science-monitor-point'
  )

  // 先全部隐藏
  bhd_layer?.hide()
  scientific_layer?.hide()
  monitoring_layer?.hide()
  scientific_and_monitoring_layer?.hide()

  // 只显示对应类型
  if (type === 'scientific') {
    const allBhdbm = scientific_and_monitoring_list.value.map((item) => item.bhdbm)
    scientific_layer?.filter('bhdbm', (val) => !allBhdbm.includes(val))
    scientific_layer?.show()

    scientific_and_monitoring_layer?.filter(true)
    scientific_and_monitoring_layer?.show()
  } else if (type === 'monitoring') {
    const allBhdbm = scientific_and_monitoring_list.value.map((item) => item.bhdbm)
    monitoring_layer?.filter('bhdbm', (val) => !allBhdbm.includes(val))
    monitoring_layer?.show()

    scientific_and_monitoring_layer?.filter(true)
    scientific_and_monitoring_layer?.show()
  } else {
    // 全部显示，且处理重复点过滤
    bhd_layer?.filter(true)
    bhd_layer?.show()
    scientific_layer?.filter(true)
    scientific_layer?.show()
    monitoring_layer?.filter(true)
    monitoring_layer?.show()
    if (scientific_and_monitoring_layer) {
      filterMonitorPointAllLayer(scientific_and_monitoring_list.value)
      scientific_and_monitoring_layer.filter(true)
      scientific_and_monitoring_layer.show()
    }
  }

  scene.value.render()
}

async function getFormData() {
  const bhd = formData.value.protection_point_list
  const scientific = formData.value.protection_point_scientific
  const monitoring = formData.value.protection_point_monitoring

  pointInfo.value = {
    scientific,
    monitoring
  }

  let bhd_point: any[] = []
  let scientific_point: any[] = []
  let monitoring_point: any[] = []
  let scientific_and_monitoring_point: any[] = []
  bhd.forEach((item: any) => {
    let scientificInfo = scientific.find((items: any) => items.bhdbm === item.bhdbm)
    let monitoringInfo = monitoring.find((items: any) => items.bhdbm === item.bhdbm)

    if (scientificInfo && monitoringInfo) {
      scientific_and_monitoring_point.push(item)
    }
    if (scientificInfo) {
      scientific_point.push(item)
    }
    if (monitoringInfo) {
      monitoring_point.push(item)
    }
    if (!monitoringInfo && !scientificInfo) {
      bhd_point.push(item)
    }
  })

  bhd_list.value = bhd_point
  scientific_list.value = scientific_point
  monitoring_list.value = monitoring_point
  scientific_and_monitoring_list.value = scientific_and_monitoring_point
}

function changePoint(bm: string) {
  let bhd_layer: any = scene.value.getLayerByName('science-bhd-point')
  let scientific_layer: any = scene.value.getLayerByName('science-science-point')
  let monitoring_layer: any = scene.value.getLayerByName('science-monitor-point')
  let scientific_and_monitoring_layer: any = scene.value.getLayerByName(
    'science-science-monitor-point'
  )

  if (scientific_and_monitoring_layer) {
    filterMonitorPointAllLayer(scientific_and_monitoring_list.value)
  }
  if (bm) {
    if (scientific_and_monitoring_layer) {
      scientific_and_monitoring_layer.filter('bhdbm', (val) => val === bm)
    }
    if (scientific_layer) {
      const allBhdbm = scientific_and_monitoring_list.value.map((item) => item.bhdbm)
      scientific_layer.filter('bhdbm', (val) => val === bm && !allBhdbm.includes(val))
    }
    if (monitoring_layer) {
      const allBhdbm = scientific_and_monitoring_list.value.map((item) => item.bhdbm)
      monitoring_layer.filter('bhdbm', (val) => val === bm && !allBhdbm.includes(val))
    }
    if (bhd_layer) {
      bhd_layer.filter('bhdbm', (val) => val === bm)
    }
  } else {
    if (scientific_layer) scientific_layer.filter(true)
    if (monitoring_layer) monitoring_layer.filter(true)
    if (bhd_layer) bhd_layer.filter(true)
    if (scientific_and_monitoring_layer) {
      filterMonitorPointAllLayer(scientific_and_monitoring_list.value)
      scientific_and_monitoring_layer.filter(true)
    }
  }
  scene.value.render()
}

// 过滤掉存在于 scientific_layer 、monitoring_layer以及 bhd 中的点
function filterMonitorPointAllLayer(scientific_and_monitoring_point: any[]) {
  const bhd_layer: any = scene.value.getLayerByName('science-bhd-point')
  const scientific_layer: any = scene.value.getLayerByName('science-science-point')
  const monitoring_layer: any = scene.value.getLayerByName('science-monitor-point')
  const allBhdbm = scientific_and_monitoring_point.map((item) => item.bhdbm)

  if (bhd_layer) {
    bhd_layer.filter('bhdbm', (val) => !allBhdbm.includes(val))
  }
  if (scientific_layer) {
    scientific_layer.filter('bhdbm', (val) => !allBhdbm.includes(val))
  }
  if (monitoring_layer) {
    monitoring_layer.filter('bhdbm', (val) => !allBhdbm.includes(val))
  }
}

function addPoint() {
  if (bhd_list.value.length != 0) {
    bhd_layer?.setData(bhd_list.value)
    addPointClick(bhd_layer)
  }

  if (scientific_list.value.length != 0) {
    scientific_layer?.setData(scientific_list.value)
    addPointClick(scientific_layer)
  }

  if (monitoring_list.value.length != 0) {
    monitoring_layer?.setData(monitoring_list.value)
    addPointClick(monitoring_layer)
  }

  if (scientific_and_monitoring_list.value.length != 0) {
    filterMonitorPointAllLayer(scientific_and_monitoring_list.value)
    scienceMonitorLayer?.setData(scientific_and_monitoring_list.value)
    addPointClick(scienceMonitorLayer)
  }
}

function addPointClick(layer) {
  layer.on('click', (e: any) => {
    oneMapStore.setProtectPointId(e.feature.bhdbm)
    // 记录状态
    oneMapStore.pageStatePush({
      page: 2
    })
    oneMapStore.setPageCurrent(1)
  })
}

onMounted(async () => {
  await getFormData()

  addPoint()
})
// 定义图例数据
const legendData = [
  { label: '科学考察', img: mapMarker1Icon },
  { label: '监测评估', img: mapMarker2Icon },
  { label: '科学考察与监测评估', img: mapMarker3Icon },
  { label: '原生境保护点', img: mapMarkerIcon }
]

function layerEvent() {
  const baseLayer: any = scene.value.getLayerByName('base-layer')
  baseLayer.on('click', () => {
    if (oneMapStore.pageCurrent !== 2) return
    console.log('触发monitor点击事件')
  })
}

function initPageState() {
  let pageState = oneMapStore.pageState
  // 显示隐藏的标点
  pageLayerSwitch(scene.value, 'science')
  // 初始化图层点击事件
  layerEvent()
  if (pageState) {
    // 处理相应的状态显示
  } else {
    console.log('设置专题图')
    mapRef.value.setThemeLayer()
  }
}

watch(
  () => oneMapStore.pageCurrent,
  (val) => {
    if (val === 2) {
      initPageState()
    }
  }
)
</script>

<style lang="less" scoped>
.triangle {
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}

.hexagram-clip {
  clip-path: polygon(
    50% 0%,
    61% 35%,
    98% 35%,
    68% 57%,
    79% 91%,
    50% 70%,
    21% 91%,
    32% 57%,
    2% 35%,
    39% 35%
  );
}

.triangle-clip {
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}
</style>
