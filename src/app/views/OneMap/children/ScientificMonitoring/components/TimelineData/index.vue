<template>
  <InfoBox title="科学考察与监测评估工作开展情况" width="500px" height="850px">
    <div class="flex my-2 mx-2 select-none">
      <div
        v-for="(item, index) in baseInfo"
        :key="index"
        class="flex items-center w-1/2 h-[61px] shadow-lg"
        :style="{
          'background-image': `url(${Imgs[index]})`,
          'background-size': '100% 100%'
        }"
      >
        <div
          class="flex items-center pl-[77px] text-base text-map-simple-blue"
          @click="handleClick(item.cilckType)"
        >
          <div class="w-[40px]">{{ item.label }}</div>
          <p class="pl-4">
            <span
              class="font-bold text-number"
              :class="{
                'text-[#6AC5FF]': index === 0,
                'text-[#13D1C1]': index === 1
              }"
              >{{ item.field === 'scientificCount' ? scientificCount : monitoringCount }}</span
            >
            <span class="ml-2">个</span>
          </p>
        </div>
      </div>
    </div>

    <div class="mt-[20px] flex-1 px-[12px] py-[10px] max-h-[710px] overflow-y-auto">
      <a-timeline>
        <template v-for="(items, year) in groupedData" :key="year">
          <a-timeline-item color="#34B6FA">
            <template #dot
              ><div
                class="w-[17px] h-[17px] border-2 border-map-simple-blue bg-[#34B6FA] rounded-full flex items-center justify-center text-base"
              ></div
            ></template>
            <div class="year-box">
              {{ year === '未知' ? '未知' : year + '年' }}
            </div>
          </a-timeline-item>
          <a-timeline-item
            v-for="item in items"
            :key="item._id"
            :color="
              Object.prototype.hasOwnProperty.call(item, 'kxkcsj_start') ? '#00B4E8' : '#22C5A8'
            "
          >
            <template #dot
              ><div
                class="w-[15px] h-[15px] border-2 border-[#001916] rounded-full flex items-center justify-center"
                :class="{
                  'bg-[#00B4E8]': Object.prototype.hasOwnProperty.call(item, 'kxkcsj_start'),
                  'bg-[#22C5A8]': !Object.prototype.hasOwnProperty.call(item, 'kxkcsj_start')
                }"
              ></div
            ></template>
            <div
              class="h-[55px] relative pl-[70px] text-map-simple-blue"
              :style="{
                backgroundImage: `url(${Object.prototype.hasOwnProperty.call(item, 'kxkcsj_start') ? Imgs[0] : Imgs[1]})`,
                backgroundSize: '200px 100%',
                backgroundPosition: 'left center',
                backgroundRepeat: 'no-repeat'
              }"
              @click="$emit('toDetail', item)"
            >
              <div class="flex items-center h-full text-left absolute top-0 text-base">
                <span>{{ item.bhd_mc }}</span>
              </div>
            </div>
          </a-timeline-item>
        </template>
      </a-timeline>
    </div>
  </InfoBox>
</template>

<script lang="ts" setup>
import InfoBox from '../../../../components/InfoBox/index.vue'
import card_5 from '@/assets/images/onemap/card_5.png'
import card_6 from '@/assets/images/onemap/card_6.png'

interface Props {
  data: { scientific: any[]; monitoring: any[] }
}
const props = defineProps<Props>()

const emit = defineEmits(['changeType'])
// 响应式数据
const groupedData = ref<Record<string, any[]>>({})
const scientificList = ref<any[]>([])
const monitoringList = ref<any[]>([])
const scientificCount = ref(0)
const monitoringCount = ref(0)
const selectedType = ref<string | null>(null) // 当前选中的类型

const baseInfo = [
  {
    label: '科学考察',
    field: 'scientificCount',
    cilckType: 'scientific',
    img: 'card_5'
  },
  {
    label: '监测评估',
    field: 'monitoringCount',
    cilckType: 'monitoring',
    img: 'card_6'
  }
]
const imageMap = {
  card_5,
  card_6
}
const Imgs = baseInfo.map((item) => imageMap[item.img])

// 按年份分组并排序
const groupAndSortByYear = (data: any[]) => {
  const grouped = data.reduce(
    (acc, item) => {
      let year = item.year
      if (year === '' || year === null || year === undefined) {
        year = '未知'
      }
      if (!acc[year]) acc[year] = []
      acc[year].push(item)
      return acc
    },
    {} as Record<string, any[]>
  )

  return Object.keys(grouped)
    .sort((a, b) => Number(b) - Number(a)) // 按年份降序排序
    .reduce(
      (sorted, year) => {
        sorted[year] = grouped[year]
        return sorted
      },
      {} as Record<string, any[]>
    )
}

// 点击事件
const handleClick = (type: string) => {
  if (selectedType.value === type) {
    // 如果再次点击同一个类型，则取消选中
    selectedType.value = null
    groupedData.value = groupAndSortByYear([...scientificList.value, ...monitoringList.value])
  } else {
    // 选中新的类型
    selectedType.value = type
    groupedData.value =
      type === 'scientific'
        ? groupAndSortByYear(scientificList.value)
        : groupAndSortByYear(monitoringList.value)
  }
  emit('changeType', selectedType.value)
}

watch(
  () => props.data,
  (newData) => {
    if (newData) {
      const { monitoring, scientific } = newData
      scientificList.value = scientific || []
      monitoringList.value = monitoring || []
      scientificCount.value = scientific?.length || 0
      monitoringCount.value = monitoring?.length || 0
      groupedData.value = groupAndSortByYear([...scientificList.value, ...monitoringList.value])
      console.log('groupedData.value', groupedData.value)
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.year-box {
  width: 84px;
  height: 31px;
  background: #34b6fa;
  border-radius: 2px;
  border: 1px solid #34b6fa;
  border-left: none;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  color: #fff;
  font-size: 18px;
  transform: translateY(25%);
}

.year-box::before {
  content: '';
  position: absolute;
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-right: 6px solid #34b6fa;
}

:deep(.ant-timeline-item-head) {
  background-color: transparent !important;
}

:deep(.ant-timeline-item) {
  padding-bottom: 25px !important;
}

:deep(.ant-timeline-item-tail) {
  border-inline-start: 2px dashed #a6c1d3 !important;
}

:deep(.ant-timeline-item-head-custom) {
  transform: translate(-50%, 0%) !important;
}
</style>
