<template>
  <div v-if="pageLoad">
    <div class="fixed top-[150px] left-10 z-[999] flex flex-col gap-2.5">
      <div class="w-[330px]">
        <ProtectPointSelect v-if="!pointId" :list="bhd_data" @change="changePoint" />
      </div>

      <div v-if="pointId" class="fixed top-[110px] h-[50px] left-[600px] right-0 flex">
        <div class="flex items-center rounded-lg">
          <div class="flex items-center cursor-pointer" @click="backState">
            <SvgIcon class="ml-4 text-map-light-blue" icon="ArrowLeftOutlined" />
            <div class="text-map-light-blue text-[22px] ml-2">返回</div>
          </div>
          <div class="text-white mx-4">丨</div>
          <div class="text-[22px] text-white" style="text-shadow: 0 0 2 #000">
            {{ pointName }}
          </div>
        </div>
      </div>
    </div>
    <div v-if="!pointId">
      <div class="fixed top-[150px] right-10 z-[999]">
        <ChartData :data="bhd_data" />
      </div>
      <!-- 播放步骤条 -->
      <Steps
        :step-current="stepCurrent"
        :steps-year="stepsYear"
        :playing="playing"
        @play-step="playStep"
      />

      <div v-if="playing" class="fixed z-[1000] top-[125px] left-0 bottom-0 right-0"></div>
    </div>
    <div v-else>
      <div class="fixed top-[270px] left-10 flex flex-col gap-2 text-map-simple-blue">
        <div
          v-for="(item, index) in tabList"
          :key="index"
          class="h-[150px] px-[12px] cursor-pointer [writing-mode:vertical-lr] tracking-[4px] font-bold text-[18px] select-none"
          :style="{
            'background-image': `url(${activeTab === index ? activeImgs[index] : normalImgs[index]})`,
            'background-size': '100% 100%'
          }"
          :class="{ 'text-[#FFDFAE]': activeTab === index }"
          @click="activeTab = index"
        >
          <span class="pt-[60px]">{{ item.label }}</span>
        </div>
      </div>
      <div class="fixed top-[150px] right-10 z-[999]">
        <Species v-if="activeTab === 0" :pointId="pointId" />
        <Build v-else-if="activeTab === 1" :pointId="pointId" />
        <Inquiry v-else-if="activeTab === 2" :pointId="pointId" />
        <Monitor v-else-if="activeTab === 3" :pointId="pointId" />
      </div>
    </div>

    <div v-if="pointId" class="mt-2 pl-[10px] fixed left-[100px] bottom-[30px]">
      <div class="text-map-simple-blue text-base mb-2">保护区类型</div>
      <div class="flex items-center" v-for="(value, key) in layerColorMap" :key="key">
        <div class="w-[14px] h-[8px]" :style="{ 'background-color': value }"></div>
        <div class="ml-4 text-map-simple-blue text-base">{{ key }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'PointList' })
import ProtectPointSelect from '../../components/ProtectPointSelect/index.vue'
import ChartData from './components/ChartData/index.vue'
import Build from './components/Build/index.vue'
import Species from './components/Species/index.vue'
import Inquiry from './components/Inquiry/index.vue'
import Monitor from './components/Monitor/index.vue'
import Steps from './components/Steps/index.vue'

import { useOneMapStore } from '@/stores'
import { sceneKey, mapKey, mapRefKey } from '@/app/views/OneMap/config'
import wzzk from '@/assets/images/onemap/wzzk.png'
import wzzking from '@/assets/images/onemap/wzzking.png'
import jsqk from '@/assets/images/onemap/jsqk.png'
import jsqking from '@/assets/images/onemap/jsqking.png'
import dcpg from '@/assets/images/onemap/dcpg.png'
import dcpging from '@/assets/images/onemap/dcpging.png'
import spjk from '@/assets/images/onemap/spjk.png'
import spjking from '@/assets/images/onemap/spjking.png'

import { formDataKey } from '@/app/views/OneMap/config'
import { initSceneLayerState, pageLayerSwitch } from '../../hooks/useMapLayers'

const imageMap = {
  wzzk,
  wzzking,
  jsqk,
  jsqking,
  dcpg,
  dcpging,
  spjk,
  spjking
}

const oneMapStore = useOneMapStore()
const pageLoad = ref(false)

const formData = inject(formDataKey)

onMounted(async () => {
  await getFormData()
  addPoint()
  pageLoad.value = true
})

function initPageState() {
  let pageState = oneMapStore.pageState

  if (pageState && pageState.page === 1) {
    // 处理相应的状态显示
  } else {
    if (oneMapStore.selectedProtectPointId) {
      changePoint(oneMapStore.selectedProtectPointId)
    } else {
      playing.value = false
      pointId.value = null
      activeTab.value = 0
      initSceneLayerState(scene.value)
      pageLayerSwitch(scene.value, 'protect')
      playStep()
    }
  }
}

function backState() {
  let state = oneMapStore.getLastPageState()

  oneMapStore.clearProtectPointId()
  mapRef.value?.setThemeLayer()
  if (state && state.page !== 1) {
    oneMapStore.setPageCurrent(state.page)
  } else {
    activeTab.value = 0
    pointId.value = ''
    let layer = scene.value.getLayerByName('protect-bhd-point')
    layer.show()
  }
}

const tabList = [
  { label: '物种状况', img: 'wzzk', imging: 'wzzking' },
  { label: '建设情况', img: 'jsqk', imging: 'jsqking' },
  { label: '调查评估', img: 'dcpg', imging: 'dcpging' },
  { label: '视频监控', img: 'spjk', imging: 'spjking' }
]

const normalImgs = tabList.map((item) => imageMap[item.img])
const activeImgs = tabList.map((item) => imageMap[item.imging])
const activeTab = ref(0)

const scene: any = inject(sceneKey)
const mapbox = inject(mapKey)
const mapRef: any = inject(mapRefKey)

const playing = ref(false)
const bhd_data = ref<any[]>([])
let protectAreaGeoJson: any = null
async function getFormData() {
  // 保护点数据
  try {
    const list = formData?.value['protection_point_list']
    bhd_data.value = list

    const geojson = await fetch(`${import.meta.env.VITE_APP_URL}gis/protect_area.geojson`)
    protectAreaGeoJson = await geojson.json()
    // 向地图添加source
    mapbox?.value.addSource('protect-point-area', {
      type: 'geojson',
      data: protectAreaGeoJson
    })
  } catch (error) {
    console.log('获取保护点基本信息表失败', error)
  }
}

const stepCurrent = ref(0)

const stepsYear = computed<any[]>(() => {
  const validYears = bhd_data.value
    .filter((item) => item.year !== undefined && item.year !== null)
    .map((item) => Number(item.year))

  if (validYears.length === 0) return []

  const minYear = Math.min(...validYears)
  const maxYear = Math.max(...validYears)

  const allYears = Array.from({ length: maxYear - minYear + 1 }, (_, index) => minYear + index)

  // const existingYears = new Set(validYears)

  // let prevExistingYear: any = null
  return allYears.map((year) => {
    // if (existingYears.has(year)) {
    //   prevExistingYear = year
    // }
    return {
      title: year,
      value: year
    }
  })
})

// 添加保护点
const addPoint = () => {
  let layer = scene.value.getLayerByName('protect-bhd-point')
  layer.on('click', (e) => {
    changePoint(e.feature.bhdbm)
  })
}
let stepTimer: any
function playStep() {
  let layer = scene.value.getLayerByName('protect-bhd-point')
  let new_point = scene.value.getLayerByName('protect-bhd-point-new')
  function stop() {
    clearInterval(stepTimer)
    playing.value = false
  }
  function pointFilter() {
    layer.filter('year', (val) => val < stepsYear.value[stepCurrent.value].value)
    new_point.filter('year', (val) => val == stepsYear.value[stepCurrent.value].value)
    scene.value.render()
  }

  if (playing.value) {
    stop()
  } else {
    stop()
    playing.value = true
    pointFilter()
    stepTimer = setInterval(() => {
      stepCurrent.value++
      pointFilter()
      if (stepCurrent.value >= stepsYear.value.length) {
        stepCurrent.value = 0
      }
    }, 3000)
  }
}

const pointName = ref('')
// 选择保护点
const pointId = ref<string | null>(null)
function changePoint(value: string) {
  activeTab.value = 0
  pointId.value = value
  let layer = scene.value.getLayerByName('protect-bhd-point')
  if (value) {
    layer.hide()
    let point = bhd_data.value.find((item) => item.bhdbm === value)
    pointName.value = point.bhdmc || ''

    mapRef.value?.setMaskLayer(point.ccode)
    // 获取保护点相应的四至
    let info = protectAreaGeoJson.features.filter((item: any) => {
      return item.properties.BHDBM === value
    })
    if (info.length === 0) {
      mapRef.value?.flyToPoint({ x: point.lon, y: point.lat, zoom: 15 })
      return
    }
    console.log('保护区信息', info)
    // 获取最大的四至
    let bounds = {
      x_min: null,
      y_min: null,
      x_max: null,
      y_max: null
    }
    info.forEach((item: any) => {
      let temp = item.properties
      if (!bounds.x_min || temp.xmin < bounds.x_min) bounds.x_min = temp.xmin
      if (!bounds.y_min || temp.ymin < bounds.y_min) bounds.y_min = temp.ymin
      if (!bounds.x_max || temp.xmax > bounds.x_max) bounds.x_max = temp.xmax
      if (!bounds.y_max || temp.ymax > bounds.y_max) bounds.y_max = temp.ymax
    })
    mapbox?.value.fitBounds([bounds.x_min, bounds.y_min, bounds.x_max, bounds.y_max])

    mapbox?.value.addLayer({
      id: 'protect-point-area',
      type: 'fill',
      source: 'protect-point-area',
      filter: ['==', 'BHDBM', value],
      paint: {
        'fill-color': [
          'match',
          ['get', 'BHDLX'],
          '缓冲区',
          'rgba(163,255,115,0.5)',
          '核心区',
          'rgba(115,223,255,0.5)',
          '试验区',
          'rgba(255,170,0,0.5)',
          '抢救园',
          'rgba(255, 48, 48, 0.5)',
          'rgba(0,0,0,0)'
        ]
      }
    })
  } else {
    mapRef.value?.setThemeLayer()
    layer.show()
  }
}

let layerColorMap = {
  缓冲区: '#A3FF73',
  核心区: '#73DFFF',
  试验区: '#FFAA00',
  抢救园: '#FF3030'
}

watch(
  () => oneMapStore.pageCurrent,
  (val) => {
    if (val === 1) initPageState()
  }
)
</script>

<style lang="less" scoped>
.tab-item.active {
  background-color: #166cff;
  color: #fff;
}
</style>
