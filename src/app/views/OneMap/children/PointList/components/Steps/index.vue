<template>
  <div class="fixed z-[1001] bottom-5 left-20">
    <div class="flex items-center gap-10">
      <div class="flex items-center">
        <img
          v-if="!playing"
          class="w-[24px] h-[24px] cursor-pointer"
          :src="Begin"
          alt="播放"
          @click="$emit('play-step')"
        />
        <img
          v-else
          class="w-[24px] h-[24px] cursor-pointer"
          :src="Pause"
          alt="暂停"
          @click="$emit('play-step')"
        />
      </div>

      <div class="h-[70px] rounded-md">
        <div class="relative h-[24px]">
          <div class="w-[1214px] h-full bg-[rgba(0,0,0,0.2)] rounded-full"></div>
          <div
            class="absolute top-1/2 -translate-y-1/2 w-[20px] h-[20px] bg-map-light-blue border-[3px] border-white border-solid rounded-full transition-all duration-300 ease-in-out"
            :style="{
              left: `calc(${(stepCurrent / totalMonths) * 11.8 * 100}% + 2px)`
            }"
          ></div>
        </div>

        <div class="w-[1194px] relative">
          <div class="absolute w-full translate-x-[0px] translate-y-2">
            <div
              v-for="monthIndex in totalMonths + 1"
              :key="'month-' + monthIndex"
              :style="{ left: `${(monthIndex / totalMonths) * 100}%` }"
              :class="[
                'month-line',
                (monthIndex - 1) % 12 === 0 || monthIndex === totalMonths + 1
                  ? 'month-long'
                  : 'month-short'
              ]"
            ></div>
          </div>

          <div
            v-for="(year, index) in stepsYear"
            :key="year"
            class="year-marker"
            :style="{
              left: `${(index / (stepsYear.length - 1)) * 100}%`,
              bottom: '-18px'
            }"
          >
            {{ year.title }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.month-line {
  width: 1px;
  position: absolute;
  top: 0;

  &.month-short {
    height: 8px;
    background: #8bb4b9;
  }

  &.month-long {
    height: 14px;
    background: #eafeff;
  }
}

.year-marker {
  position: absolute;
  top: 20px;
  transform: translateX(-25%);
  color: #eafeff;
  white-space: nowrap;
}
</style>

<script lang="ts" setup>
import Begin from '@/assets/images/onemap/begin.png'
import Pause from '@/assets/images/onemap/pause.png'
const props = defineProps({
  stepCurrent: { type: Number, required: true },
  stepsYear: { type: Array<any>, required: true },
  playing: { type: Boolean, required: true }
})

const emit = defineEmits(['play-step'])

const totalMonths = computed(() => {
  if (props.stepsYear.length < 2) return 0
  return (props.stepsYear.length - 1) * 12
})
</script>
