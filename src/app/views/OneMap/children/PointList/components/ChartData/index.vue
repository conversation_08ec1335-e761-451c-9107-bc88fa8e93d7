<template>
  <div>
    <info-box title="保护点建设情况" width="500px" height="230px">
      <BarChart width="470px" height="200px" :data="bhd_build" />
    </info-box>
    <info-box title="历年保护点建设情况（万亩）" width="500px" height="245px">
      <LineChart lineType="straight" unit="万亩" width="470px" height="210px" :data="bhd_history" />
    </info-box>
    <info-box title="历年保护点资金投入情况（万元）" width="500px" height="245px">
      <LineChart lineType="curve" unit="万元" width="470px" height="210px" :data="bhd_cost" />
    </info-box>
  </div>
</template>

<script lang="ts" setup>
import InfoBox from '@/app/views/OneMap/components/InfoBox/index.vue'
import LineChart from '../../../../components/LineChart/index.vue'
import BarChart from '../../../../components/BarChart/index.vue'

interface Props {
  data: any[]
}
const props = defineProps<Props>()

// 获取数据
let bhd_base: any = props.data

interface BaseChartData {
  name: string
  value: number
}
// 统计数据
const bhd_build = ref<BaseChartData[]>([])
const bhd_history = ref<BaseChartData[]>([])
const bhd_cost = ref<BaseChartData[]>([])
function countData() {
  // 统计保护点建设情况
  const build = new Map()
  bhd_base.forEach((item) => {
    item.wzmc.forEach((items) => {
      if (build.has(items)) {
        build.set(items, build.get(items) + 1)
      } else {
        build.set(items, 1)
      }
    })
  })
  bhd_build.value = Array.from(build, ([name, value]) => ({
    name,
    value
  })).sort((a, b) => a.value - b.value)
  // 统计历年保护点建设情况
  const history = new Map()
  bhd_base.forEach((item) => {
    if (!item.year) return
    if (history.has(item.year)) {
      history.set(item.year, history.get(item.year) + item.jsmj || 0)
    } else {
      history.set(item.year, item.jsmj || 0)
    }
  })
  bhd_history.value = Array.from(history, ([name, value]) => ({
    name,
    value: value / 10000
  })).sort((a, b) => Number(a.name) - Number(b.name))

  // 统计历年保护点资金投入情况
  const cost = new Map()
  bhd_base.forEach((item) => {
    if (!item.year) return
    if (cost.has(item.year)) {
      cost.set(item.year, cost.get(item.year) + item.trzj || 0)
    } else {
      cost.set(item.year, item.trzj || 0)
    }
  })
  bhd_cost.value = Array.from(cost, ([name, value]) => ({
    name,
    value: value / 10000
  })).sort((a, b) => Number(a.name) - Number(b.name))
}

onMounted(async () => {
  countData()
})
</script>
<style></style>
