<template>
  <div class="flex gap-[30px] w-[86vw]">
    <div class="w-[468px] flex flex-col gap-[20px]">
      <InfoBox title="设备状态" height="490px" :infoType="1">
        <div class="flex flex-col p-[19px]">
          <div class="flex">
            <div
              v-for="(item, index) in deviceState"
              :key="index"
              class="flex items-center w-1/2 h-[60px]"
              :style="{
                'background-image': `url(${item.img})`,
                'background-size': '100% 100%'
              }"
            >
              <div class="flex items-end justfity-center pl-[85px] text-map-simple-blue">
                <div class="text-base">{{ item.label }}</div>
                <p class="pl-[12px]">
                  <span
                    class="text-number font-bold leading-none"
                    :class="{
                      'text-[#13D1C1]': index === 0,
                      'text-[#FFF88B]': index === 1
                    }"
                  >
                    {{ item.value }}</span
                  >
                  <span class="pl-[8px] text-base">个</span>
                </p>
              </div>
            </div>
          </div>
          <div class="flex-1">
            <div class="mt-[10px] max-h-[320px] overflow-y-auto">
              <div
                class="flex justify-between items-center mb-[10px] text-[#EAFEFF]"
                v-for="device in show_data"
                :key="device.device_code"
                @click="realPlay(device)"
              >
                <div class="flex items-center gap-1">
                  <img :src="monitorImg" alt="" style="width: 18px; height: 20px" />
                  <p>{{ device.device_name }}</p>
                </div>

                <div class="flex items-center gap-2">
                  <span
                    class="w-2.5 h-2.5 rounded-full"
                    :style="{
                      'background-color': device.info.isOnline === 1 ? '#13D1C1' : '#8BB4B9'
                    }"
                  ></span>
                  <span>{{ device.info.isOnline === 1 ? '在线' : '离线' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </InfoBox>

      <InfoBox title="历史数据" height="290px" :infoType="1">
        <div class="p-[20px]">
          <div>
            <a-range-picker
              class="w-full"
              size="large"
              popupClassName="onemap-picker-panel"
              @change="onRangeChange"
            >
              <template #suffixIcon>
                <img :src="timeImg" alt="" style="width: 17px; height: 17px" />
              </template>
            </a-range-picker>
          </div>

          <div class="flex flex-nowrap gap-2 overflow-x-auto mt-[10px]">
            <div
              v-for="(item, index) in historyData"
              :key="index"
              @click="selected = item.field"
              class="px-2 py-2 text-[#fff] text-center transition-all duration-300 ease-in-out cursor-pointer relative border-b-1 border-[#8BB4B9]"
            >
              <div class="text-center" :class="{ 'text-[#00B3C4]': selected === item.field }">
                {{ item.label }}
              </div>

              <div
                v-if="selected === item.field"
                class="absolute bottom-0 left-1/2 -translate-x-1/2 h-1 w-full bg-[#00B3C4]"
              ></div>
            </div>
          </div>
        </div>
      </InfoBox>
    </div>

    <div class="flex-1">
      <InfoBox title="实时监控" height="860px" :infoType="1">
        <div class="h-full w-full">
          <div class="rounded-sm overflow-hidden" id="point-monitor-video"></div>
        </div>
      </InfoBox>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'PointMonitor' })
import InfoBox from '@/app/views/OneMap/components/InfoBox/index.vue'
import { formDataKey } from '@/app/views/OneMap/config'
import PlayerManager from '@/app/views/OneMap/utils/icc/PlayerManager'
import monitorApi from '@/apis/monitor'

import retractImg from '@/assets/images/onemap/retract.png'
import expandImg from '@/assets/images/onemap/expand.png'
import monitorImg from '@/assets/images/onemap/monitor.png'
import timeImg from '@/assets/images/onemap/time.png'
import card_7 from '@/assets/images/onemap/card_7.png'
import card_8 from '@/assets/images/onemap/card_8.png'
import { createPlayerContainer } from '@/app/views/OneMap/hooks/useMainHandler'

interface Props {
  pointId: string
}
const props = defineProps<Props>()
const formData = inject(formDataKey)

// 设备状态统计
const deviceState = computed(() => {
  const online = show_data.value.filter((item) => item.info?.isOnline === 1).length
  const offline = show_data.value.filter((item) => item.info?.isOnline === 0).length
  return [
    {
      label: '在线',
      img: card_7,
      value: online
    },
    {
      label: '离线',
      img: card_8,
      value: offline
    }
  ]
})

// 监测点数据
const point_data = ref<any[]>([])
const show_data = ref<any[]>([])

// 获取监测点设备列表
async function getPointDeviceList() {
  try {
    // 获取设备在线状态列表
    const list = await monitorApi.getDeviceList({
      pageNum: 1,
      pageSize: 100,
      ownerCode: '001',
      showChildNodeData: 1
    })

    // 处理监测点列表以及在线情况
    const devices = formData?.value.monitor_device
    const ary: any = []

    let device_list = devices.filter((items) => props.pointId == items.bhdbm)
    if (device_list.length > 0) {
      // 匹配相应的设备信息
      device_list.map((items) => {
        let info = list.pageData.find((device) => items.device_code == device.deviceCode)
        items.info = info ? info : null
        return items
      })
    }
    point_data.value = ary
    show_data.value = device_list
  } catch (error) {
    console.log('获取监测点设备列表失败:', error)
  }
}

// 视频播放相关
const realPlayer = ref<PlayerManager>()
const selectPlayerWindow = ref(0)
const init_completed = ref(false)

// 初始化视频播放器
async function initVideo() {
  if (init_completed.value) return

  realPlayer.value = createPlayerContainer('point-monitor-video', (methods, data, err) => {
    switch (methods) {
      case 'initializationCompleted':
        console.log('监测点播放器组件初始化完成')
        break
      case 'selectWindowChanged':
        selectPlayerWindow.value = data.playIndex
        break
    }
  })

  // 默认播放前6个在线设备
  let online_devices: any[] = []
  show_data.value.forEach((device) => {
    if (device.info?.isOnline === 1 && online_devices.length < 6) {
      online_devices.push(device)
    }
  })

  online_devices.forEach((device, index) => {
    realPlay(device, index)
  })

  init_completed.value = true
}

// 实时播放
async function realPlay(device, play_window = selectPlayerWindow.value) {
  try {
    const result: any = await getDeviceUrl(device.device_code)
    const videoUrl = result.url.split('|')[1] + `?token=${result.token}`
    console.log('videoUrl', videoUrl)
    console.log('realPlayer.value', realPlayer.value)
    const channelId = device.channel_id ? device.channel_id : device.device_code + '$1$0$0'
    realPlayer.value?.realByUrl({
      wsURL: 'ws://223.75.52.103:9100/', // 建立的websocket连接地址, 必传, 根据返回的rtsp的流地址进行拼接，可在demo进行查看
      rtspURL: videoUrl, // 接口返回的rtsp地址
      channelId,
      selectIndex: play_window,
      playerAdapter: 'stretching'
    })
  } catch (error) {
    console.log('播放视频失败:', error)
  }
}

// 获取设备视频流地址
async function getDeviceUrl(deviceId: string) {
  try {
    const list = await monitorApi.getVideoList(`${deviceId}$1$0$0`)
    return list
  } catch (err) {
    console.log('getVideoList', err)
  }
}

// 时间范围选择
const onRangeChange = (date, dateString) => {
  console.log('时间范围变化:', date, dateString)
}

// 展开/收起状态
const currentExpandedIndex = ref(-1)

// 处理列表项点击事件
const handleItemClick = (index: number) => {
  currentExpandedIndex.value = currentExpandedIndex.value === index ? -1 : index
}

// 历史数据选项
const selected = ref('alarm')
const historyData = [
  {
    label: '最近告警',
    field: 'alarm'
  },
  {
    label: '历史数据',
    field: 'history'
  }
]

// 组件挂载时获取数据
onMounted(async () => {
  try {
    // 获取认证token
    const token = await monitorApi.authToken()
    localStorage.setItem('dhToken', token)
    // 获取监测点设备列表
    await getPointDeviceList()
    initVideo()
  } catch (error) {
    console.log('初始化失败:', error)
  }
})
</script>

<style lang="less" scoped>
:deep(.ant-input-affix-wrapper) {
  background-color: #043e53;
  border-color: #3bb8c1;
  .ant-input {
    background-color: #043e53;
    color: #eafeff !important;
    &::placeholder {
      color: #eafeff;
    }
  }
}

:deep(.ant-picker) {
  background-color: #043e53;
  border-color: #3bb8c1;
  color: #eafeff;
  .ant-picker-input input::placeholder {
    color: #eafeff;
  }
  .ant-picker-input > input {
    color: #eafeff;
  }

  .ant-picker-range-separator {
    font-size: 0;

    svg {
      color: #eafeff;
    }
  }

  .ant-picker-clear {
    svg {
      width: 20px;
      height: 20px;
    }
  }
}
</style>
