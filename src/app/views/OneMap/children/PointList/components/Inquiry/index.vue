<template>
  <div v-if="Object.keys(scientificData).length > 0 || monitoringData">
    <div class="tabBox">
      <div
        v-for="item in tab"
        :key="item"
        @click="activeTab = item"
        class="flex-1 mx-4 my-3 h-[44px] flex justify-center cursor-pointer items-center text-center text-map-simple-blue"
        :class="{
          'text-map-light-blue': activeTab === item,
          active: activeTab === item
        }"
      >
        <div>
          <span>{{ item }}</span>
        </div>
      </div>
    </div>

    <div v-if="activeTab === '科学考察'">
      <InfoBox
        title="科学考察工作开展情况"
        subTitle="查看更多"
        @update:open="modalOpen(0)"
        width="500px"
        height="300px"
      >
        <div>
          <InfoList :list="workInfo" :data="scientificData" />
        </div>
      </InfoBox>
      <InfoBox title="保护区物种组成" width="500px">
        <div class="flex mb-4">
          <div
            v-for="(item, index) in speciesCard"
            :key="index"
            class="shadow-lg flex items-center gap-x-2 w-1/2 h-[61px]"
            :style="{
              'background-image': `url(${yswz_Imgs[index]})`,
              'background-size': '100% 100%'
            }"
          >
            <div class="flex items-center pl-[75px]">
              <div class="text-sm w-[90px] shrink-0 text-map-simple-blue">{{ item.label }}</div>
              <p class="text-white text-xs pl-2">
                <span
                  class="text-xl font-bold"
                  :class="{
                    'text-[#6AC5FF]': index === 0,
                    'text-[#13D1C1]': index === 1
                  }"
                >
                  {{ scientificData[item.field] || 0 }}
                </span>
                <span class="ml-1 text-sm">种</span>
              </p>
            </div>
          </div>
        </div>
        <div>
          <InfoList :list="speciesInfo" :data="scientificData" :contentWrap="false" />
        </div>
      </InfoBox>
      <InfoBox title="标本采集" subTitle="查看更多" @update:open="modalOpen(1)" width="500px">
        <div class="flex mb-4">
          <div
            v-for="(item, index) in sampleInfo"
            :key="index"
            class="shadow-lg flex items-center gap-x-1 w-1/2 h-[61px]"
            :style="{
              'background-image': `url(${cjbb_Imgs[index]})`,
              'background-size': '100% 100%'
            }"
          >
            <div class="flex items-center pl-[75px] text-base">
              <div class="w-[75px] text-map-simple-blue">{{ item.label }}</div>
              <p class="text-white pl-2">
                <span
                  class="font-bold text-xl"
                  :class="{
                    'text-[#6AC5FF]': index === 0,
                    'text-[#13D1C1]': index === 1
                  }"
                  >{{ scientificData[item.field] || 0 }}</span
                >
                <span class="ml-1 text-sm">种</span>
              </p>
            </div>
          </div>
        </div>
      </InfoBox>
    </div>
    <div v-else>
      <InfoBox
        title="保护点监测结果"
        subTitle="查看更多"
        @update:open="isJcpgModalOpen = true"
        width="500px"
        height="720px"
      >
        <div class="flex flex-col mb-4">
          <div class="flex flex-nowrap gap-2 overflow-x-auto">
            <div
              v-for="(item, index) in monitoringData"
              :key="index"
              @click="selectedYear = item.year"
              class="px-2 py-2 text-white text-center transition-all duration-300 ease-in-out cursor-pointer relative border-b-1 border-[#8BB4B9]"
            >
              <div
                class="text-center"
                :class="{ 'text-map-light-blue': selectedYear === item.year }"
              >
                {{ item.year + '年' }}
              </div>

              <div
                v-if="selectedYear === item.year"
                class="absolute bottom-0 left-1/2 -translate-x-1/2 h-1 w-full bg-map-light-blue"
              ></div>
            </div>
          </div>
        </div>

        <div class="max-h-[620px] overflow-y-auto">
          <InfoList :list="monitoringInfo" :data="monitoringSelectData" :isEllipsis="false" />
          <div class="mt-2">
            <div class="text-base text-map-simple-blue mb-[5px]">考察照片</div>
            <ImageCarousel height="48vh" :images="monitoringSelectData['kczp']" />
          </div>
        </div>
      </InfoBox>
    </div>

    <CustomModal
      :title="modalIndex === 0 ? '科学考察工作开展情况' : '标本照片'"
      v-model:open="isModalOpen"
    >
      <template v-if="modalIndex === 0">
        <TabNav :tabs="kxkcModalTab" v-model:activeTab="kxkc_activeTab" />
        <div v-if="kxkc_activeTab === 0">
          <InfoList :list="workInfo" :data="scientificData" :isEllipsis="false" />
        </div>
        <div v-else>
          <ImageCarousel height="44vh" :images="scientificData['xcgzzp']" />
        </div>
      </template>
      <template v-else-if="modalIndex === 1">
        <ImageCarousel height="44vh" :images="scientificData['bbcjzp']" />
      </template>
    </CustomModal>

    <CustomModal title="监测评估" v-model:open="isJcpgModalOpen">
      <TabNav :tabs="jcpgModalTab" v-model:activeTab="jcpg_activeTab" />
      <div v-if="jcpg_activeTab === 0">
        <span class="text-map-simple-blue text-base">{{
          monitoringSelectData['wzfbzk'] || '-'
        }}</span>
      </div>
      <div v-else-if="jcpg_activeTab === 1">
        <ImageCarousel height="44vh" :images="monitoringSelectData['xcgzzp']" />
      </div>
      <div v-else>
        <span class="text-map-simple-blue text-base">{{
          monitoringSelectData['qjxsj'] || '-'
        }}</span>
      </div>
    </CustomModal>
  </div>
</template>

<script lang="ts" setup>
import InfoBox from '../../../../components/InfoBox/index.vue'
import CustomModal from '../../../../components/CustomModal/index.vue'
import TabNav from '../../../../components/TabNav/index.vue'
import ImageCarousel from '../../../../components/ImageCarousel/index.vue'
import InfoList from '../../../../components/InfoList/index.vue'
import { message } from 'ant-design-vue'

import { formDataKey } from '@/app/views/OneMap/config'

import card_1 from '@/assets/images/onemap/card_1.png'
import card_2 from '@/assets/images/onemap/card_2.png'
import card_3 from '@/assets/images/onemap/card_3.png'
import card_4 from '@/assets/images/onemap/card_4.png'

interface Props {
  pointId: string
}
const props = defineProps<Props>()

const allFormData = inject(formDataKey)

const tab = ['科学考察', '监测评估']

const activeTab = ref('科学考察')
const isModalOpen = ref(false)
const modalIndex = ref(0)
const modalOpen = (index: number) => {
  modalIndex.value = index
  isModalOpen.value = true
}
const kxkcModalTab = ['科学考察工作内容', '现场工作照片']
const kxkc_activeTab = ref(0)
watch(isModalOpen, (newValue, oldValue) => {
  if (oldValue === true && newValue === false) {
    kxkc_activeTab.value = 0
  }
})

const isJcpgModalOpen = ref(false)
const jcpgModalTab = ['物种分布情况', '现场工作照片', '资源抢救性采集']
const jcpg_activeTab = ref(0)
watch(isJcpgModalOpen, (newValue, oldValue) => {
  if (oldValue === true && newValue === false) {
    jcpg_activeTab.value = 0
  }
})

const workInfo = [
  {
    label: '调查时间',
    field: 'dcsj'
  },
  {
    label: '承担单位',
    field: 'cddw'
  },
  {
    label: '调查内容',
    field: 'dcnr'
  },
  {
    label: '工作开展情况',
    field: 'gzqk'
  }
]

const speciesInfo = [
  {
    label: '其他国家重点保护植物名单',
    field: 'qtbhzw_md'
  },

  {
    label: '蕨类植物',
    field: 'jlzw_sl'
  },
  {
    label: '裸子植物',
    field: 'lzzw_sl'
  },
  {
    label: '被子植物',
    field: 'bzzw_sl'
  }
]

const imageMap = {
  card_1,
  card_2,
  card_3,
  card_4
}
const speciesCard = [
  {
    label: '其他国家重点保护野生植物',
    field: 'qtbhzw_sl',
    img: 'card_1'
  },
  {
    label: '维管束植物',
    field: 'wgszw_sl',
    img: 'card_2'
  }
]
const yswz_Imgs = speciesCard.map((item) => imageMap[item.img])

const sampleInfo = [
  {
    label: '物种标本',
    field: 'cjbb_sl',
    img: 'card_3'
  },
  {
    label: '物种数量',
    field: 'cjbb_wzsl',
    img: 'card_4'
  }
]
const cjbb_Imgs = sampleInfo.map((item) => imageMap[item.img])

const monitoringInfo = [
  {
    label: '调查日期',
    field: 'dcsj'
  },
  {
    label: '保护物种分布状况',
    field: 'wzfbzk'
  },
  {
    label: '生境现状',
    field: 'sjxz'
  },
  {
    label: '保护物种种群数量及变动趋势',
    field: 'zqsl'
  },
  {
    label: '保护物种及其生境受威胁因素及程度',
    field: 'sjswxcd'
  },
  {
    label: '保护物种及其生境保护现状',
    field: 'sjbhxz'
  },
  {
    label: '伴生物种',
    field: 'bswz'
  }
]

const scientificData = ref({})

const getScientificData = async () => {
  try {
    const list = allFormData?.value['protection_point_scientific']
    console.log(props.pointId)

    const data = list.find((item: any) => item.bhdbm === props.pointId)
    console.log('protection_point_scientific', data)
    if (data) {
      const jl = Number(data['jlzw_sl']) || 0
      const lz = Number(data['lzzw_sl']) || 0
      const bz = Number(data['bzzw_sl']) || 0
      data['wgszw_sl'] = jl + lz + bz

      if (Array.isArray(data['bbcjzp']) && data['bbcjzp'].length > 0) {
        data['bbcjzp'] = data['bbcjzp'].map((item) => {
          return {
            url: `${window.import_meta.VITE_APP_AXIOS_BASE_URL}/attachment/download/${item.uid}`
          }
        })
      }
      if (Array.isArray(data['xcgzzp']) && data['xcgzzp'].length > 0) {
        data['xcgzzp'] = data['xcgzzp'].map((item) => {
          return {
            url: `${window.import_meta.VITE_APP_AXIOS_BASE_URL}/attachment/download/${item.uid}`
          }
        })
      }

      if (!!data['kxkcsj_start'] && !!data['kxkcsj_end']) {
        const startDate = formatTimestamp(data['kxkcsj_start'])
        const endDate = formatTimestamp(data['kxkcsj_end'])
        data['dcsj'] = `${startDate} - ${endDate}`
      }

      scientificData.value = data
    } else {
      scientificData.value = {}
      // message.warn('该保护点还尚未开展过科学考察、监测评估工作！')
    }
  } catch (error) {
    console.log('获取保护点科学考察信息表失败', error)
  }
}
const formatTimestamp = (timestamp: number) => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

const monitoringData = ref<any>([])
const selectedYear = ref<any>()
const monitoringSelectData = ref({})
const getMonitoringData = async () => {
  try {
    const list = allFormData?.value?.['protection_point_monitoring'] || []

    const data = Array.isArray(list) ? list.filter((item: any) => item.bhdbm === props.pointId) : []

    console.log('protection_point_monitoring', data)

    if (data.length > 0) {
      data.forEach((item: any) => {
        if (item['dcsj']) {
          item['dcsj'] = formatTimestamp(item['dcsj'])
        }

        if (Array.isArray(item['kczp']) && item['kczp'].length > 0) {
          item['kczp'] = item['kczp'].map((img: any) => ({
            url: `${window.import_meta.VITE_APP_AXIOS_BASE_URL}/attachment/download/${img.uid}`
          }))
        }
        if (Array.isArray(item['xcgzzp']) && item['xcgzzp'].length > 0) {
          item['xcgzzp'] = item['xcgzzp'].map((img: any) => ({
            url: `${window.import_meta.VITE_APP_AXIOS_BASE_URL}/attachment/download/${img.uid}`
          }))
        }
      })

      data.sort((a: any, b: any) => {
        const ay = Number(a.year) || 0
        const by = Number(b.year) || 0
        return ay - by
      })
      selectedYear.value = data[0]?.year || null
      monitoringData.value = data
    } else {
      monitoringData.value = []
      selectedYear.value = null
    }
  } catch (error) {
    console.log('获取保护点监测评估信息表失败', error)
    monitoringData.value = []
    selectedYear.value = null
  }
}

// 根据选中的年份筛选数据
watch(
  () => selectedYear.value,
  (value) => {
    if (!value) return
    const foundData = monitoringData.value.find((item) => item.year === value)
    monitoringSelectData.value = foundData
  },
  { immediate: false }
)
watch(
  () => props.pointId,
  (value) => {
    if (!value) return

    getScientificData()
    getMonitoringData()
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
.tabBox {
  height: 70px;
  width: 500px;
  display: flex;
  margin-bottom: 2px;
  background-image: url('@/assets/images/onemap/tobTabBg.png');
  background-size: 100% 100%;
}

.active {
  background-image: url('@/assets/images/onemap/tobTabBging.png');
  background-size: 100% 100%;
}
</style>
