<template>
  <div>
    <InfoBox title="建设规模" width="500px">
      <div>
        <InfoList :list="buildInfo" :data="formData" :contentWrap="false" />
      </div>
    </InfoBox>
    <InfoBox title="工程内容" subTitle="查看更多" @update:open="isModalOpen = true" width="500px">
      <div class="max-h-[320px] overflow-y-auto">
        <InfoList :list="contentInfo" :data="formData" :isEllipsis="false" />
      </div>
    </InfoBox>
    <InfoBox title="验收情况" width="500px">
      <div>
        <InfoList :list="naturalInfo" :data="formData" :contentWrap="false" />
      </div>
    </InfoBox>

    <CustomModal title="工程内容" v-model:open="isModalOpen">
      <ImageCarousel height="48vh" :images="formData['gczp']" />
    </CustomModal>
  </div>
</template>

<script lang="ts" setup>
import CustomModal from '../../../../components/CustomModal/index.vue'
import InfoBox from '../../../../components/InfoBox/index.vue'
import ImageCarousel from '../../../../components/ImageCarousel/index.vue'
import InfoList from '../../../../components/InfoList/index.vue'

import { formDataKey } from '@/app/views/OneMap/config'

interface Props {
  pointId: string
}
const props = defineProps<Props>()

const allFormData = inject(formDataKey)

const buildInfo = [
  { label: '建设时间', field: 'jssj' },
  { label: '总面积', field: 'zmj', unit: '亩' },
  { label: '核心区', field: 'hxq_mj', unit: '亩' },
  { label: '缓冲区', field: 'hcq_mj', unit: '亩' },
  { label: '试验区', field: 'syq_mj', unit: '亩' },
  { label: '抢救区', field: 'qjy_mj', unit: '亩' }
]
const contentInfo = [{ label: '工程内容', field: 'gcnr' }]

const naturalInfo = [
  { label: '建设单位', field: 'jsdw' },
  { label: '是否验收', field: 'sfys' },
  { label: '建设期限', field: 'jsqx' }
]

const formatDate = (val: string | number) => {
  if (!val) return ''
  const date = new Date(Number(val))
  const y = date.getFullYear()
  const m = date.getMonth() + 1
  const d = date.getDate()
  return `${y}.${m}.${d}`
}

const isModalOpen = ref(false)

const formData = ref({})
const getFormData = async () => {
  try {
    const list = allFormData?.value['protection_point_development']
    const data = list.find((item) => item.bhdbm === props.pointId)
    console.log('protection_point_development', data)

    if (data) {
      if (!!data['jssj_start'] && !!data['jssj_end']) {
        const startYear = formatDate(data['jssj_start'])
        const endYear = formatDate(data['jssj_end'])
        data['jssj'] = `${startYear} - ${endYear}`
      }

      if (Array.isArray(data['gczp']) && data['gczp'].length > 0) {
        data['gczp'] = data['gczp'].map((item: any) => {
          return {
            url: `${window.import_meta.VITE_APP_AXIOS_BASE_URL}/attachment/download/${item.uid}`
          }
        })
      }
      formData.value = data
    } else {
      formData.value = {}
    }
  } catch (error) {
    console.log('获取protection_point_development失败', error)
  }
}

watch(
  () => props.pointId,
  (value) => {
    if (!value) return
    getFormData()
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
:deep(.slick-arrow.custom-slick-arrow) {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #fff;
  background-color: rgba(31, 45, 61, 0.11);
  transition: ease all 0.3s;
  opacity: 0.3;
  z-index: 1;
}
:deep(.slick-arrow.custom-slick-arrow:before) {
  display: none;
}
:deep(.slick-arrow.custom-slick-arrow:hover) {
  color: #fff;
  opacity: 0.5;
}
</style>
