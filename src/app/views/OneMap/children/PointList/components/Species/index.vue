<template>
  <div>
    <InfoBox title="保护点基本信息" width="500px" height="150px">
      <InfoList :list="baseInfo" :data="baseData" :contentWrap="false" />
    </InfoBox>
    <InfoBox
      title="保护点自然条件"
      subTitle="查看更多"
      @update:open="isModalOpen = true"
      width="500px"
      height="280px"
    >
      <InfoList :list="naturalInfo" :data="statusData" />
    </InfoBox>
    <InfoBox title="保护点内物种分布" width="500px" height="280px">
      <div class="max-h-[250px] overflow-y-auto">
        <InfoList :list="resourceInfo" :data="statusData" :isEllipsis="false" />
      </div>
    </InfoBox>

    <CustomModal title="保护点自然条件" v-model:open="isModalOpen">
      <TabNav :tabs="naturalTab" v-model:activeTab="activeTab" />

      <div class="text-base text-white">{{ currentTabContent || '-' }}</div>
    </CustomModal>
  </div>
</template>

<script lang="ts" setup>
import InfoBox from '../../../../components/InfoBox/index.vue'
import CustomModal from '../../../../components/CustomModal/index.vue'
import TabNav from '../../../../components/TabNav/index.vue'
import InfoList from '../../../../components/InfoList/index.vue'

import { formDataKey } from '@/app/views/OneMap/config'

interface Props {
  pointId: string
}

const props = defineProps<Props>()

const formData = inject(formDataKey)

const baseInfo = [
  { label: '保护点名称', field: 'bhdmc' },
  { label: '建设时间', field: 'year' },
  { label: '土地利用类型', field: 'tdlylx' }
]

const naturalInfo = [
  { label: '气候条件', field: 'qhtj' },
  { label: '水系条件', field: 'sxtj' },
  { label: '土壤条件', field: 'ttlx' }
]

const resourceInfo = [
  { label: '保护植物生长情况', field: 'wzszqk' },
  { label: '乔木层群落调查结果', field: 'qm_qldcjg' },
  { label: '乔木层常见伴生物种', field: 'qm_bswz' },
  { label: '草本层群落调查结果', field: 'cb_qldcjg' },
  { label: '草本层常见伴生物种', field: 'cb_bswz' }
]

const naturalTab = ['气候条件', '水系条件', '土壤条件']
const isModalOpen = ref(false)
const activeTab = ref(0)
const currentTabContent = computed(() => {
  const currentItem = naturalInfo[activeTab.value]
  if (currentItem) {
    return statusData.value[currentItem.field] || '无'
  }
  return '无'
})
watch(isModalOpen, (newValue, oldValue) => {
  if (oldValue === true && newValue === false) {
    activeTab.value = 0
  }
})

const baseData = ref({})
const getBaseData = async () => {
  try {
    const list = Array.isArray(formData?.value?.['protection_point_list'])
      ? formData.value['protection_point_list']
      : []
    const data = list.find((item) => item.bhdbm === props.pointId)
    baseData.value = data || {}
  } catch (error) {
    console.log('获取protection_point_list失败', error)
  }
}
const statusData = ref({})
const getStatusData = async () => {
  try {
    const list = Array.isArray(formData?.value?.['protection_point_species_status'])
      ? formData.value['protection_point_species_status']
      : []
    const data = list.find((item) => item.bhdbm === props.pointId)
    statusData.value = data || {}
  } catch (error) {
    console.log('获取protection_point_species_status失败', error)
  }
}

watch(
  () => props.pointId,
  (value) => {
    if (!value) return

    getBaseData()
    getStatusData()
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped></style>
