<template>
  <div v-if="formData" class="text-base">
    <div class="pb-4 pt-2">
      <div class="flex items-center gap-2.5">
        <div
          v-for="item in tab"
          :key="item"
          class="px-[20px] py-[4px] text-base flex items-center justify-center rounded cursor-pointer transition-all duration-200 border border-map-light-blue select-none"
          :class="{
            'text-map-simple-blue bg-map-dark-blue': activeTab === item,
            'text-map-simple-blue opacity-50 hover:opacity-70': activeTab !== item
          }"
          @click="activeTab = item"
        >
          {{ item }}
        </div>
      </div>
    </div>

    <div class="max-height-scroll">
      <div v-if="activeTab === '基本信息'" class="flex pt-[19px]">
        <div class="w-1/2">
          <p
            v-for="(item, index) in baseInfo"
            :key="index"
            class="mb-[19px] flex"
            :class="{ 'mb-0': index === baseInfo.length - 1 }"
          >
            <span class="inline-block w-[80px] text-map-simple-blue text-base">{{
              item.label
            }}</span>
            <span class="flex-1 text-white text-base">{{ formData?.[item.field] || '-' }}</span>
          </p>
        </div>

        <div class="w-1/2">
          <ImageCarousel height="309px" :images="formData['base_photo']" />
        </div>
      </div>

      <div v-if="activeTab === '形态特征'" class="flex flex-col">
        <div
          v-for="(info, index) in morphologyInfo"
          :key="index"
          class="w-full mb-[19px] flex flex-col"
          :class="{ 'mb-0': index === morphologyInfo.length - 1 }"
        >
          <div class="mb-4 flex">
            <span class="mr-6 font-bold text-map-simple-blue text-base">{{ info.label }}</span>
            <span class="flex-1 text-white text-base">{{ formData?.[info.field] || '-' }}</span>
          </div>
          <div><ImageCarousel height="330px" :images="formData[`${info.field}_photo`]" /></div>
        </div>
      </div>

      <div v-if="activeTab === '物种分布'" class="py-2">
        <InfoList :list="speciesDistribution" :data="formData" />
      </div>

      <div v-if="activeTab === '物种价值'" class="flex">
        <InfoList :list="speciesValue" :data="formData" />
      </div>

      <div v-if="activeTab === '主要用途'" class="flex">
        <InfoList :list="mainUse" :data="formData" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import ImageCarousel from '../../../../components/ImageCarousel/index.vue'
import InfoList from '@/app/views/OneMap/components/InfoList/index.vue'

const props = defineProps({
  plantInfo: {
    type: Object,
    required: true
  }
})

const formData = ref()

watch(
  () => props.plantInfo,
  (newVal) => {
    if (newVal) {
      formData.value = newVal
    }
  },
  {
    immediate: true
  }
)

// Tab 列表
const tab = ['基本信息', '形态特征', '物种分布', '物种价值', '主要用途']
const activeTab = ref('基本信息')

// 基本信息
const baseInfo = ref([
  { label: '物种学名', field: 'wzxm' },
  { label: '拉丁学名', field: 'ldmc' },
  { label: '科', field: 'species' },
  { label: '属', field: 'genus' },
  { label: '生活型', field: 'shx' },
  { label: '保护等级', field: 'xbhdj' },
  { label: '识别要点', field: 'sbyd' },
  { label: '生长环境', field: 'szhj' }
  // { label: '物种照片', field: 'base_photo' }
])

// 形态特征
const morphologyInfo = ref([
  { label: '根', field: 'root' },
  { label: '株', field: 'plant' },
  { label: '枝', field: 'branch' },
  { label: '茎', field: 'stem' },
  { label: '叶', field: 'leaf' },
  { label: '果', field: 'fruit' }
])

// 物种分布
const speciesDistribution = ref([
  { label: '中国分布', field: 'zgfb' },
  { label: '湖北分布', field: 'hbfb' }
])

// 物种价值
const speciesValue = ref([
  { label: '药用价值', field: 'yyjz' },
  { label: '经济价值', field: 'jjjz' },
  { label: '观赏价值', field: 'gsjz' },
  { label: '科研价值', field: 'kyjz' },
  { label: '生态作用', field: 'stzy' }
])

// 主要用途
const mainUse = ref([{ label: '主要用途', field: 'zyyt' }])
</script>

<style lang="less" scoped>
img {
  max-width: 100%;
  max-height: 300px;
  object-fit: contain;
}
</style>
