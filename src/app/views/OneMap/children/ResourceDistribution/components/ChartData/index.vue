<script lang="ts" setup>
import InfoBox from '@/app/views/OneMap/components/InfoBox/index.vue'
import PieChart from '@/app/views/OneMap/components/PieChart/index.vue'
import { useOneMapStore } from '@/stores'
import { formDataKey, sceneKey } from '@/app/views/OneMap/config'
import { Scene } from '@antv/l7'
import { splitDataAvg } from '@/app/views/OneMap/hooks/useEventHandler'

const emit = defineEmits(['change'])

const scene = inject(sceneKey) as Ref<Scene>
const formData = inject(formDataKey) as Ref<any>

const statisticData = ref<any[]>([
  {
    title: '国家重点保护野生植物数量',
    centerText: '',
    data: []
  },
  {
    title: '农业重点保护野生植物数量',
    centerText: '',
    data: []
  },
  {
    title: '已建设保护点野生植物数量',
    centerText: '',
    data: []
  }
])

const colorMap = [
  {
    name: '被子植物',
    color: '#00C2E1'
  },
  {
    name: '裸子植物',
    color: '#25DB50'
  },
  {
    name: '石松类和蕨类植物',
    color: '#F8D42A'
  },
  {
    name: '苔藓植物',
    color: '#F59E2F'
  },
  {
    name: '真菌',
    color: '#F24F29'
  }
]

const intervalsLegend = ref<any[]>([])
let statisticColorShow = false
const reset = () => {
  const layer = scene.value.getLayerByName('base-layer')
  layer?.color('rgba(0,0,0,0)')
  statisticColorShow = false
}
function setCityCylinder(plant: any) {
  let data = distributeList.all
  let layer = scene.value.getLayerByName('resource-plant-type')
  if (plant) {
    data = data.filter((item) => item.wzmc === plant)
  }
  // 按照城市区域统计好数据
  function splitAreaData(list, dicts, key) {
    const city = new Map()
    list.forEach((item) => {
      if (city.has(item[key])) {
        city.get(item[key]).push(item)
      } else {
        city.set(item[key], [item])
      }
    })
    const result = Array.from(city).map(([itemKey, value]: any[]) => {
      let { x, y } = dicts.find((item) => item[key] === itemKey)
      return {
        name: itemKey,
        value: value.length,
        x,
        y
      }
    })
    return result
  }
  const city_statistic = splitAreaData(data, oneMapStore.cityDicts, 'ccode')

  if (layer) layer.setData(city_statistic)
  scene.value.render()
}

const setColorByStatistic = (list: any[], key: string, mapKey?: string) => {
  // 按照区域分类统计物种个数
  const plant = new Map()

  list.forEach((item) => {
    if (plant.has(item[key])) {
      plant.get(item[key]).push(item)
    } else {
      plant.set(item[key], [item])
    }
  })
  const statistic = Array.from(plant).map(([key, value]: any[]) => {
    return {
      name: key,
      value: value.length,
      color: 'rgba(0,0,0,0)'
    }
  })

  intervalsLegend.value = []
  if (statistic.length === 1) {
    statistic[0].color = 'rgba(21, 255, 252, 0.6)'
    intervalsLegend.value = [
      {
        min: statistic[0].value,
        max: statistic[0].value,
        color: statistic[0].color
      }
    ]
  } else if (statistic.length > 1) {
    // 提取最大值和最小值
    const max = Math.max(...statistic.map((item) => item.value))
    const min = Math.min(...statistic.map((item) => item.value))

    // 计算区间
    let intervals = splitDataAvg(max, min)
    let color = [
      'rgba(21, 255, 252, 0.6)',
      'rgba(0, 162, 173, 0.6)',
      'rgba(0, 128, 137, 0.6)',
      'rgba(0, 65, 69, 0.6)',
      'rgba(0, 32, 34, 0.6)'
    ]
    intervalsLegend.value = intervals.map((item, index) => {
      item.color = color[index]
      return item
    })
    // 判断数据区间，取颜色值
    statistic.map((item) => {
      let index = intervals.findIndex(
        (interval) => item.value >= interval.min && item.value <= interval.max
      )
      item.color = color[index]
      return item
    })
  }

  // 设置区域颜色
  let layer: any = scene.value.getLayerByName('base-layer')

  layer.color(mapKey || key, (val) => {
    let info = statistic.find((item) => item.name === val)
    let color = info ? info.color : 'rgba(0,0,0,0)'
    return color
  })

  statisticColorShow = true
  scene.value.render()
}
const pieClick = (params: any, index: number) => {
  // 只有点击农业部门才会显示交互
  if (index !== 1) return
  let list = distributeList.nybmgl
  if (oneMapStore.selectedArea) {
    list = list.filter((item) => item.ccode === oneMapStore.selectedArea.ccode)
  }

  setColorByStatistic(list, 'fcode', 'code')
  emit('change', statisticData.value[index].title)
}

// 获取种类分布
const plantList = reactive<any>({
  all: [],
  nation: [],
  nybmgl: [],
  jsbhd: []
})
const distributeList = reactive<any>({
  all: [],
  nation: [],
  nybmgl: [],
  jsbhd: []
})
const getFormData = async () => {
  // 物种信息表
  const plant = formData.value.wild_plant_list
  // 分布信息表
  const distribute = formData.value.plant_distribution_list
  // 保护点信息表
  const protect_point = formData.value.protection_point_list
  // 过滤有对应的物种分类
  plantList.all = plant
  distributeList.all = distribute
    .map((item) => {
      item.info = plant.find((items) => item.wzmc === items.wzmc)
      return item
    })
    .filter((item) => item.info)

  // 筛选重点保护野生植物数量
  plantList.nation = plant.filter((item) => ['一级', '二级'].includes(item.xbhdj))
  plantList.nybmgl = plant.filter((item) => item.sfnybmgl === '是' && item.sfhbfb === '是')
  plantList.jsbhd = plant.filter((item) => item.sfjsbhd === '是')

  // 筛选分布数据
  distributeList.nation = distributeList.all.filter((item) =>
    ['一级', '二级'].includes(item.info.xbhdj)
  )
  distributeList.nybmgl = distributeList.all.filter(
    (item) => item.info.sfnybmgl === '是' && item.info.sfhbfb === '是'
  )

  const plant_wzmc = plantList.nybmgl.map((item) => item.wzmc)
  console.log('物种名称', plant_wzmc, plant_wzmc.length)

  const distribute_wzmc = uniqueArr(distributeList.nybmgl, 'wzmc').map((item) => item.wzmc)
  console.log('分布物种', distribute_wzmc, distribute_wzmc.length)

  // 统计种类数量
  statisticData.value[0].data = getPlantFldw(plantList.nation)
  statisticData.value[0].centerText = `${plantList.nation.length}`

  let uniqueNybmgl = uniqueArr(distributeList.nybmgl, 'wzmc')
  statisticData.value[1].data = getPlantFldw(uniqueNybmgl)
  statisticData.value[1].centerText = `${uniqueNybmgl.length}`

  // 已建设保护点野生植物数量
  let jsbhd_ary: any = []
  protect_point.forEach((item) => {
    plantList.jsbhd.forEach((items) => {
      if (item.wzmc.includes(items.wzmc)) jsbhd_ary.push(items)
    })
  })
  distributeList.jsbhd = uniqueArr(jsbhd_ary, 'wzmc')

  statisticData.value[2].data = getPlantFldw(distributeList.jsbhd)
  statisticData.value[2].centerText = `${distributeList.jsbhd.length}`
}

function protectPointStatistic(ccode?: string) {
  // 保护点信息表
  let protect_point = formData.value.protection_point_list
  if (ccode) protect_point = protect_point.filter((item) => item.ccode === ccode)
  let jsbhd_ary: any = []
  protect_point.forEach((item) => {
    plantList.jsbhd.forEach((items) => {
      if (item.wzmc.includes(items.wzmc)) jsbhd_ary.push(items)
    })
  })
  const uniqueJsbhd = uniqueArr(jsbhd_ary, 'wzmc')

  statisticData.value[2].data = getPlantFldw(uniqueJsbhd)
  statisticData.value[2].centerText = `${uniqueJsbhd.length}`
}

function uniqueArr(list, key) {
  const ary = list.filter(
    (obj, index, self) => index === self.findIndex((t) => t[key] === obj[key])
  )
  return ary
}

function getPlantFldw(list) {
  const data = {
    被子植物: 0,
    裸子植物: 0,
    石松类和蕨类植物: 0,
    苔藓植物: 0,
    真菌: 0
  }

  list.forEach((item) => {
    data[item.fldw || item.info.fldw] += 1
  })
  const result = Object.entries(data).map(([name, value]) => ({
    name,
    value
  }))

  return result
}
onMounted(async () => {
  await getFormData()
  reset()
  setCityCylinder(null)
})

const oneMapStore = useOneMapStore()

defineExpose({ reset, intervalsLegend, pieClick, setCityCylinder })

watch(
  () => oneMapStore.selectedArea,
  (val) => {
    let nybmgl = distributeList.nybmgl
    if (val) {
      protectPointStatistic(val.ccode)
      nybmgl = nybmgl.filter((item) => item.ccode === val.ccode)

      statisticData.value[1].data = getPlantFldw(nybmgl)
      statisticData.value[1].centerText = `${nybmgl.length}`
      if (statisticColorShow) setColorByStatistic(nybmgl, 'fcode', 'code')
    } else {
      nybmgl = uniqueArr(nybmgl, 'wzmc')
      protectPointStatistic()
      statisticData.value[1].data = getPlantFldw(nybmgl)
      statisticData.value[1].centerText = `${nybmgl.length}`
      if (statisticColorShow) setColorByStatistic(nybmgl, 'fcode', 'code')
    }
  }
)
</script>

<template>
  <div>
    <div v-for="(item, index) in statisticData" :key="item.title">
      <info-box :title="item.title" width="500px" height="250px">
        <div class="flex items-center">
          <PieChart
            :center-text="item.centerText"
            :data="item.data"
            @click="pieClick($event, index)"
          />
          <div class="flex flex-col justify-between h-[150px] ml-[20px]">
            <div v-for="(items, index) in colorMap" :key="items.name" class="flex items-center">
              <div
                class="legend-color w-[10px] h-[10px] rounded-lg mr-2 text-[16px]"
                :style="{ backgroundColor: items.color }"
              ></div>
              <span class="text-map-simple-blue mr-3 text-base">{{ items.name }}</span>
              <span v-if="item.data.length > 0" class="text-white">
                <span class="text-xl">{{ item.data[index].value }}</span>
                <span class="ml-1 text-xm">种</span>
              </span>
            </div>
          </div>
        </div>
      </info-box>
    </div>
  </div>
</template>

<style lang="less" scoped></style>

<style>
.l7-popup-content {
  background-color: transparent !important;
  background-image: url('@/assets/images/onemap/toast-popup.png') !important;
  background-size: 100% 100% !important;
  background-position: bottom;
  width: 100px !important;
  height: 60px !important;
  box-shadow: none !important;
  text-align: center;
  font-size: 24px !important;
  font-weight: bold !important;
  color: #00ff84;
}
.l7-popup-tip {
  border: none !important;
}
</style>
