<script lang="ts" setup>
import { useOneMapStore } from '@/stores'
import { EnvironmentTwoTone } from '@ant-design/icons-vue'
import InfoBox from '../../../../components/InfoBox/index.vue'
import CustomModal from '../../../../components/CustomModal/index.vue'
import PlantBaseInfo from '../PlantBaseInfo/index.vue'
import { formDataKey } from '@/app/views/OneMap/config'
import { message } from 'ant-design-vue'

const emit = defineEmits(['change', 'click'])
// 获取父组件传入的值
const formData = inject(formDataKey) as Ref<any>

interface Props {
  plantName: string | null
}

const props = defineProps<Props>()

const plants = ref<any>({
  title: '农业野生植物资源分布情况',
  data: []
})

const points = ref<any>({
  title: '原生境保护点建设情况',
  centerText: 0,
  data: []
})

const detailInfo = ref()

const selectInfoTitle = computed(() => {
  return props.plantName ? `物种百科信息-${props.plantName}` : '物种百科信息'
})

const oneMapStore = useOneMapStore()
const selectedArea = computed(() => oneMapStore.selectedArea)

type FilterCondition = string | string[]

const generateAreaFilter = (
  selectedArea: { ccode: string; fcode?: string } | undefined
): FilterCondition[] => {
  const filter: FilterCondition[] = []
  if (selectedArea) {
    filter.push(['=', 'ccode', selectedArea.ccode])
    if (selectedArea.fcode) {
      filter.push(['=', 'fcode', selectedArea.fcode])
    }
  }
  return filter
}

const getPlantsData = async () => {
  try {
    const list = formData.value.wild_plant_list.filter((item) => item.sfnybmgl === '是')
    plants.value.data = list.map((item) => {
      return { name: item.wzmc, id: item._id }
    })
  } catch (error) {
    console.error('获取植物数据失败:', error)
  }
}

const getPointsData = async () => {
  try {
    let list = formData.value.protection_point_list
    // 过滤植物名称是否包含在保护点里
    list = list.filter((item) => {
      return item.wzmc.includes(props.plantName)
    })
    // 过滤行政区划
    if (oneMapStore.selectedArea) {
      list = list.filter((item) => {
        return item.ccode === oneMapStore.selectedArea.ccode
      })
    }

    points.value.data = list
  } catch (error) {
    console.error('获取保护点数据失败:', error)
  }
}

const processImageArray = (imageArray: any[]) => {
  if (Array.isArray(imageArray)) {
    return imageArray.map((item: any) => ({
      url: `${window.import_meta.VITE_APP_AXIOS_BASE_URL}/attachment/download/${item.uid}`
    }))
  }
  return imageArray
}

const infoFields = ref([
  { label: '物种名称', field: 'wzmc' },
  { label: '科', field: 'species' },
  { label: '属', field: 'genus' },
  { label: '保护级别', field: 'xbhdj' },
  { label: '识别要点', field: 'sbyd' },
  { label: '生活型', field: 'shx' }
])
const getPlantInfo = async (wzmc) => {
  try {
    const list = formData.value.wild_plant_info.filter((item) => item.wzmc === wzmc)

    let info = null
    if (list.length > 0) {
      const firstItem = { ...list[0] }

      const photoFields = [
        'base_photo',
        'branch_photo',
        'fruit_photo',
        'leaf_photo',
        'plant_photo',
        'root_photo',
        'stem_photo'
      ]

      photoFields.forEach((field) => {
        firstItem[field] = processImageArray(firstItem[field])
      })

      info = firstItem
    }
    return info
  } catch (error) {
    console.error('获取物种百科信息表失败', error)
  }
}
const plantsContainer = ref()
watch(
  () => props.plantName,
  async (newPlant) => {
    await getPlantsData()
    await getPointsData()
    if (!newPlant) {
      detailInfo.value = undefined
      return
    }
    // 滚动到相应的植物
    let info = plants.value.data.find((item) => item.name === newPlant)
    if (info) {
      const element = document.getElementById(info.id)
      if (element) {
        plantsContainer.value.scrollTop = element.offsetTop - 100
      }
    }

    // 获取详情
    detailInfo.value = await getPlantInfo(newPlant)
  },
  {
    immediate: true
  }
)

watch(selectedArea, () => {
  getPlantsData()
  getPointsData()
})

const toPoint = (item) => {
  emit('click', {
    type: 'protect-point',
    data: item
  })
}
const cyclopedia = ref()
async function viewInfo(name) {
  let info = await getPlantInfo(name)
  if (!info) {
    message.warning('暂无该物种的百科信息')
  } else {
    cyclopedia.value = info
    isModalOpen.value = true
  }
}

const isModalOpen = ref(false)
</script>

<template>
  <div>
    <InfoBox :title="plants.title" width="500px" height="245px">
      <div class="text-map-simple-blue text-base">
        <div class="flex justify-between items-center select-none px-2 py-2 bg-[rgba(0,0,0,0.27)]">
          <span class="w-[120px] text-center text-xl">物种名称</span>
          <span class="w-[120px] text-center text-xl">详细</span>
        </div>

        <div
          class="h-[165px] overflow-y-auto"
          style="scroll-behavior: smooth"
          ref="plantsContainer"
        >
          <div
            class="flex justify-between items-center px-2 py-2 text-base text-white"
            v-for="(item, index) in plants.data"
            :id="item.id"
            :key="item.id"
            :class="index % 2 === 0 ? 'bg-transparent' : 'bg-[rgba(0,0,0,0.15)]'"
          >
            <span
              class="w-[120px] text-center"
              :class="{ 'text-map-light-blue': item.name === props.plantName }"
              >{{ item.name }}</span
            >
            <span
              class="text-map-light-blue w-[120px] cursor-pointer text-center"
              @click="viewInfo(item.name)"
            >
              查看百科
            </span>
          </div>
        </div>
      </div>
    </InfoBox>

    <InfoBox :title="points.title" width="500px">
      <div class="text-map-simple-blue text-base">
        <div class="flex justify-between items-center select-none px-2 py-2 bg-[rgba(0,0,0,0.27)]">
          <span>保护区名称</span>
          <span class="text-center pr-[6px]">定位</span>
        </div>
        <div class="max-h-[180px] overflow-y-auto">
          <div
            v-for="(item, index) in points.data"
            :key="item._id"
            :class="index % 2 === 0 ? 'bg-transparent' : 'bg-[rgba(0,0,0,0.15)]'"
          >
            <div
              class="px-2 py-2 hover:text-map-light-blue text-white cursor-pointer flex justify-between items-center"
              @click="toPoint(item)"
            >
              <span class="line-clamp-1">{{ item.bhdmc }}</span>
              <EnvironmentTwoTone class="text-base pr-[10px]" />
            </div>
          </div>
        </div>
      </div>
    </InfoBox>

    <template v-if="props.plantName">
      <InfoBox :title="selectInfoTitle" width="500px">
        <div class="text-map-simple-blue text-base px-1">
          <div class="max-h-[220px] overflow-y-auto">
            <div class="flex items-center justify-between mb-1">
              <div class="flex items-center">
                <span class="inline-block w-[5px] h-[18px] bg-map-light-blue mr-1"></span>
                <span class="text-map-light-blue">基本信息</span>
              </div>
              <div
                class="text-map-simple-blue hover:text-map-light-blue cursor-pointer"
                @click="viewInfo(props.plantName)"
              >
                查看详细
              </div>
            </div>
            <div>
              <p
                v-for="(item, index) in infoFields"
                :key="index"
                class="my-2 flex"
                :class="{ 'mb-0': index === infoFields.length - 1 }"
              >
                <span class="inline-block w-[80px] text-map-simple-blue text-base">
                  {{ item.label }}
                </span>
                <span class="flex-1 line-clamp-1 text-white text-base">
                  {{ detailInfo?.[item.field] || '-' }}
                </span>
              </p>
            </div>
          </div>
        </div>
      </InfoBox>
    </template>
  </div>

  <CustomModal title="物种百科信息" v-model:open="isModalOpen">
    <PlantBaseInfo :plantInfo="cyclopedia" />
  </CustomModal>
</template>

<style lang="less" scoped></style>
