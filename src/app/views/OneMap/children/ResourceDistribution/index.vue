<template>
  <div class="resource-map">
    <!-- 顶部标题栏 -->
    <div v-if="title" class="fixed top-[110px] h-[50px] left-[600px] right-0 flex">
      <div class="flex items-center rounded-lg">
        <div class="flex items-center cursor-pointer" @click="reset">
          <SvgIcon class="ml-4 text-map-light-blue text-title" icon="ArrowLeftOutlined" />
          <div class="text-map-light-blue text-title ml-2">返回</div>
        </div>
        <div class="text-white mx-4">丨</div>
        <div class="text-title text-white" style="text-shadow: 0 0 2 #000">{{ title }}</div>
      </div>
    </div>

    <!-- 左侧控制面板 -->
    <div class="left-container">
      <AreaSelect />
      <PlantSelect :value="plantName" @change="changePlant" />
    </div>

    <!-- 右侧数据面板 -->
    <div class="right-container">
      <ChartData v-if="!plantName" ref="chartDataRef" @change="changeDistribution" />
      <TextData v-else :plantName="plantName" @click="infoClick" />
    </div>

    <!-- 雾化遮罩 -->
    <div ref="fogOverlay" class="fog-overlay"></div>

    <!-- 底部图例控制 -->
    <div class="legend-container">
      <!-- 保护点开关 -->
      <div class="legend-item">
        <img class="w-[30px]" src="@/assets/images/onemap/map-marker.png" />
        <div class="mx-2 text-map-simple-blue text-[16px]">原生境保护点</div>
        <a-switch v-model:checked="pointState.bhd" size="small" />
      </div>

      <!-- 植物数量开关 -->
      <div class="legend-item" v-if="!oneMapStore.selectedArea">
        <div class="legend-icon">
          <div class="w-[10px] h-[10px] bg-[#E1B131]"></div>
        </div>
        <div class="mx-2 text-map-simple-blue text-[16px]">农业重点保护野生植物数量</div>
        <a-switch v-model:checked="pointState.yeszfb" size="small" />
      </div>

      <!-- 植物分布显示 -->
      <div class="legend-item" v-if="plantName">
        <div class="legend-icon">
          <div class="w-[10px] h-[10px] bg-map-light-blue"></div>
        </div>
        <div class="mx-2 text-map-simple-blue text-[16px]">{{ plantName }}植物分布</div>
      </div>

      <!-- 图表图例 -->
      <div class="chart-legend" v-if="title && chartDataLegend.length > 0">
        <div class="text-map-simple-blue text-[16px] ml-[30px] mb-2">农业重点保护野生植物数量</div>
        <div class="legend-item" v-for="(item, index) in chartDataLegend" :key="index">
          <div class="legend-color" :style="{ 'background-color': item.color }"></div>
          <div class="ml-4 text-map-simple-blue text-[16px]">
            <template v-if="chartDataLegend.length === 1">
              {{ item.min }}
            </template>
            <template v-else> {{ item.min }}~{{ item.max }} </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'ResourceDistribution' })

// 组件导入
import ChartData from './components/ChartData/index.vue'
import TextData from './components/TextData/index.vue'
import AreaSelect from '../../components/AreaSelect/index.vue'
import PlantSelect from '../../components/PlantSelect/index.vue'

// 工具和配置导入
import { useOneMapStore } from '@/stores'
import {
  baseCenter,
  baseLayerSwitch,
  basePitch,
  baseZoom,
  initSceneLayerState,
  pageLayerSwitch
} from '@/app/views/OneMap/hooks/useMapLayers'
import { sceneKey, mapKey, mapRefKey, formDataKey } from '@/app/views/OneMap/config'

// 依赖注入
const scene: any = inject(sceneKey)
const mapbox: any = inject(mapKey)
const mapRef: any = inject(mapRefKey)
const formData: any = inject(formDataKey)

// 状态管理
const oneMapStore = useOneMapStore()
const chartDataRef = ref<InstanceType<typeof ChartData> | null>(null)
const fogOverlay = ref()

// 计算属性
const chartDataLegend = computed(() => {
  return chartDataRef.value?.intervalsLegend || []
})

const title = computed(() => {
  if (!stateTitle.value) return false
  console.log(stateTitle.value, 'stateTitle')

  let area = '湖北省'
  if (oneMapStore.selectedArea) {
    area = oneMapStore.selectedArea.fname || oneMapStore.selectedArea.cname
  }
  return `${area}${stateTitle.value}分布图`
})

// 响应式数据
const pointState = reactive({
  bhd: true,
  yeszfb: true
})

const stateTitle = ref()
const plantName = ref<string | null>(null)
const pointList = ref<any[]>([])
const distributionList = ref<any[]>([])

let state: string | null = null

// 重置功能
const reset = () => {
  oneMapStore.clearSelectedArea()
  chartDataRef.value?.reset()
  changePlant(null)
  stateTitle.value = ''
}

// 生命周期
onMounted(() => {
  getFormData()
  addPointEvent()
  layerEvent()
  if (oneMapStore.pageCurrent === 0) pageLayerSwitch(scene.value, 'resource')
})

// 页面状态初始化
function initPageState() {
  console.log('==========初始化资源分布图层==========')
  const squareLayer = scene.value.getLayerByName('resource-plant-type')
  const bhdLayer = scene.value.getLayerByName('resource-bhd-point')
  const pageState = oneMapStore.pageState

  // 恢复图层显示状态
  pageLayerSwitch(scene.value, 'resource')
  if (pageState) {
    // 恢复状态选择
    if (pageState.state) {
      if (pageState.state === 'distribute') {
        chartDataRef.value?.pieClick({}, 1)
      } else if (pageState.state === 'plant') {
        changePlant(pageState.plantName)
      }
    }

    // 恢复行政区选择
    if (pageState.ccode) {
      const city = oneMapStore.getCityInfo(pageState.ccode)
      if (oneMapStore.selectedArea?.ccode !== city.ccode) {
        oneMapStore.setAreaByCode(city.ccode)
      } else {
        changeByArea(city)
      }
    }

    // 检测图例控制状态
    if (pageState.pointState) {
      if (!pageState.pointState.bhd) bhdLayer.hide()
      if (!pageState.pointState.yeszfb) squareLayer.hide()
    }
  } else {
    // 重置到默认状态
    resetToDefaultState(squareLayer, bhdLayer)
  }
}

// 重置到默认状态
function resetToDefaultState(squareLayer: any, bhdLayer: any) {
  mapRef.value.setThemeLayer()

  // 重置控制状态
  pointState.bhd = true
  pointState.yeszfb = true
  plantName.value = null
  stateTitle.value = null

  // 重置图层过滤
  bhdLayer.filter(true)
  squareLayer.filter(true)
}

// 图层事件绑定
function layerEvent() {
  const baseLayer = scene.value.getLayerByName('base-layer')

  baseLayer.on('click', (e: any) => {
    if (oneMapStore.pageCurrent !== 0) return
    if (oneMapStore.selectedArea) return

    const city = e.feature.properties
    oneMapStore.setAreaByCode(city.ccode)
  })
}

// 数据变化处理
const changeDistribution = (title: string) => {
  state = 'distribute'
  stateTitle.value = title
}

function infoClick(data: any) {
  if (data.type === 'protect-point') {
    protectPointClick(data.data.bhdbm)
  }
}

// 监听器
watch(
  () => pointState.bhd,
  (val) => {
    const layer = scene.value.getLayerByName('resource-bhd-point')
    if (val) layer.show()
    else layer.hide()
  }
)

watch(
  () => pointState.yeszfb,
  (val) => {
    const layer = scene.value.getLayerByName('resource-plant-type')
    if (val) layer.show()
    else layer.hide()
  }
)

watch(
  () => oneMapStore.selectedArea,
  (val) => {
    changeByArea(val)
  }
)

watch(
  () => oneMapStore.pageCurrent,
  (val) => {
    if (val === 0) initPageState()
  }
)

// 植物选择处理
const changePlant = (value: string | null) => {
  const { ccode } = oneMapStore.selectedArea || {}

  // 更新标题状态
  updatePlantTitle(value)

  plantName.value = value

  // 过滤保护点图层
  filterProtectionPoints(value, ccode)

  // 更新分布区域颜色
  updateDistributionColors(value, ccode)

  state = value ? 'plant' : null
  scene.value.render()
}

// 更新植物标题
function updatePlantTitle(value: string | null) {
  if (stateTitle.value === `${plantName.value}资源` && !value) {
    stateTitle.value = null
  }
  if (value) {
    stateTitle.value = `${value}资源`
  }
}

// 过滤保护点
function filterProtectionPoints(value: string | null, ccode: string) {
  const pointLayer = scene.value.getLayerByName('resource-bhd-point')
  if (pointLayer) {
    pointLayer.filter('wzmc*ccode', (wzmc: any, code: string) => {
      const wzmcState = !value || !Array.isArray(wzmc) || wzmc.includes(value)
      const cityState = !oneMapStore.selectedArea || code === ccode
      return wzmcState && cityState
    })
    chartDataRef.value?.setCityCylinder(value)
  }
}

// 更新分布区域颜色
function updateDistributionColors(value: string | null, ccode: string) {
  const baseLayer = scene.value.getLayerByName('base-layer')
  let plants = distributionList.value.filter((item) => item.wzmc === value)

  if (oneMapStore.selectedArea) {
    plants = plants.filter((item) => item.ccode === ccode)
  }

  baseLayer.color('code', (code: string) => {
    const info = plants.find((item) => item.fcode === code)
    return info ? 'rgba(0, 255, 255, 0.4)' : 'rgba(0,0,0,0)'
  })
}

// 数据获取
const getFormData = async () => {
  pointList.value = formData.value.protection_point_list
  distributionList.value = formData.value.plant_distribution_list
}

// 保护点事件绑定
const addPointEvent = () => {
  const layer = scene.value.getLayerByName('resource-bhd-point')
  layer.on('click', (e: any) => {
    protectPointClick(e.feature.bhdbm)
  })
}

// 保护点点击处理
function protectPointClick(number: string) {
  oneMapStore.setProtectPointId(number)

  // 保存当前状态
  oneMapStore.pageStatePush({
    page: 0,
    ccode: oneMapStore.selectedArea?.ccode,
    plantName: plantName.value,
    state: state,
    pointState: pointState
  })

  oneMapStore.setPageCurrent(1)
}

// 区域变化处理
function changeByArea(val: any) {
  if (val) {
    // 缩放到选中区域
    zoomToArea(val)
    // 调整图层显示
    adjustLayersForArea(val)
  } else {
    // 重置到默认视图
    resetToDefaultView()
  }

  changePlant(plantName.value)

  scene.value.render()
}

// 缩放到指定区域
function zoomToArea(area: any) {
  mapbox.value.fitBounds(
    [
      [area.x_min - 0.3, area.y_min - 0.3],
      [area.x_max + 0.3, area.y_max + 0.3]
    ],
    { pitch: 20 }
  )
}

// 调整区域图层显示
function adjustLayersForArea(area: any) {
  const squareLayer = scene.value.getLayerByName('resource-plant-type')
  const countyLineLayer = scene.value.getLayerByName('base-county-line')
  const cityLineLayer = scene.value.getLayerByName('base-city-line')
  const cityLabelLayer = scene.value.getLayerByName('base-city-label')
  const countyLabelLayer = scene.value.getLayerByName('base-county-label')
  const boundaryLayer = scene.value.getLayerByName('base-boundary')

  // 隐藏方块图层和市级图层
  squareLayer.hide()
  cityLineLayer.hide()
  cityLabelLayer.hide()

  // 显示和配置县级图层
  countyLineLayer.size(2).color('#fff')
  countyLabelLayer.show()

  // 过滤显示当前区域
  countyLineLayer.color('ccode', (code: string) => {
    return code === area.ccode ? '#E5FFFF' : 'rgba(0,0,0,0)'
  })

  countyLabelLayer.filter('ccode', (code: string) => code === area.ccode)
  boundaryLayer.filter('ccode', (code: string) => code === area.ccode)
}

// 重置到默认视图
function resetToDefaultView() {
  mapbox.value.setPitch(basePitch)
  mapbox.value.flyTo({
    center: baseCenter,
    zoom: baseZoom
  })
  // scene场景初始状态
  initSceneLayerState(scene.value)
  // 显示资源分布模块点位
  pageLayerSwitch(scene.value, 'resource')
}
</script>

<style lang="less" scoped>
/* 布局容器 */
.left-container {
  position: fixed;
  top: 120px;
  left: 40px;
  z-index: 999;
  width: 330px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.right-container {
  position: fixed;
  top: 120px;
  right: 40px;
  z-index: 999;
}

/* 图例控制区域 */
.legend-container {
  position: absolute;
  bottom: 40px;
  left: 40px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.legend-icon {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.legend-color {
  width: 14px;
  height: 8px;
  background-color: var(--primary-color);
}

.chart-legend {
  margin-top: 8px;
  padding-left: 10px;
}

/* 雾化遮罩效果 */
.fog-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.7) 30%,
    rgba(255, 255, 255, 0.9) 70%
  );
  opacity: 0;
  pointer-events: none;
  transition: opacity 1.5s ease;
  z-index: 999;
}

.fog-overlay.active {
  opacity: 1;
  pointer-events: auto;
}
</style>
