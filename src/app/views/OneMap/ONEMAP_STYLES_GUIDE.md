# OneMap 样式作用域限制指南

## 概述

为了确保 `src/assets/styles/onemap.less` 中的样式只影响 OneMap 相关页面，而不影响项目中的其他页面，我们采用了 CSS 作用域前缀的方案。

## 实现方案

### 1. 样式文件修改

`src/assets/styles/onemap.less` 文件已被重构，所有样式都被包裹在 `.onemap-container` 作用域内：

```less
.onemap-container {
  // Select 组件样式
  .ant-select-dropdown {
    width: 330px !important;
    background-color: #00475e !important;
    // ... 其他样式
  }
  
  // Modal 组件样式
  .ant-modal-content {
    background-color: rgba(0, 50, 67, 0.8) !important;
    // ... 其他样式
  }
}

// 全局 Modal 样式（针对通过 getContainer 挂载到 body 的 Modal）
body .onemap-modal {
  .ant-modal-content {
    // ... Modal 样式
  }
}
```

### 2. 组件修改

#### OneMap 主组件 (`src/app/views/OneMap/index.vue`)
- 在根容器添加了 `onemap-container` 类名
- 确保所有子组件都在此作用域内

```vue
<template>
  <div class="page-container onemap-container">
    <!-- 组件内容 -->
  </div>
</template>
```

#### CustomModal 组件 (`src/app/views/OneMap/components/CustomModal/index.vue`)
- 添加了 `wrapClassName="onemap-modal"` 属性
- 确保 Modal 样式正确应用

```vue
<template>
  <a-modal
    wrapClassName="onemap-modal"
    <!-- 其他属性 -->
  >
    <!-- Modal 内容 -->
  </a-modal>
</template>
```

## 样式作用域说明

### 1. 容器内样式 (`.onemap-container`)
- 适用于 Select 组件等在容器内渲染的组件
- 通过 CSS 后代选择器确保样式只在 OneMap 页面生效

### 2. 全局 Modal 样式 (`.onemap-modal`)
- 适用于 Modal 组件（通常挂载到 body）
- 使用 `wrapClassName` 属性为 Modal 添加特定类名
- 确保 Modal 样式不影响其他页面的 Modal

## 使用指南

### 在 OneMap 子组件中使用样式化组件

1. **Select 组件**：直接使用，样式会自动应用
```vue
<a-select>
  <a-select-option value="option1">选项1</a-select-option>
</a-select>
```

2. **Modal 组件**：使用 CustomModal 组件或添加 wrapClassName
```vue
<!-- 使用 CustomModal -->
<CustomModal :open="visible" title="标题">
  <!-- 内容 -->
</CustomModal>

<!-- 或直接使用 a-modal -->
<a-modal wrapClassName="onemap-modal">
  <!-- 内容 -->
</a-modal>
```

### 添加新的样式化组件

如果需要为其他 Ant Design 组件添加 OneMap 专用样式：

1. 在 `onemap.less` 的 `.onemap-container` 内添加样式
2. 如果是全局挂载的组件（如 Drawer、Notification），在 `.onemap-modal` 样式块中添加

## 注意事项

1. **样式优先级**：使用了 `!important` 确保样式优先级
2. **作用域限制**：所有样式都限制在 OneMap 页面内
3. **全局组件**：Modal、Drawer 等全局组件需要特殊处理
4. **维护性**：新增样式时请遵循相同的作用域规则

## 验证方法

1. 在 OneMap 页面中，相关组件应显示自定义样式
2. 在其他页面中，相同组件应显示默认样式
3. 检查浏览器开发者工具，确认样式选择器正确应用
