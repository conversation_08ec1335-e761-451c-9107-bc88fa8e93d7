const registerComponents = {}
// components自动加载组件
const components = import.meta.globEager([
  '@/app/components/**/*Field/index.vue',
  '@/app/components/**/*Form/index.vue'
])
// console.log('object :>> ', components)
for (const fileName in components) {
  const comp = components[fileName] as unknown as { default: '' }
  const a = fileName.split('/')
  registerComponents[a[a.length - 2]] = comp.default
}
// console.log('registerComponents :>> ', registerComponents)
const MIS_COMPONENTS: any = {
  install(App) {
    Object.keys(registerComponents).forEach((key) => {
      App.component(key, registerComponents[key])
    })
  }
}
export default MIS_COMPONENTS
