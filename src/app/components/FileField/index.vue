<template>
  <GTField :label="label" :field="field" :required="required" :hide="hide">
    <a-upload
      :class="{ 'hide-button': fileList.length >= max }"
      name="files"
      :action="uploadUrl"
      :headers="headers"
      @change="changeHandler"
      :accept="accept"
      :multiple="true"
      :fileList="fileList"
      :beforeUpload="beforeUploadHandler"
      :disabled="readonly"
      listType="picture"
    >
      <a-button :disabled="readonly">
        <template #icon>
          <SvgIcon icon="UploadOutlined" />
        </template>
        上传文件
      </a-button>
      <span class="accept-desc" v-if="accept">支持的格式：{{ accept }}</span>
    </a-upload>
  </GTField>
</template>

<script lang="ts" setup>
defineOptions({ name: 'FileField' })
import { BaseFields } from '@gt/mis-components-web'
import WORD from './images/file_thumb_word.png'
import EXCEL from './images/file_thumb_excel.png'
import PDF from './images/file_thumb_pdf.png'
import DEFAULT from './images/file_thumb_default.png'
import { cloneDeep } from 'lodash'
import { message } from 'ant-design-vue'
import SvgIcon from '@gt/mis-components-web'
const isValidate = inject('isValidate', ref({ state: true, message: '' }))
interface addOptions extends FORM.SON_OPTIONS {
  max: number
  sizeLimit?: number
  suffixes?: []
}
interface Props {
  type: string
  options: addOptions
  formData: object
}
enum FILE_STATUS {
  UPLOADING = 'uploading',
  DONE = 'done',
  ERROR = 'error',
  REMOVED = 'removed'
}

const DOWNLOAD_URL = `${window.import_meta.VITE_APP_AXIOS_BASE_URL}/attachment/download`
const FILE_THUMB_URL = {
  WORD,
  EXCEL,
  PDF,
  DEFAULT
}
// 获取父子间传值
const props = defineProps<Props>()
// 获取options配置参数
const { label, field, required, readonly, hide, eventBus, formRecordData } = BaseFields(props)
const max = props.options.max || 1
const sizeLimit = computed(() => {
  const { sizeLimit } = props.options
  return !isNaN(sizeLimit as any) && sizeLimit ? sizeLimit : 0
})
const accept = computed(() => {
  const { suffixes } = props.options
  return suffixes ? suffixes.join(',') : ''
})
const uploadUrl = `${window.import_meta.VITE_APP_AXIOS_BASE_URL}/attachment/upload`
const headers = {
  Authorization: localStorage.getItem('access_token')
}
const fileList = ref<any[]>([])

const setValue = (value) => {
  let v = value || []
  v = cloneDeep(v)
  let formValue: any = null
  if (v.length !== 0) {
    formValue = []
    v.forEach((file) => {
      file.url = `${DOWNLOAD_URL}/${file.uid}`
      file.thumbUrl = getThumbnailUrl(file)
      formValue.push({
        name: file.name,
        uid: file.uid
      })
    })
  }
  fileList.value = v
  eventBus.emit('change', { field, value: formValue })
}
function changeHandler(info) {
  // 必须加下面这一行代码，否则只能监听到uploading事件
  //   console.log('info :>> ', info)
  // fileList.value = info.fileList
  const fileStatus = info.file.status
  // console.log('fileStatus :>> ', fileStatus)
  // console.log('info :>> ', info)
  switch (fileStatus) {
    case FILE_STATUS.UPLOADING:
      fileList.value = info.fileList
      isValidate.value = { state: false, message: '请等待文件上传完成后提交数据...' }
      // 避免uploading过程中提示文件为空
      //   this.formData[this.field] = '__temp_file_uid__'
      break
    case FILE_STATUS.DONE: {
      // 上传成功要根据API返回内容判断是否真的成功
      const { code } = info.file.response
      if (code === 2000) {
        // 添加文件uid
        info.file.uid = info.file.response.data[0]
        info.file.url = `${DOWNLOAD_URL}/${info.file.uid}`
        info.file.thumbUrl = getThumbnailUrl(info.file)
        const index = fileList.value.length !== 0 ? fileList.value.length - 1 : 0
        fileList.value[index] = info.file
      } else {
        // 删除上传失败的文件
        // delete this.fileList[this.fileList.length - 1]
        fileList.value.pop()
        message.error('文件上传失败，请重试')
      }
      setValue(fileList.value)
      isValidate.value = { state: true, message: '' }
      break
    }
    case FILE_STATUS.ERROR:
      // 删除上传失败的文件
      fileList.value.pop()
      setValue(fileList.value)
      message.error('文件上传失败，请重试')
      isValidate.value = { state: true, message: '' }
      break
    case FILE_STATUS.REMOVED:
      fileList.value = info.fileList
      setValue(fileList.value)
      isValidate.value = { state: true, message: '' }
      // FileAPI.del(info.file.uid)
      break
    default:
      isValidate.value = { state: true, message: '' }
  }
}
function beforeUploadHandler(file) {
  if (!file || !file.name) return Promise.reject()
  if (exceedSizeLimit(file.size)) {
    message.error(`文件大小不能超过${sizeLimit.value}KB`)
    return Promise.reject()
  }
  if (!testFormat(file.name)) {
    message.warning(`只能上传${accept.value}的文件`)
    return Promise.reject()
  }
  if (!max) return true
  if (fileList.value.length >= max) {
    message.error(`您最多只能上传${max}个${label.value}，请删除后再继续上传`)
    return Promise.reject()
  }
}
// 检测图片类型
function testFormat(name) {
  const { suffixes } = props.options
  if (!suffixes) return true
  name = name.toLocaleLowerCase()
  return suffixes.some((suffix) => name.endsWith(suffix))
}
// size单位byte
function exceedSizeLimit(size) {
  if (!sizeLimit.value) return false
  const fileSize = size / 1024
  if (fileSize > sizeLimit.value) return true
}
function getThumbnailUrl({ uid, name }) {
  if (!uid || !name) return FILE_THUMB_URL.DEFAULT
  const suffix = name.split('.').pop().toLowerCase()
  if (!suffix) return FILE_THUMB_URL.DEFAULT
  // WORD
  if (['doc', 'docx'].indexOf(suffix) >= 0) {
    return FILE_THUMB_URL.WORD
  }
  // EXCEL
  if (['xls', 'xlsx'].indexOf(suffix) >= 0) {
    return FILE_THUMB_URL.EXCEL
  }
  // PDF
  if ('pdf' === suffix) {
    return FILE_THUMB_URL.PDF
  }
  // Picture
  if (['png', 'jpeg', 'jpg'].indexOf(suffix) >= 0) {
    return `${DOWNLOAD_URL}/${uid}`
  }
  return FILE_THUMB_URL.DEFAULT
}
onMounted(() => {
  if (formRecordData[field]) setValue(formRecordData[field])
})
</script>

<style lang="less" scoped>
:deep(.ant-upload-list-item-card-actions .anticon) {
  color: #e91e63;
  padding-right: 8px;
}

.accept-desc {
  padding-left: 12px;
  color: rgba(0, 0, 0, 0.4);
}
.hide-button {
  :deep(.ant-upload-select) {
    display: none;
  }
}
</style>
