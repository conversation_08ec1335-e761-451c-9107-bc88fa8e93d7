<template>
  <button
    class="fullscreen-btn"
    @click="toggleFullscreen"
    :style="
      {
        '--btn-bg-color': props.backgroundColor,
        '--btn-bg-color-hover': props.backgroundColorHover,
        position: props.position,
        top: props.top + 'px',
        right: props.right + 'px'
      } as any
    "
  >
    <FullscreenOutlined
      v-show="!isFullscreen"
      :style="{ color: props.iconColor, fontSize: props.iconSize + 'px' }"
      class="fullscreen-icon"
    />
    <FullscreenExitOutlined
      v-show="isFullscreen"
      :style="{ color: props.iconColor, fontSize: props.iconSize + 'px' }"
      class="fullscreen-icon"
    />
  </button>
</template>

<script setup lang="ts">
import { FullscreenOutlined, FullscreenExitOutlined } from '@ant-design/icons-vue'

interface Props {
  iconColor?: string
  iconSize?: string
  backgroundColor?: string
  backgroundColorHover?: string
  position?: string
  top?: string
  right?: string
}

const props = withDefaults(defineProps<Props>(), {
  iconColor: '#166cff',
  iconSize: '16',
  backgroundColor: '#eee',
  backgroundColorHover: '#166cff',
  position: 'fixed',
  top: '20',
  right: '20'
})

declare global {
  interface Document {
    webkitExitFullscreen?: () => Promise<void>
    mozCancelFullScreen?: () => Promise<void>
    msExitFullscreen?: () => Promise<void>
    webkitFullscreenElement?: Element
    mozFullScreenElement?: Element
    msFullscreenElement?: Element
  }
}

// 全屏状态
const isFullscreen = ref(false)

// 切换全屏函数
const toggleFullscreen = () => {
  const docElm = document.documentElement
  if (isFullscreen.value) {
    exitFullscreen()
  } else {
    enterFullscreen(docElm)
  }
}

// 进入全屏
const enterFullscreen = (docElm: any) => {
  if (docElm.requestFullscreen) {
    docElm.requestFullscreen()
  } else if (docElm.webkitRequestFullscreen) {
    docElm.webkitRequestFullscreen()
  } else if (docElm.mozRequestFullScreen) {
    docElm.mozRequestFullScreen()
  } else if (docElm.msRequestFullscreen) {
    docElm.msRequestFullscreen()
  }
  isFullscreen.value = true
}

// 退出全屏
const exitFullscreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen()
  } else if (document.webkitExitFullscreen) {
    document.webkitExitFullscreen()
  } else if (document.mozCancelFullScreen) {
    document.mozCancelFullScreen()
  } else if (document.msExitFullscreen) {
    document.msExitFullscreen()
  }
  isFullscreen.value = false
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!(
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.mozFullScreenElement ||
    document.msFullscreenElement
  )
}

// 添加事件监听
onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.addEventListener('mozfullscreenchange', handleFullscreenChange)
  document.addEventListener('MSFullscreenChange', handleFullscreenChange)
})

// 移除事件监听
onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
})
</script>

<style scoped>
.fullscreen-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: var(--btn-bg-color);
  padding: 16px;
  cursor: pointer;
  z-index: 1000;
  transition: background-color 0.3s ease;
}

.fullscreen-btn:hover {
  background-color: var(--btn-bg-color-hover);
}

.fullscreen-icon {
  transition: transform 0.3s ease;
}

.fullscreen-btn:hover .fullscreen-icon {
  transform: scale(1.2);
}
</style>
