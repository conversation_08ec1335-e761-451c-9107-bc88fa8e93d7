export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx,vue}'],
  theme: {
    colors: {
      white: '#fff',
      map: {
        'light-blue': '#15FFFC',
        'dark-blue': '#00475E',
        'light-green': '#00FF84',
        'simple-blue': '#A6C1D3'
      }
    },
    fontSize: {
      sm: '14px', // 小字体
      base: '18px', // 基本
      xl: '20px', // 重点
      number: '32px', // 数字
      title: '24px' // 标题
    },
    extend: {}
  },
  plugins: []
}
